<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="750" height="670" style="" viewBox="0 0 750 670"><rect x="0" y="0" width="750" height="670" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-e017ec"><g class="clips"><clipPath id="clipe017ecxyplot" class="plotclip"><rect width="625" height="240.24626865671644"/></clipPath><clipPath id="clipe017ecx2y2plot" class="plotclip"><rect width="625" height="240.24626865671644"/></clipPath><clipPath class="axesclip" id="clipe017ecx"><rect x="95" y="0" width="625" height="670"/></clipPath><clipPath class="axesclip" id="clipe017ecy"><rect x="0" y="91" width="750" height="240.24626865671644"/></clipPath><clipPath class="axesclip" id="clipe017ecxy"><rect x="95" y="91" width="625" height="240.24626865671644"/></clipPath><clipPath class="axesclip" id="clipe017ecy2"><rect x="0" y="361.75373134328356" width="750" height="240.24626865671644"/></clipPath><clipPath class="axesclip" id="clipe017ecxy2"><rect x="95" y="361.75373134328356" width="625" height="240.24626865671644"/></clipPath><clipPath class="axesclip" id="clipe017ecx2"><rect x="95" y="0" width="625" height="670"/></clipPath><clipPath class="axesclip" id="clipe017ecx2y"><rect x="95" y="91" width="625" height="240.24626865671644"/></clipPath><clipPath class="axesclip" id="clipe017ecx2y2"><rect x="95" y="361.75373134328356" width="625" height="240.24626865671644"/></clipPath></g><g class="gradients"/><g class="patterns"/></defs><g class="bglayer"><rect class="bg" x="95" y="91" width="625" height="240.24626865671644" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/><rect class="bg" x="95" y="361.75373134328356" width="625" height="240.24626865671644" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(152.95,0)" d="M0,91v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(231.07,0)" d="M0,91v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(309.2,0)" d="M0,91v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(387.32,0)" d="M0,91v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(465.45,0)" d="M0,91v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(543.5799999999999,0)" d="M0,91v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(621.7,0)" d="M0,91v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(699.83,0)" d="M0,91v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,319.28)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,276.34000000000003)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,233.4)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,190.45)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,147.51)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,104.57)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"/><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(95,91)" clip-path="url(#clipe017ecxyplot)"><g class="scatterlayer mlayer"><g class="trace scatter trace137c1cbb-98c2-4815-b0db-bc9a337a7dc2" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M35.62,147.86L41.21,24.15L46.79,71.11L52.37,99.74L57.95,194.78L63.53,194.79L69.11,215.81L74.69,42.3L80.27,99.22L85.85,76.25L91.43,223.86L97.01,20.03L102.59,49.55L108.17,182.69L113.75,189.24L119.33,188.9L124.91,162.96L130.49,115.61L136.07,135.54L141.65,165.75L147.23,96.91L152.81,198.33L158.39,165.55L163.97,149.62L169.55,130.36L175.14,59.7L180.72,185.41L186.3,117.87L191.88,101.08L197.46,218.31L203.04,97.83L208.62,191.67L214.2,214.31L219.78,24.55L225.36,20.95L230.94,54.71L236.52,162.88L242.1,207.31L247.68,81.37L253.26,133.77L258.84,202.08L264.42,121.96L270,220.9L275.58,33.04L281.16,172.72L286.74,86.03L292.32,161.35L297.9,116.62L303.48,110.9L309.06,188.59L314.65,20.1L320.23,61.85L325.81,26.56L331.39,36.15L336.97,99.91L342.55,30.35L348.13,209.28L353.71,186.2L359.29,218.57L364.87,158.43L370.45,144.83L376.03,170.02L381.61,50.34L387.19,151.68L392.77,167.96L398.35,111.76L403.93,198.02L409.51,56.04L415.09,212.27L420.67,16.39L426.25,62.47L431.83,185.61L437.41,227.09L442.99,53.19L448.58,76.51L454.16,71.76L459.74,62.68L465.32,212.38L470.9,151.31L476.48,203.4L482.06,42.96L487.64,94.45L493.22,157.23L498.8,214.63L504.38,161.51L509.96,158.46L515.54,71.63L521.12,91.39L526.7,37.79L532.28,126.89L537.86,202.6L543.44,75.14L549.02,64.93L554.6,107.77L560.18,62.75L565.76,122.26L571.34,116.04L576.92,136.48L582.5,222.82L588.09,205.11" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter tracea1086b9a-6114-4c66-88bc-8e24aea5fee4" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(35.62,147.86)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,50.11)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,50.11)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,187.63)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,138.72)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,138.72)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,138.72)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,146.54)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,154.42)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,154.42)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,154.42)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,154.42)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,154.42)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,140.24)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,138.61)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,138.61)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,138.61)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,138.61)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,138.61)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,97)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,146.39)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,143.73)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,143.73)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,143.73)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,143.34)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,146.03)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(41.21,146.03)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,145.07)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,145.07)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,95.13)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,95.13)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,162.44)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,163.76)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,150.36)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,150.36)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,133.83)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,133.83)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,133.83)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,134.41)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,134.41)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,133.85)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,133.65)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,133.65)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,124.98)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,124.98)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,123.92)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,129.51)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,129.51)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace07e2e91e-d7d6-49a5-848a-5bcdf8d11c68" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(41.21,24.15)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(52.37,99.74)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(57.95,194.78)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(91.43,223.86)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(113.75,189.24)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(141.65,165.75)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(152.81,198.33)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(180.72,185.41)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(197.46,218.31)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(208.62,191.67)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(214.2,214.31)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(258.84,202.08)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(270,220.9)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(314.65,20.1)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(320.23,61.85)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(331.39,36.15)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(381.61,50.34)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(420.67,16.39)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(487.64,94.45)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(515.54,71.63)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(526.7,37.79)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(543.44,75.14)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(554.6,107.77)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(560.18,62.75)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceab3fc9dd-0ca9-4f67-9daa-e9a9b7740fe9" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(74.69,42.3)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(97.01,20.03)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(102.59,49.55)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(130.49,115.61)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(136.07,135.54)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(147.23,96.91)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(191.88,101.08)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(203.04,97.83)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(230.94,54.71)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(247.68,81.37)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(264.42,121.96)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(286.74,86.03)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(297.9,116.62)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,110.9)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(336.97,99.91)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(364.87,158.43)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(415.09,212.27)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(493.22,157.23)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(498.8,214.63)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(509.96,158.46)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(537.86,202.6)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(576.92,136.48)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(588.09,205.11)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace96afee41-bce6-4c03-98b1-285e8d807998" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(588.09,205.11)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(255, 170, 0); fill-opacity: 1; stroke: rgb(178, 118, 0); stroke-opacity: 1;"/></g><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"/><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,319.28)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">1</text></g><g class="ytick"><text text-anchor="end" x="94" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,276.34000000000003)">1.2</text></g><g class="ytick"><text text-anchor="end" x="94" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,233.4)">1.4</text></g><g class="ytick"><text text-anchor="end" x="94" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,190.45)">1.6</text></g><g class="ytick"><text text-anchor="end" x="94" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,147.51)">1.8</text></g><g class="ytick"><text text-anchor="end" x="94" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,104.57)">2</text></g></g><g class="overaxes-above"/></g><g class="subplot x2y2"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x2"><path class="x2grid crisp" transform="translate(152.95,0)" d="M0,361.75373134328356v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(231.07,0)" d="M0,361.75373134328356v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(309.2,0)" d="M0,361.75373134328356v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(387.32,0)" d="M0,361.75373134328356v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(465.45,0)" d="M0,361.75373134328356v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(543.5799999999999,0)" d="M0,361.75373134328356v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(621.7,0)" d="M0,361.75373134328356v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(699.83,0)" d="M0,361.75373134328356v240.24626865671644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y2"><path class="y2grid crisp" transform="translate(0,582.2537313432836)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y2grid crisp" transform="translate(0,543.3537313432836)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y2grid crisp" transform="translate(0,504.4637313432836)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y2grid crisp" transform="translate(0,426.6737313432836)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y2grid crisp" transform="translate(0,387.78373134328353)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="y2zl zl crisp" transform="translate(0,465.57373134328355)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(95,361.75373134328356)" clip-path="url(#clipe017ecx2y2plot)"><g class="scatterlayer mlayer"><g class="trace scatter tracec716b840-97a4-47d5-ab1c-ea3fd2b353c3" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(41.21,27.01)" d="M5.81,0A5.81,5.81 0 1,1 0,-5.81A5.81,5.81 0 0,1 5.81,0Z" style="opacity: 0.849112; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(52.37,82.89)" d="M4.1,0A4.1,4.1 0 1,1 0,-4.1A4.1,4.1 0 0,1 4.1,0Z" style="opacity: 0.775916; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(57.95,35.38)" d="M5.56,0A5.56,5.56 0 1,1 0,-5.56A5.56,5.56 0 0,1 5.56,0Z" style="opacity: 0.838144; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(91.43,52.75)" d="M5.03,0A5.03,5.03 0 1,1 0,-5.03A5.03,5.03 0 0,1 5.03,0Z" style="opacity: 0.815399; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(113.75,79.42)" d="M4.21,0A4.21,4.21 0 1,1 0,-4.21A4.21,4.21 0 0,1 4.21,0Z" style="opacity: 0.780471; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(141.65,100)" d="M3.58,0A3.58,3.58 0 1,1 0,-3.58A3.58,3.58 0 0,1 3.58,0Z" style="opacity: 0.753515; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(152.81,77.82)" d="M4.26,0A4.26,4.26 0 1,1 0,-4.26A4.26,4.26 0 0,1 4.26,0Z" style="opacity: 0.782558; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(180.72,78.4)" d="M4.24,0A4.24,4.24 0 1,1 0,-4.24A4.24,4.24 0 0,1 4.24,0Z" style="opacity: 0.7818; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(197.46,56.28)" d="M4.92,0A4.92,4.92 0 1,1 0,-4.92A4.92,4.92 0 0,1 4.92,0Z" style="opacity: 0.810775; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(208.62,73.47)" d="M4.39,0A4.39,4.39 0 1,1 0,-4.39A4.39,4.39 0 0,1 4.39,0Z" style="opacity: 0.788261; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(214.2,58.86)" d="M4.84,0A4.84,4.84 0 1,1 0,-4.84A4.84,4.84 0 0,1 4.84,0Z" style="opacity: 0.807399; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(258.84,69.41)" d="M4.52,0A4.52,4.52 0 1,1 0,-4.52A4.52,4.52 0 0,1 4.52,0Z" style="opacity: 0.793571; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(270,57.06)" d="M4.89,0A4.89,4.89 0 1,1 0,-4.89A4.89,4.89 0 0,1 4.89,0Z" style="opacity: 0.809748; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(314.65,26.95)" d="M5.81,0A5.81,5.81 0 1,1 0,-5.81A5.81,5.81 0 0,1 5.81,0Z" style="opacity: 0.849188; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(320.23,53.93)" d="M4.99,0A4.99,4.99 0 1,1 0,-4.99A4.99,4.99 0 0,1 4.99,0Z" style="opacity: 0.813852; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(331.39,75.07)" d="M4.34,0A4.34,4.34 0 1,1 0,-4.34A4.34,4.34 0 0,1 4.34,0Z" style="opacity: 0.786167; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(381.61,29.51)" d="M5.74,0A5.74,5.74 0 1,1 0,-5.74A5.74,5.74 0 0,1 5.74,0Z" style="opacity: 0.845834; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(420.67,19.56)" d="M6.04,0A6.04,6.04 0 1,1 0,-6.04A6.04,6.04 0 0,1 6.04,0Z" style="opacity: 0.858861; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(487.64,83.19)" d="M4.1,0A4.1,4.1 0 1,1 0,-4.1A4.1,4.1 0 0,1 4.1,0Z" style="opacity: 0.775534; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(515.54,68.54)" d="M4.54,0A4.54,4.54 0 1,1 0,-4.54A4.54,4.54 0 0,1 4.54,0Z" style="opacity: 0.794718; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(526.7,47.88)" d="M5.17,0A5.17,5.17 0 1,1 0,-5.17A5.17,5.17 0 0,1 5.17,0Z" style="opacity: 0.821772; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(543.44,71.29)" d="M4.46,0A4.46,4.46 0 1,1 0,-4.46A4.46,4.46 0 0,1 4.46,0Z" style="opacity: 0.791112; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(554.6,97.29)" d="M3.66,0A3.66,3.66 0 1,1 0,-3.66A3.66,3.66 0 0,1 3.66,0Z" style="opacity: 0.757065; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(560.18,70.03)" d="M4.5,0A4.5,4.5 0 1,1 0,-4.5A4.5,4.5 0 0,1 4.5,0Z" style="opacity: 0.792763; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace870879c6-806f-46b1-afcb-62ced8af4a04" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(74.69,219.48)" d="M7,0A7,7 0 1,1 0,-7A7,7 0 0,1 7,0Z" style="opacity: 0.9; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(97.01,184.32)" d="M5.93,0A5.93,5.93 0 1,1 0,-5.93A5.93,5.93 0 0,1 5.93,0Z" style="opacity: 0.853951; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(102.59,165.27)" d="M5.34,0A5.34,5.34 0 1,1 0,-5.34A5.34,5.34 0 0,1 5.34,0Z" style="opacity: 0.828997; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(130.49,134.13)" d="M4.39,0A4.39,4.39 0 1,1 0,-4.39A4.39,4.39 0 0,1 4.39,0Z" style="opacity: 0.788207; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(136.07,120.56)" d="M3.98,0A3.98,3.98 0 1,1 0,-3.98A3.98,3.98 0 0,1 3.98,0Z" style="opacity: 0.770442; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(147.23,146.85)" d="M4.78,0A4.78,4.78 0 1,1 0,-4.78A4.78,4.78 0 0,1 4.78,0Z" style="opacity: 0.804877; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(191.88,131.92)" d="M4.32,0A4.32,4.32 0 1,1 0,-4.32A4.32,4.32 0 0,1 4.32,0Z" style="opacity: 0.785318; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(203.04,134.02)" d="M4.39,0A4.39,4.39 0 1,1 0,-4.39A4.39,4.39 0 0,1 4.39,0Z" style="opacity: 0.788064; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(230.94,131.71)" d="M4.32,0A4.32,4.32 0 1,1 0,-4.32A4.32,4.32 0 0,1 4.32,0Z" style="opacity: 0.785045; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(247.68,150.77)" d="M4.9,0A4.9,4.9 0 1,1 0,-4.9A4.9,4.9 0 0,1 4.9,0Z" style="opacity: 0.810002; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(264.42,122)" d="M4.02,0A4.02,4.02 0 1,1 0,-4.02A4.02,4.02 0 0,1 4.02,0Z" style="opacity: 0.772322; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(286.74,145.27)" d="M4.73,0A4.73,4.73 0 1,1 0,-4.73A4.73,4.73 0 0,1 4.73,0Z" style="opacity: 0.802806; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(297.9,127.16)" d="M4.18,0A4.18,4.18 0 1,1 0,-4.18A4.18,4.18 0 0,1 4.18,0Z" style="opacity: 0.779087; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(303.48,130.95)" d="M4.29,0A4.29,4.29 0 1,1 0,-4.29A4.29,4.29 0 0,1 4.29,0Z" style="opacity: 0.784042; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(336.97,110.35)" d="M3.66,0A3.66,3.66 0 1,1 0,-3.66A3.66,3.66 0 0,1 3.66,0Z" style="opacity: 0.757068; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(364.87,104.95)" d="M3.5,0A3.5,3.5 0 1,1 0,-3.5A3.5,3.5 0 0,1 3.5,0Z" style="opacity: 0.75; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(415.09,148.44)" d="M4.83,0A4.83,4.83 0 1,1 0,-4.83A4.83,4.83 0 0,1 4.83,0Z" style="opacity: 0.806953; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(493.22,122.28)" d="M4.03,0A4.03,4.03 0 1,1 0,-4.03A4.03,4.03 0 0,1 4.03,0Z" style="opacity: 0.772694; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(498.8,158.03)" d="M5.12,0A5.12,5.12 0 1,1 0,-5.12A5.12,5.12 0 0,1 5.12,0Z" style="opacity: 0.819509; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(509.96,122.71)" d="M4.04,0A4.04,4.04 0 1,1 0,-4.04A4.04,4.04 0 0,1 4.04,0Z" style="opacity: 0.773259; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(537.86,150.62)" d="M4.9,0A4.9,4.9 0 1,1 0,-4.9A4.9,4.9 0 0,1 4.9,0Z" style="opacity: 0.809809; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(576.92,115.29)" d="M3.82,0A3.82,3.82 0 1,1 0,-3.82A3.82,3.82 0 0,1 3.82,0Z" style="opacity: 0.763533; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(588.09,154.14)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 0.814419; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceff1d2e4c-c8b2-4ade-8eaf-161cbecb9c4b" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(588.09,152.66)" d="M4.96,0A4.96,4.96 0 1,1 0,-4.96A4.96,4.96 0 0,1 4.96,0Z" style="opacity: 0.812486; stroke-width: 1px; fill: rgb(255, 170, 0); fill-opacity: 1; stroke: rgb(178, 118, 0); stroke-opacity: 1;"/></g><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="x2tick"><text text-anchor="middle" x="0" y="615" transform="translate(152.95,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;"><tspan class="line" dy="0em" x="0" y="615">Jan 5</tspan><tspan class="line" dy="1.3em" x="0" y="615">2020</tspan></text></g><g class="x2tick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(231.07,0)">Jan 19</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(309.2,0)">Feb 2</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(387.32,0)">Feb 16</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(465.45,0)">Mar 1</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(543.5799999999999,0)">Mar 15</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(621.7,0)">Mar 29</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="615" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(699.83,0)">Apr 12</text></g></g><g class="yaxislayer-above"><g class="y2tick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,582.2537313432836)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">−60.00%</text></g><g class="y2tick"><text text-anchor="end" x="94" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,543.3537313432836)">−40.00%</text></g><g class="y2tick"><text text-anchor="end" x="94" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,504.4637313432836)">−20.00%</text></g><g class="y2tick"><text text-anchor="end" x="94" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,465.57373134328355)">−0.00%</text></g><g class="y2tick"><text text-anchor="end" x="94" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,426.6737313432836)">20.00%</text></g><g class="y2tick"><text text-anchor="end" x="94" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,387.78373134328353)">40.00%</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="iciclelayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-e017ec"><g class="clips"/><clipPath id="legende017ec"><rect width="500" height="48" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"><path data-index="0" fill-rule="evenodd" d="M95,465.57373134328355L720,465.57373134328355" clip-path="url(#clipe017ecy2)" style="opacity: 1; stroke: rgb(128, 128, 128); stroke-opacity: 1; fill: rgb(0, 0, 0); fill-opacity: 0; stroke-dasharray: 9px, 9px; stroke-width: 2px;"/></g></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(220,12.492537313432862)"><rect class="bg" shape-rendering="crispEdges" width="500" height="48" x="0" y="0" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;"/><g class="scrollbox" transform="" clip-path="url(#legende017ec)"><g class="groups"><g class="traces" transform="translate(0,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Close</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="74.859375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(130.609375,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Entry</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="74.640625" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(261.21875,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Exit - Profit</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="110.984375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(391.828125,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Exit - Loss</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="105.578125" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(0,33.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Active</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(255, 170, 0); fill-opacity: 1; stroke: rgb(178, 118, 0); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="79.234375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(130.609375,33.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Closed - Profit</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M4.74,0A4.74,4.74 0 1,1 0,-4.74A4.74,4.74 0 0,1 4.74,0Z" style="opacity: 0.803064; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="128.109375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(261.21875,33.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Closed - Loss</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M4.59,0A4.59,4.59 0 1,1 0,-4.59A4.59,4.59 0 0,1 4.59,0Z" style="opacity: 0.796539; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="122.6875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(391.828125,33.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Open</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M4.96,0A4.96,4.96 0 1,1 0,-4.96A4.96,4.96 0 0,1 4.96,0Z" style="opacity: 0.812486; stroke-width: 1px; fill: rgb(255, 170, 0); fill-opacity: 1; stroke: rgb(178, 118, 0); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="74.171875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" x="0" y="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-x2title"><text class="x2title" x="407.5" y="657.909375" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Index</text></g><g class="g-ytitle"><text class="ytitle" transform="rotate(-90,49.575,211.12313432835822)" x="49.575" y="211.12313432835822" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Price</text></g><g class="g-y2title" transform="translate(2.4267578125,0)"><text class="y2title" transform="rotate(-90,11.575000000000003,481.8768656716418)" x="11.575000000000003" y="481.8768656716418" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Trade PnL</text></g><g class="annotation" data-index="0" style="opacity: 1;"><g class="annotation-text-g" transform="rotate(0,407.5,79)"><g class="cursor-pointer" transform="translate(379,67)"><rect class="bg" x="0.5" y="0.5" width="57" height="23" style="stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;"/><text class="annotation-text" text-anchor="middle" x="29.078125" y="18" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Trades</text></g></g></g><g class="annotation" data-index="1" style="opacity: 1;"><g class="annotation-text-g" transform="rotate(0,407.5,349.75373134328356)"><g class="cursor-pointer" transform="translate(366,338)"><rect class="bg" x="0.5" y="0.5" width="83" height="23" style="stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;"/><text class="annotation-text" text-anchor="middle" x="42.0625" y="18" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Trade PnL</text></g></g></g></g></svg>