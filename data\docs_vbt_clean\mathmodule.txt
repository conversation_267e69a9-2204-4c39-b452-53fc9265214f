math_ moduleMath utilities. add_nb functionadd_nb ( a , b , rel_tol = 1e-09 , abs_tol = 1e-12 ) Add two floats. is_addition_zero_nb functionis_addition_zero_nb ( a , b , rel_tol = 1e-09 , abs_tol = 1e-12 ) Tell whether addition of two values yields zero. is_close_nb functionis_close_nb ( a , b , rel_tol = 1e-09 , abs_tol = 1e-12 ) Tell whether two values are approximately equal. is_close_or_less_nb functionis_close_or_less_nb ( a , b , rel_tol = 1e-09 , abs_tol = 1e-12 ) Tell whether the first value is approximately less than or equal to the second value. is_less_nb functionis_less_nb ( a , b , rel_tol = 1e-09 , abs_tol = 1e-12 ) Tell whether the first value is approximately less than the second value.