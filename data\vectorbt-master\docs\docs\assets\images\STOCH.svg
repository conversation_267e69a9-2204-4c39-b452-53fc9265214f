<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="350" style="" viewBox="0 0 700 350"><rect x="0" y="0" width="700" height="350" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-1413d0"><g class="clips"><clipPath id="clip1413d0xyplot" class="plotclip"><rect width="633" height="274"/></clipPath><clipPath class="axesclip" id="clip1413d0x"><rect x="37" y="0" width="633" height="350"/></clipPath><clipPath class="axesclip" id="clip1413d0y"><rect x="0" y="46" width="700" height="274"/></clipPath><clipPath class="axesclip" id="clip1413d0xy"><rect x="37" y="46" width="633" height="274"/></clipPath></g><g class="gradients"/></defs><g class="bglayer"><rect class="bg" x="37" y="46" width="633" height="274" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"><path data-index="0" fill-rule="evenodd" d="M37,232.82H670V133.18H37Z" clip-path="url('#clip1413d0xy')" style="opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(128, 0, 128); fill-opacity: 1; stroke-width: 0px;"/></g><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(40.44,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(147.09,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(250.29,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(356.94,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(460.15,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(566.79,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,257.73)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,207.91)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,158.09)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,108.27000000000001)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,58.45)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="yzl zl crisp" transform="translate(0,307.55)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(37, 46)" clip-path="url('#clip1413d0xyplot')"><g class="scatterlayer mlayer"><g class="trace scatter tracea0902177-ea17-49cf-9d53-72b7030cfeb5" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M44.72,91.58L48.16,74.23L51.6,38.32L55.04,33L58.48,50.05L61.92,45.88L65.36,25.02L68.8,14.64L72.24,84.94L75.68,90.65L79.13,78.01L82.57,92.56L86.01,155.53L89.45,148.58L92.89,25.53L96.33,50.84L99.77,33.45L103.21,143.01L106.65,143.88L110.09,107.64L113.53,19.14L116.97,73.05L120.41,82.16L123.85,61.5L127.29,57.3L130.73,32.07L134.17,17.72L137.61,33.61L141.05,29.89L144.49,76.57L147.93,72.94L151.37,73.45L154.81,60.2L158.25,82.19L161.69,91.71L165.13,84.74L168.57,73.6L172.01,75.47L175.45,57.25L178.89,69.74L182.33,24.9L185.77,35.03L189.21,76.77L192.65,169.1L196.09,158.65L199.53,163.11L202.97,156.31L206.41,179.98L209.85,168.62L213.29,140.76L216.73,85.77L220.17,47.76L223.61,32.02L227.05,44.47L230.49,61.63L233.93,61.2L237.38,14.76L240.82,14.56L244.26,23.86L247.7,27.55L251.14,70.33L254.58,32.97L258.02,35.84L261.46,17.96L264.9,50.81L268.34,103.41L271.78,110.48L275.22,23.95L278.66,45.53L282.1,47.71L285.54,80.71L288.98,63.09L292.42,55.16L295.86,56.86L299.3,14.45L306.18,37.4L309.62,45.46L313.06,99.54L316.5,73.96L319.94,76.05L323.38,57.26L330.26,232.07L333.7,212.4L337.14,217.12L340.58,178.83L344.02,194.32L347.46,240.22L350.9,186.35L354.34,198.88L361.22,128.19L364.66,35.56L368.1,16.45L371.54,60.46L374.98,25.37L378.42,57.64L381.86,31.75L385.3,20.78L388.74,12.45L392.18,44.21L395.63,39.05L399.07,29.31L402.51,12.45L405.95,46.5L409.39,128.44L412.83,79.28L416.27,104.31L419.71,166.82L423.15,179.5L426.59,170.3L430.03,112.62L433.47,162.97L436.91,185.41L440.35,171.29L443.79,156.41L447.23,105.16L450.67,87.49L454.11,92.94L457.55,142.51L460.99,108.92L464.43,140.04L467.87,223.49L471.31,176.56L474.75,259.55L478.19,228.22L481.63,167.13L485.07,175.66L488.51,160.81L491.95,171.37L495.39,187.44L498.83,215.22L502.27,207.34L505.71,193.54L509.15,195.79L512.59,227.3L516.03,210.17L519.47,214.51L522.91,202.85L526.35,145.99L529.79,102.45L533.23,86.01L536.67,30.84L540.11,17.99L543.55,20.88L546.99,78.05L550.43,39.81L553.88,37.8L557.32,46.33L560.76,88.28L564.2,78.34L567.64,90.72L571.08,140.74L574.52,247.81L577.96,200.56L581.4,194.54L584.84,208.21L588.28,197.27L591.72,142.61L595.16,146.8L598.6,211.52L602.04,212.08L605.48,180.09L608.92,196.39L612.36,199.28L615.8,162.25L619.24,161.62L622.68,246.05L626.12,247.08L629.56,232.72L633,227.41" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter trace5ec97453-5a91-4986-bb2c-3c78cf683a98" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M51.6,68.05L55.04,48.52L58.48,40.46L61.92,42.97L65.36,40.31L68.8,28.51L72.24,41.53L79.13,84.53L82.57,87.07L89.45,132.22L92.89,109.88L99.77,36.61L103.21,75.77L110.09,131.51L113.53,90.22L116.97,66.61L120.41,58.11L123.85,72.23L127.29,66.99L134.17,35.7L137.61,27.8L141.05,27.07L144.49,46.69L151.37,74.32L154.81,68.86L158.25,71.95L161.69,78.04L165.13,86.21L168.57,83.35L172.01,77.93L175.45,68.77L178.89,67.49L182.33,50.63L185.77,43.22L189.21,45.57L196.09,134.84L199.53,163.62L202.97,159.36L206.41,166.47L209.85,168.3L213.29,163.12L216.73,131.71L220.17,91.43L223.61,55.18L227.05,41.42L230.49,46.04L233.93,55.77L237.38,45.87L240.82,30.18L244.26,17.73L247.7,21.99L251.14,40.58L254.58,43.62L258.02,46.38L261.46,28.92L264.9,34.87L268.34,57.4L271.78,88.24L275.22,79.28L282.1,39.06L285.54,57.98L288.98,63.84L292.42,66.32L295.86,58.37L299.3,42.15L302.74,32.42L306.18,25.94L309.62,36.28L313.06,60.8L319.94,83.18L323.38,69.09L326.82,93.58L330.26,145.59L333.7,197.3L337.14,220.53L340.58,202.78L344.02,196.76L347.46,204.46L350.9,206.97L354.34,208.49L357.78,182.16L361.22,162.77L364.66,108.33L368.1,60.07L371.54,37.49L374.98,34.09L378.42,47.82L381.86,38.25L385.3,36.72L388.74,21.66L392.18,25.82L399.07,37.52L402.51,26.94L405.95,29.42L409.39,62.46L416.27,104.01L419.71,116.8L423.15,150.21L426.59,172.21L430.03,154.14L433.47,148.63L436.91,153.66L440.35,173.22L443.79,171.04L447.23,144.29L450.67,116.36L454.11,95.2L457.55,107.65L460.99,114.79L464.43,130.49L467.87,157.48L471.31,180.03L474.75,219.86L478.19,221.44L481.63,218.3L488.51,167.87L491.95,169.28L495.39,173.21L498.83,191.34L502.27,203.34L505.71,205.37L509.15,198.89L512.59,205.54L519.47,217.33L522.91,209.18L526.35,187.79L529.79,150.43L536.67,73.1L540.11,44.95L543.55,23.24L546.99,38.97L553.88,51.89L557.32,41.31L564.2,70.98L567.64,85.78L571.08,103.26L574.52,159.75L577.96,196.37L581.4,214.3L584.84,201.1L588.28,200.01L595.16,162.23L598.6,166.98L602.04,190.14L605.48,201.23L615.8,185.97L619.24,174.38L622.68,189.97L626.12,218.25L629.56,241.95L633,235.74" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(40.44,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Mar 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(147.09,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Apr 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(250.29,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">May 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(356.94,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jun 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(460.15,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jul 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(566.79,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Aug 2019</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,307.55)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,257.73)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">20</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,207.91)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">40</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,158.09)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">60</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,108.27000000000001)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">80</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,58.45)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">100</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-1413d0"><g class="clips"/><clipPath id="legend1413d0"><rect width="134" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(536, 11.519999999999996)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="134" height="29" x="0" y="0"/><g class="scrollbox" transform="translate(0, 0)" clip-path="url('#legend1413d0')"><g class="groups"><g class="traces" transform="translate(0, 14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">%K</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="63.734375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(66.234375, 14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">%D</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="64.671875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-ytitle"/></g></svg>