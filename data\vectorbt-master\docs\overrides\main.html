{% extends "base.html" %}

{% block extrahead %}
    {% if page and page.meta and page.meta.og_title %}
        <meta property="og:title" content="{{ page.meta.og_title }}" />
    {% elif page and page.meta and page.meta.title %}
        <meta property="og:title" content="{{ page.meta.title }}" />
    {% endif %}

    {% if page and page.meta and page.meta.og_type %}
        <meta property="og:type" content="{{ page.meta.og_type }}" />
    {% else %}
        <meta property="og:type" content="website" />
    {% endif %}

    <meta property="og:url" content="{{ page.canonical_url }}" />

    {% if page and page.meta and page.meta.page_path and page.meta.og_image %}
        <meta property="og:image:url" content="{{ config.site_url ~ page.meta.page_path ~ page.meta.og_image }}" />
    {% elif page and page.meta and page.meta.og_image %}
        <meta property="og:image:url" content="{{ page.meta.og_image }}" />
    {% else %}
        <meta property="og:image:url" content="/assets/logo/social-512x512.png" />
    {% endif %}

    {% if page and page.meta and page.meta.og_image_type %}
        <meta property="og:image:type" content="{{ page.meta.og_image_type}}" />
    {% else %}
        <meta property="og:image:type" content="image/png" />
    {% endif %}

    {% if page and page.meta and page.meta.description %}
        <meta property="og:description" content="{{ page.meta.description }}">
    {% elif config.site_description %}
        <meta property="og:description" content="{{ config.site_description }}">
    {% endif %}

    <meta property="og:locale" content="en-GB" />

    <link rel="apple-touch-icon" sizes="180x180" href="/assets/logo/apple-touch-icon.png">
    <link rel="icon" type="image/svg+xml" href="/assets/logo/favicon.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="/assets/logo/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/assets/logo/favicon-16x16.png">
    <link rel="manifest" href="/assets/logo/site.webmanifest">
    <link rel="mask-icon" href="/assets/logo/safari-pinned-tab.svg" color="#1e2029">
    <link rel="shortcut icon" href="/assets/logo/favicon.ico">
    <meta name="msapplication-TileColor" content="#1e2029">
    <meta name="msapplication-config" content="/assets/logo/browserconfig.xml">
    <meta name="theme-color" content="#1e2029">
{% endblock %}

{% block announce %}
    Get access to <a href="https://vectorbt.pro/" style="color: #ff6e42; text-decoration: underline;">vectorbt PRO</a> and join our quant army!
{% endblock %}

{% block content %}
  {{ super() }}
  {% if config.theme.sponsor %}
  <footer class="sponsorship">
    <hr>
    <a href="{{config.theme.sponsor}}" title="Become a sponsor">
      <span class="twemoji heart-throb-hover">
        {% set icon = "octicons/heart-fill-16" %}
        {% include ".icons/" ~ icon ~ ".svg" %}
      </span>
    </a>
    <hr>
  </footer>
  {% endif %}
{% endblock %}