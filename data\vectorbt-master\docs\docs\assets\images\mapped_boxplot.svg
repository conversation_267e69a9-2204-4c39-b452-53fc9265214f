<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="350" style="" viewBox="0 0 700 350"><rect x="0" y="0" width="700" height="350" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-3b8fae"><g class="clips"><clipPath id="clip3b8faexyplot" class="plotclip"><rect width="640" height="274"/></clipPath><clipPath class="axesclip" id="clip3b8faex"><rect x="30" y="0" width="640" height="350"/></clipPath><clipPath class="axesclip" id="clip3b8faey"><rect x="0" y="46" width="700" height="274"/></clipPath><clipPath class="axesclip" id="clip3b8faexy"><rect x="30" y="46" width="640" height="274"/></clipPath></g><g class="gradients"/></defs><g class="bglayer"><rect class="bg" x="30" y="46" width="640" height="274" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"/><g class="y"><path class="ygrid crisp" transform="translate(0,306.3)" d="M30,0h640" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,244.65)" d="M30,0h640" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,183)" d="M30,0h640" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,121.35)" d="M30,0h640" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,59.7)" d="M30,0h640" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"/><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(30, 46)" clip-path="url('#clip3b8faexyplot')"><g class="boxlayer mlayer"><g class="trace boxes" style="opacity: 1;"><path class="box" d="M54.4,229.48H158.93M54.4,252.59H158.93V206.36H54.4ZM106.67,252.59V260.3M106.67,206.36V198.65M80.53,260.3H132.8M80.53,198.65H132.8" style="vector-effect: non-scaling-stroke; stroke-width: 2px; stroke: rgb(31, 119, 180); stroke-opacity: 1; fill: rgb(31, 119, 180); fill-opacity: 0.5;"/><g class="points"/></g><g class="trace boxes" style="opacity: 1;"><path class="box" d="M267.73,137H372.27M267.73,160.12H372.27V113.88H267.73ZM320,160.12V167.83M320,113.88V106.17M293.87,167.83H346.13M293.87,106.17H346.13" style="vector-effect: non-scaling-stroke; stroke-width: 2px; stroke: rgb(255, 127, 14); stroke-opacity: 1; fill: rgb(255, 127, 14); fill-opacity: 0.5;"/><g class="points"/></g><g class="trace boxes" style="opacity: 1;"><path class="box" d="M481.07,44.52H585.6M481.07,67.64H585.6V21.41H481.07ZM533.33,67.64V75.35M533.33,21.41V13.7M507.2,75.35H559.47M507.2,13.7H559.47" style="vector-effect: non-scaling-stroke; stroke-width: 2px; stroke: rgb(44, 160, 44); stroke-opacity: 1; fill: rgb(44, 160, 44); fill-opacity: 0.5;"/><g class="points"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(136.67000000000002,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">a</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(350,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">b</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(563.33,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">c</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="29" y="4.199999999999999" transform="translate(0,306.3)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">10</text></g><g class="ytick"><text text-anchor="end" x="29" y="4.199999999999999" transform="translate(0,244.65)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">12</text></g><g class="ytick"><text text-anchor="end" x="29" y="4.199999999999999" transform="translate(0,183)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">14</text></g><g class="ytick"><text text-anchor="end" x="29" y="4.199999999999999" transform="translate(0,121.35)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">16</text></g><g class="ytick"><text text-anchor="end" x="29" y="4.199999999999999" transform="translate(0,59.7)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">18</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-3b8fae"><g class="clips"/><clipPath id="legend3b8fae"><rect width="156" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(514, 11.519999999999996)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="156" height="29" x="0" y="0"/><g class="scrollbox" transform="translate(0, 0)" clip-path="url('#legend3b8fae')"><g class="groups"><g class="traces" transform="translate(0, 14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">a</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="legendbox" d="M6,6H-6V-6H6Z" transform="translate(20,0)" style="stroke-width: 2px; fill: rgb(31, 119, 180); fill-opacity: 0.5; stroke: rgb(31, 119, 180); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="49.71875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(52.21875, 14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">b</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="legendbox" d="M6,6H-6V-6H6Z" transform="translate(20,0)" style="stroke-width: 2px; fill: rgb(255, 127, 14); fill-opacity: 0.5; stroke: rgb(255, 127, 14); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="49.984375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(104.703125, 14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">c</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="legendbox" d="M6,6H-6V-6H6Z" transform="translate(20,0)" style="stroke-width: 2px; fill: rgb(44, 160, 44); fill-opacity: 0.5; stroke: rgb(44, 160, 44); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="48.765625" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-ytitle"/></g></svg>