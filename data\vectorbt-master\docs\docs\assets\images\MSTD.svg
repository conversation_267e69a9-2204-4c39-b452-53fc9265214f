<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="350" style="" viewBox="0 0 700 350"><rect x="0" y="0" width="700" height="350" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-bd0379"><g class="clips"><clipPath id="clipbd0379xyplot" class="plotclip"><rect width="626" height="274"/></clipPath><clipPath class="axesclip" id="clipbd0379x"><rect x="44" y="0" width="626" height="350"/></clipPath><clipPath class="axesclip" id="clipbd0379y"><rect x="0" y="46" width="700" height="274"/></clipPath><clipPath class="axesclip" id="clipbd0379xy"><rect x="44" y="46" width="626" height="274"/></clipPath></g><g class="gradients"/></defs><g class="bglayer"><rect class="bg" x="44" y="46" width="626" height="274" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(47.4,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(152.87,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(254.93,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(360.4,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(462.47,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(567.93,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,268.93)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,227.08)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,185.23)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,143.38)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,101.53)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,59.69)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="yzl zl crisp" transform="translate(0,310.78)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(44, 46)" clip-path="url('#clipbd0379xyplot')"><g class="scatterlayer mlayer"><g class="trace scatter tracea4d2f628-f425-43cf-8e67-c533d313bdbd" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M30.62,254.21L34.02,253.37L37.42,253.47L44.23,254.08L47.63,260.3L51.03,259.82L54.43,255.72L64.64,251.93L68.04,250.23L74.85,252.77L78.25,255.19L81.65,258.11L85.05,258.22L105.47,254.57L108.87,252.74L112.27,212.33L115.67,192.18L122.48,177.3L125.88,173.43L129.28,171.48L132.68,172.57L136.09,182.04L139.49,198.43L142.89,234.46L146.29,238.39L149.7,240.26L153.1,244.88L159.9,246.3L163.3,245.69L166.71,245.47L170.11,243.97L173.51,243.58L176.91,244.33L180.32,243.51L183.72,238.01L187.12,237.69L190.52,242.61L197.33,243.63L200.73,243.49L207.53,242.73L210.93,242.47L214.34,240.5L217.74,231.5L221.14,221.03L224.54,217.7L234.75,215.54L238.15,214.03L241.55,207.79L244.96,169.28L248.36,160.06L251.76,122.3L255.16,96.04L258.57,78.76L261.97,84.97L265.37,105.84L268.77,130.19L272.17,147.01L275.58,176.05L278.98,181.48L282.38,201.42L285.78,201.36L289.18,201.44L292.59,204.65L295.99,184.26L299.39,176.76L302.79,186.36L306.2,181.08L313,184.23L316.4,197.73L323.21,214.68L326.61,198.25L330.01,187.57L333.41,184.85L340.22,192.67L343.62,187.46L347.02,194.37L350.42,203.79L353.83,230.16L357.23,229.38L364.03,191.81L367.43,177.98L370.84,156.93L374.24,153.24L377.64,159.03L381.04,156.73L384.45,146.29L387.85,122.99L394.65,100.32L398.05,71.94L401.46,13.7L404.86,24.11L408.26,29.01L411.66,55.93L415.07,89.84L418.47,101.73L421.87,103.58L425.27,106.3L428.67,108.86L432.08,105.51L435.48,145.95L438.88,146.03L442.28,150.66L445.68,130.92L449.09,129.52L452.49,145.23L455.89,158.65L459.29,158.76L462.7,128.9L466.1,127.27L469.5,75.76L472.9,48.93L476.3,61.33L479.71,91L483.11,117.82L486.51,125.52L489.91,153.52L493.32,169.93L496.72,164.81L500.12,173.07L503.52,183.32L506.92,176.48L510.33,175.25L513.73,175.2L520.53,210.03L523.93,207.55L527.34,192.34L530.74,171.48L534.14,152.89L537.54,110.88L540.95,105.17L544.35,95.5L547.75,101.58L551.15,123.44L557.96,164.26L561.36,186.1L564.76,188.85L568.16,148.61L571.57,133.14L574.97,122.71L578.37,123.02L581.77,135.68L585.17,156.78L588.58,165.5L591.98,180.03L595.38,200.5L598.78,209.83L602.18,212.18L605.59,210.15L608.99,210.15L612.39,209.6L615.79,197.89L619.2,196.69L622.6,203.22L626,198.7" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(47.4,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Mar 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(152.87,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Apr 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(254.93,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">May 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(360.4,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jun 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(462.47,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jul 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(567.93,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Aug 2019</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,310.78)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0</text></g><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,268.93)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">200</text></g><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,227.08)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">400</text></g><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,185.23)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">600</text></g><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,143.38)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">800</text></g><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,101.53)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">1000</text></g><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,59.69)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">1200</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-bd0379"><g class="clips"/><clipPath id="legendbd0379"><rect width="80" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(590, 11.519999999999996)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="80" height="29" x="0" y="0"/><g class="scrollbox" transform="translate(0, 0)" clip-path="url('#legendbd0379')"><g class="groups"><g class="traces" transform="translate(0, 14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">MSTD</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="77.46875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-ytitle"/></g></svg>