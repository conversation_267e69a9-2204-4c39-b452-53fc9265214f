generators moduleSignal generators built with SignalFactory . ohlcstx_config ConfigFactory config for OHLCSTX . ohlcstx_func_config ConfigExit function config for OHLCSTX . rprobx_config ConfigFactory config for RPROBX . rprobx_func_config ConfigExit function config for RPROBX . stx_config ConfigFactory config for STX . stx_func_config ConfigExit function config for STX . OHLCSTCX classOHLCSTCX ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Exit signal generator based on OHLC and stop values. Generates chain of new_entries and exits based on entries and ohlc_stop_choice_nb() . See OHLCSTX for notes on parameters. Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.signals.generators.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.signals.generators._OHLCSTCX close methodInput array. close_above methodOHLCSTCX . close_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is above other . See combine_objs() . close_below methodOHLCSTCX . close_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is below other . See combine_objs() . close_crossed_above methodOHLCSTCX . close_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_above other . See combine_objs() . close_crossed_below methodOHLCSTCX . close_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_below other . See combine_objs() . close_equal methodOHLCSTCX . close_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is equal other . See combine_objs() . close_stats methodOHLCSTCX . close_stats ( * args , ** kwargs ) Stats of close as generic. custom_func methodSignalFactory . from_choice_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , flex_2d = None , entry_args = None , exit_args = None , cache_args = None , entry_kwargs = None , exit_kwargs = None , cache_kwargs = None , return_cache = False , use_cache = None , ** _kwargs ) entries methodInput array. entries_and methodOHLCSTCX . entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries AND other . See combine_objs() . entries_or methodOHLCSTCX . entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries OR other . See combine_objs() . entries_stats methodOHLCSTCX . entries_stats ( * args , ** kwargs ) Stats of entries as signals. entries_xor methodOHLCSTCX . entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries XOR other . See combine_objs() . exits propertyOutput array. exits_and methodOHLCSTCX . exits_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits AND other . See combine_objs() . exits_or methodOHLCSTCX . exits_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits OR other . See combine_objs() . exits_stats methodOHLCSTCX . exits_stats ( * args , ** kwargs ) Stats of exits as signals. exits_xor methodOHLCSTCX . exits_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits XOR other . See combine_objs() . high methodInput array. high_above methodOHLCSTCX . high_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is above other . See combine_objs() . high_below methodOHLCSTCX . high_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is below other . See combine_objs() . high_crossed_above methodOHLCSTCX . high_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is crossed_above other . See combine_objs() . high_crossed_below methodOHLCSTCX . high_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is crossed_below other . See combine_objs() . high_equal methodOHLCSTCX . high_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is equal other . See combine_objs() . high_stats methodOHLCSTCX . high_stats ( * args , ** kwargs ) Stats of high as generic. low methodInput array. low_above methodOHLCSTCX . low_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is above other . See combine_objs() . low_below methodOHLCSTCX . low_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is below other . See combine_objs() . low_crossed_above methodOHLCSTCX . low_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is crossed_above other . See combine_objs() . low_crossed_below methodOHLCSTCX . low_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is crossed_below other . See combine_objs() . low_equal methodOHLCSTCX . low_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is equal other . See combine_objs() . low_stats methodOHLCSTCX . low_stats ( * args , ** kwargs ) Stats of low as generic. new_entries propertyOutput array. new_entries_and methodOHLCSTCX . new_entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return new_entries AND other . See combine_objs() . new_entries_or methodOHLCSTCX . new_entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return new_entries OR other . See combine_objs() . new_entries_stats methodOHLCSTCX . new_entries_stats ( * args , ** kwargs ) Stats of new_entries as signals. new_entries_xor methodOHLCSTCX . new_entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return new_entries XOR other . See combine_objs() . open methodInput array. open_above methodOHLCSTCX . open_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where open is above other . See combine_objs() . open_below methodOHLCSTCX . open_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where open is below other . See combine_objs() . open_crossed_above methodOHLCSTCX . open_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where open is crossed_above other . See combine_objs() . open_crossed_below methodOHLCSTCX . open_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where open is crossed_below other . See combine_objs() . open_equal methodOHLCSTCX . open_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where open is equal other . See combine_objs() . open_stats methodOHLCSTCX . open_stats ( * args , ** kwargs ) Stats of open as generic. plot method_bind_ohlcstx_plot .< locals >. plot ( plot_type = None , ohlc_kwargs = None , entry_trace_kwargs = None , exit_trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Plot OHLC, OHLCSTCX.new_entries and OHLCSTCX.exits . Args plot_type Either 'OHLC', 'Candlestick' or Plotly trace. ohlc_kwargs : dict Keyword arguments passed to plot_type . entry_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_entry_markers() for OHLCSTCX.new_entries . exit_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_exit_markers() for OHLCSTCX.exits . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. reverse_list propertyList of reverse values. run class methodOHLCSTCX . run ( entries , open , high , low , close , sl_stop = Default ( nan ), sl_trail = Default ( False ), tp_stop = Default ( nan ), reverse = Default ( False ), stop_price = nan , stop_type =- 1 , short_name = 'ohlcstcx' , hide_params = None , hide_default = True , ** kwargs ) Run OHLCSTCX indicator. Inputs: entries , open , high , low , close In-place outputs: stop_price , stop_type Parameters: sl_stop , sl_trail , tp_stop , reverse Outputs: new_entries , exits Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodOHLCSTCX . run_combs ( entries , open , high , low , close , sl_stop = Default ( nan ), sl_trail = Default ( False ), tp_stop = Default ( nan ), reverse = Default ( False ), stop_price = nan , stop_type =- 1 , r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple OHLCSTCX indicators using function comb_func . Inputs: entries , open , high , low , close In-place outputs: stop_price , stop_type Parameters: sl_stop , sl_trail , tp_stop , reverse Outputs: new_entries , exits comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to OHLCSTCX.run() . sl_stop_list propertyList of sl_stop values. sl_trail_list propertyList of sl_trail values. stop_price propertyIn-place output array. stop_price_above methodOHLCSTCX . stop_price_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where stop_price is above other . See combine_objs() . stop_price_below methodOHLCSTCX . stop_price_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where stop_price is below other . See combine_objs() . stop_price_crossed_above methodOHLCSTCX . stop_price_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where stop_price is crossed_above other . See combine_objs() . stop_price_crossed_below methodOHLCSTCX . stop_price_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where stop_price is crossed_below other . See combine_objs() . stop_price_equal methodOHLCSTCX . stop_price_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where stop_price is equal other . See combine_objs() . stop_price_stats methodOHLCSTCX . stop_price_stats ( * args , ** kwargs ) Stats of stop_price as generic. stop_type propertyIn-place output array. stop_type_readable propertystop_type in readable format based on the following mapping: { "0" : "StopLoss" , "1" : "TrailStop" , "2" : "TakeProfit" , "-1" : null } stop_type_stats methodOHLCSTCX . stop_type_stats ( * args , ** kwargs ) Stats of stop_type based on the following mapping: { "0" : "StopLoss" , "1" : "TrailStop" , "2" : "TakeProfit" , "-1" : null } tp_stop_list propertyList of tp_stop values. OHLCSTX classOHLCSTX ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Exit signal generator based on OHLC and stop values. Generates exits based on entries and ohlc_stop_choice_nb() . Hint All parameters can be either a single value (per frame) or a NumPy array (per row, column, or element). To generate multiple combinations, pass them as lists. Usage Test each stop type: import vectorbt as vbt import pandas as pd import numpy as np entries = pd . Series ([ True , False , False , False , False , False ]) price = pd . DataFrame ({ ... 'open' : [ 10 , 11 , 12 , 11 , 10 , 9 ], ... 'high' : [ 11 , 12 , 13 , 12 , 11 , 10 ], ... 'low' : [ 9 , 10 , 11 , 10 , 9 , 8 ], ... 'close' : [ 10 , 11 , 12 , 11 , 10 , 9 ] ... }) ohlcstx = vbt . OHLCSTX . run ( ... entries , ... price [ 'open' ], price [ 'high' ], price [ 'low' ], price [ 'close' ], ... sl_stop = [ 0.1 , 0.1 , np . nan ], ... sl_trail = [ False , True , False ], ... tp_stop = [ np . nan , np . nan , 0.1 ]) ohlcstx . entries ohlcstx_sl_stop 0.1 0.1 NaN ohlcstx_sl_trail False True False ohlcstx_tp_stop NaN NaN 0.1 0 True True True 1 False False False 2 False False False 3 False False False 4 False False False 5 False False False ohlcstx . exits ohlcstx_sl_stop 0.1 0.1 NaN ohlcstx_sl_trail False True False ohlcstx_tp_stop NaN NaN 0.1 0 False False False 1 False False True 2 False False False 3 False True False 4 True False False 5 False False False ohlcstx . stop_price ohlcstx_sl_stop 0.1 0.1 NaN ohlcstx_sl_trail False True False ohlcstx_tp_stop NaN NaN 0.1 0 NaN NaN NaN 1 NaN NaN 11.0 2 NaN NaN NaN 3 NaN 11.7 NaN 4 9.0 NaN NaN 5 NaN NaN NaN ohlcstx . stop_type_readable ohlcstx_sl_stop 0.1 0.1 NaN ohlcstx_sl_trail False True False ohlcstx_tp_stop NaN NaN 0.1 0 None None None 1 None None TakeProfit 2 None None None 3 None TrailStop None 4 StopLoss None None 5 None None None Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.signals.generators.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.signals.generators._OHLCSTX close methodInput array. close_above methodOHLCSTX . close_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is above other . See combine_objs() . close_below methodOHLCSTX . close_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is below other . See combine_objs() . close_crossed_above methodOHLCSTX . close_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_above other . See combine_objs() . close_crossed_below methodOHLCSTX . close_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_below other . See combine_objs() . close_equal methodOHLCSTX . close_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is equal other . See combine_objs() . close_stats methodOHLCSTX . close_stats ( * args , ** kwargs ) Stats of close as generic. custom_func methodSignalFactory . from_choice_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , flex_2d = None , entry_args = None , exit_args = None , cache_args = None , entry_kwargs = None , exit_kwargs = None , cache_kwargs = None , return_cache = False , use_cache = None , ** _kwargs ) entries methodInput array. entries_and methodOHLCSTX . entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries AND other . See combine_objs() . entries_or methodOHLCSTX . entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries OR other . See combine_objs() . entries_stats methodOHLCSTX . entries_stats ( * args , ** kwargs ) Stats of entries as signals. entries_xor methodOHLCSTX . entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries XOR other . See combine_objs() . exits propertyOutput array. exits_and methodOHLCSTX . exits_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits AND other . See combine_objs() . exits_or methodOHLCSTX . exits_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits OR other . See combine_objs() . exits_stats methodOHLCSTX . exits_stats ( * args , ** kwargs ) Stats of exits as signals. exits_xor methodOHLCSTX . exits_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits XOR other . See combine_objs() . high methodInput array. high_above methodOHLCSTX . high_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is above other . See combine_objs() . high_below methodOHLCSTX . high_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is below other . See combine_objs() . high_crossed_above methodOHLCSTX . high_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is crossed_above other . See combine_objs() . high_crossed_below methodOHLCSTX . high_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is crossed_below other . See combine_objs() . high_equal methodOHLCSTX . high_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is equal other . See combine_objs() . high_stats methodOHLCSTX . high_stats ( * args , ** kwargs ) Stats of high as generic. low methodInput array. low_above methodOHLCSTX . low_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is above other . See combine_objs() . low_below methodOHLCSTX . low_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is below other . See combine_objs() . low_crossed_above methodOHLCSTX . low_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is crossed_above other . See combine_objs() . low_crossed_below methodOHLCSTX . low_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is crossed_below other . See combine_objs() . low_equal methodOHLCSTX . low_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is equal other . See combine_objs() . low_stats methodOHLCSTX . low_stats ( * args , ** kwargs ) Stats of low as generic. open methodInput array. open_above methodOHLCSTX . open_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where open is above other . See combine_objs() . open_below methodOHLCSTX . open_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where open is below other . See combine_objs() . open_crossed_above methodOHLCSTX . open_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where open is crossed_above other . See combine_objs() . open_crossed_below methodOHLCSTX . open_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where open is crossed_below other . See combine_objs() . open_equal methodOHLCSTX . open_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where open is equal other . See combine_objs() . open_stats methodOHLCSTX . open_stats ( * args , ** kwargs ) Stats of open as generic. plot method_bind_ohlcstx_plot .< locals >. plot ( plot_type = None , ohlc_kwargs = None , entry_trace_kwargs = None , exit_trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Plot OHLC, OHLCSTX.entries and OHLCSTX.exits . Args plot_type Either 'OHLC', 'Candlestick' or Plotly trace. ohlc_kwargs : dict Keyword arguments passed to plot_type . entry_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_entry_markers() for OHLCSTX.entries . exit_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_exit_markers() for OHLCSTX.exits . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage ohlcstx . iloc [:, 0 ] . plot () reverse_list propertyList of reverse values. run class methodOHLCSTX . run ( entries , open , high , low , close , sl_stop = Default ( nan ), sl_trail = Default ( False ), tp_stop = Default ( nan ), reverse = Default ( False ), stop_price = nan , stop_type =- 1 , short_name = 'ohlcstx' , hide_params = None , hide_default = True , ** kwargs ) Run OHLCSTX indicator. Inputs: entries , open , high , low , close In-place outputs: stop_price , stop_type Parameters: sl_stop , sl_trail , tp_stop , reverse Outputs: exits Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodOHLCSTX . run_combs ( entries , open , high , low , close , sl_stop = Default ( nan ), sl_trail = Default ( False ), tp_stop = Default ( nan ), reverse = Default ( False ), stop_price = nan , stop_type =- 1 , r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple OHLCSTX indicators using function comb_func . Inputs: entries , open , high , low , close In-place outputs: stop_price , stop_type Parameters: sl_stop , sl_trail , tp_stop , reverse Outputs: exits comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to OHLCSTX.run() . sl_stop_list propertyList of sl_stop values. sl_trail_list propertyList of sl_trail values. stop_price propertyIn-place output array. stop_price_above methodOHLCSTX . stop_price_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where stop_price is above other . See combine_objs() . stop_price_below methodOHLCSTX . stop_price_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where stop_price is below other . See combine_objs() . stop_price_crossed_above methodOHLCSTX . stop_price_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where stop_price is crossed_above other . See combine_objs() . stop_price_crossed_below methodOHLCSTX . stop_price_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where stop_price is crossed_below other . See combine_objs() . stop_price_equal methodOHLCSTX . stop_price_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where stop_price is equal other . See combine_objs() . stop_price_stats methodOHLCSTX . stop_price_stats ( * args , ** kwargs ) Stats of stop_price as generic. stop_type propertyIn-place output array. stop_type_readable propertystop_type in readable format based on the following mapping: { "0" : "StopLoss" , "1" : "TrailStop" , "2" : "TakeProfit" , "-1" : null } stop_type_stats methodOHLCSTX . stop_type_stats ( * args , ** kwargs ) Stats of stop_type based on the following mapping: { "0" : "StopLoss" , "1" : "TrailStop" , "2" : "TakeProfit" , "-1" : null } tp_stop_list propertyList of tp_stop values. RAND classRAND ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Random entry signal generator based on the number of signals. Generates entries based on rand_choice_nb() . Hint Parameter n can be either a single value (per frame) or a NumPy array (per column). To generate multiple combinations, pass it as a list. Usage Test three different entry counts values: import vectorbt as vbt rand = vbt . RAND . run ( input_shape = ( 6 ,), n = [ 1 , 2 , 3 ], seed = 42 ) rand . entries rand_n 1 2 3 0 True True True 1 False False True 2 False False False 3 False True False 4 False False True 5 False False False Entry count can also be set per column: import numpy as np rand = vbt . RAND . run ( input_shape = ( 8 , 2 ), n = [ np . array ([ 1 , 2 ]), 3 ], seed = 42 ) rand . entries rand_n 1 2 3 3 0 1 0 1 0 False False True False 1 True False False False 2 False False False True 3 False True True False 4 False False False False 5 False False False True 6 False False True False 7 False True False True Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.signals.generators.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.signals.generators._RAND custom_func methodSignalFactory . from_choice_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , flex_2d = None , entry_args = None , exit_args = None , cache_args = None , entry_kwargs = None , exit_kwargs = None , cache_kwargs = None , return_cache = False , use_cache = None , ** _kwargs ) entries propertyOutput array. entries_and methodRAND . entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries AND other . See combine_objs() . entries_or methodRAND . entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries OR other . See combine_objs() . entries_stats methodRAND . entries_stats ( * args , ** kwargs ) Stats of entries as signals. entries_xor methodRAND . entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries XOR other . See combine_objs() . n_list propertyList of n values. plot methodSignalFactory . __init__ .< locals >. plot ( _self , entry_y = None , exit_y = None , entry_types = None , exit_types = None , entry_trace_kwargs = None , exit_trace_kwargs = None , fig = None , ** kwargs ) Plot RAND.entries and RAND.exits . Args entry_y : array_like Y-axis values to plot entry markers on. exit_y : array_like Y-axis values to plot exit markers on. entry_types : array_like Entry types in string format. exit_types : array_like Exit types in string format. entry_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_entry_markers() for RAND.entries . exit_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_exit_markers() for RAND.exits . fig : Figure or FigureWidget Figure to add traces to. **kwargs Keyword arguments passed to SignalsSRAccessor.plot_as_markers() . run class methodRAND . run ( input_shape , n , short_name = 'rand' , hide_params = None , hide_default = True , ** kwargs ) Run RAND indicator. Parameters: n Outputs: entries Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodRAND . run_combs ( input_shape , n , r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple RAND indicators using function comb_func . Parameters: n Outputs: entries comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to RAND.run() . RANDNX classRANDNX ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Random entry and exit signal generator based on the number of signals. Generates entries and exits based on rand_enex_apply_nb() . See RAND for notes on parameters. Usage Test three different entry and exit counts: import vectorbt as vbt randnx = vbt . RANDNX . run ( ... input_shape = ( 6 ,), ... n = [ 1 , 2 , 3 ], ... seed = 42 ) randnx . entries randnx_n 1 2 3 0 True True True 1 False False False 2 False True True 3 False False False 4 False False True 5 False False False randnx . exits randnx_n 1 2 3 0 False False False 1 True True True 2 False False False 3 False True True 4 False False False 5 False False True Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.signals.generators.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.signals.generators._RANDNX apply_func methodRANDNX . apply_func ( input_shape , n , entry_wait , exit_wait ) apply_func_nb that calls generate_rand_enex_nb . custom_func methodIndicatorFactory . from_apply_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , col = None , flex_2d = None , return_cache = False , use_cache = None , use_ray = False , ** _kwargs ) Custom function that forwards inputs and parameters to apply_func . entries propertyOutput array. entries_and methodRANDNX . entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries AND other . See combine_objs() . entries_or methodRANDNX . entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries OR other . See combine_objs() . entries_stats methodRANDNX . entries_stats ( * args , ** kwargs ) Stats of entries as signals. entries_xor methodRANDNX . entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries XOR other . See combine_objs() . exits propertyOutput array. exits_and methodRANDNX . exits_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits AND other . See combine_objs() . exits_or methodRANDNX . exits_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits OR other . See combine_objs() . exits_stats methodRANDNX . exits_stats ( * args , ** kwargs ) Stats of exits as signals. exits_xor methodRANDNX . exits_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits XOR other . See combine_objs() . n_list propertyList of n values. plot methodSignalFactory . __init__ .< locals >. plot ( _self , entry_y = None , exit_y = None , entry_types = None , exit_types = None , entry_trace_kwargs = None , exit_trace_kwargs = None , fig = None , ** kwargs ) Plot RANDNX.entries and RANDNX.exits . Args entry_y : array_like Y-axis values to plot entry markers on. exit_y : array_like Y-axis values to plot exit markers on. entry_types : array_like Entry types in string format. exit_types : array_like Exit types in string format. entry_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_entry_markers() for RANDNX.entries . exit_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_exit_markers() for RANDNX.exits . fig : Figure or FigureWidget Figure to add traces to. **kwargs Keyword arguments passed to SignalsSRAccessor.plot_as_markers() . run class methodRANDNX . run ( input_shape , n , short_name = 'randnx' , hide_params = None , hide_default = True , ** kwargs ) Run RANDNX indicator. Parameters: n Outputs: entries , exits Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodRANDNX . run_combs ( input_shape , n , r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple RANDNX indicators using function comb_func . Parameters: n Outputs: entries , exits comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to RANDNX.run() . RANDX classRANDX ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Random exit signal generator based on the number of signals. Generates exits based on entries and rand_choice_nb() . See RAND for notes on parameters. Usage Generate an exit for each entry: import vectorbt as vbt import pandas as pd entries = pd . Series ([ True , False , False , True , False , False ]) randx = vbt . RANDX . run ( entries , seed = 42 ) randx . exits 0 False 1 False 2 True 3 False 4 True 5 False dtype: bool Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.signals.generators.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.run_combs() IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.signals.generators._RANDX custom_func methodSignalFactory . from_choice_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , flex_2d = None , entry_args = None , exit_args = None , cache_args = None , entry_kwargs = None , exit_kwargs = None , cache_kwargs = None , return_cache = False , use_cache = None , ** _kwargs ) entries methodInput array. entries_and methodRANDX . entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries AND other . See combine_objs() . entries_or methodRANDX . entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries OR other . See combine_objs() . entries_stats methodRANDX . entries_stats ( * args , ** kwargs ) Stats of entries as signals. entries_xor methodRANDX . entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries XOR other . See combine_objs() . exits propertyOutput array. exits_and methodRANDX . exits_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits AND other . See combine_objs() . exits_or methodRANDX . exits_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits OR other . See combine_objs() . exits_stats methodRANDX . exits_stats ( * args , ** kwargs ) Stats of exits as signals. exits_xor methodRANDX . exits_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits XOR other . See combine_objs() . plot methodSignalFactory . __init__ .< locals >. plot ( _self , entry_y = None , exit_y = None , entry_types = None , exit_types = None , entry_trace_kwargs = None , exit_trace_kwargs = None , fig = None , ** kwargs ) Plot RANDX.entries and RANDX.exits . Args entry_y : array_like Y-axis values to plot entry markers on. exit_y : array_like Y-axis values to plot exit markers on. entry_types : array_like Entry types in string format. exit_types : array_like Exit types in string format. entry_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_entry_markers() for RANDX.entries . exit_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_exit_markers() for RANDX.exits . fig : Figure or FigureWidget Figure to add traces to. **kwargs Keyword arguments passed to SignalsSRAccessor.plot_as_markers() . run class methodRANDX . run ( entries , short_name = 'randx' , hide_params = None , hide_default = True , ** kwargs ) Run RANDX indicator. Inputs: entries Outputs: exits Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . RPROB classRPROB ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Random entry signal generator based on probabilities. Generates entries based on rand_by_prob_choice_nb() . Hint All parameters can be either a single value (per frame) or a NumPy array (per row, column, or element). To generate multiple combinations, pass them as lists. Usage Generate three columns with different entry probabilities: import vectorbt as vbt rprob = vbt . RPROB . run ( input_shape = ( 5 ,), prob = [ 0. , 0.5 , 1. ], seed = 42 ) rprob . entries rprob_prob 0.0 0.5 1.0 0 False True True 1 False True True 2 False False True 3 False False True 4 False False True Probability can also be set per row, column, or element: import numpy as np rprob = vbt . RPROB . run ( input_shape = ( 5 ,), prob = np . array ([ 0. , 0. , 1. , 1. , 1. ]), seed = 42 ) rprob . entries 0 False 1 False 2 True 3 True 4 True Name: array_0, dtype: bool Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.signals.generators.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.signals.generators._RPROB custom_func methodSignalFactory . from_choice_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , flex_2d = None , entry_args = None , exit_args = None , cache_args = None , entry_kwargs = None , exit_kwargs = None , cache_kwargs = None , return_cache = False , use_cache = None , ** _kwargs ) entries propertyOutput array. entries_and methodRPROB . entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries AND other . See combine_objs() . entries_or methodRPROB . entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries OR other . See combine_objs() . entries_stats methodRPROB . entries_stats ( * args , ** kwargs ) Stats of entries as signals. entries_xor methodRPROB . entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries XOR other . See combine_objs() . plot methodSignalFactory . __init__ .< locals >. plot ( _self , entry_y = None , exit_y = None , entry_types = None , exit_types = None , entry_trace_kwargs = None , exit_trace_kwargs = None , fig = None , ** kwargs ) Plot RPROB.entries and RPROB.exits . Args entry_y : array_like Y-axis values to plot entry markers on. exit_y : array_like Y-axis values to plot exit markers on. entry_types : array_like Entry types in string format. exit_types : array_like Exit types in string format. entry_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_entry_markers() for RPROB.entries . exit_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_exit_markers() for RPROB.exits . fig : Figure or FigureWidget Figure to add traces to. **kwargs Keyword arguments passed to SignalsSRAccessor.plot_as_markers() . prob_list propertyList of prob values. run class methodRPROB . run ( input_shape , prob , short_name = 'rprob' , hide_params = None , hide_default = True , ** kwargs ) Run RPROB indicator. Parameters: prob Outputs: entries Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodRPROB . run_combs ( input_shape , prob , r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple RPROB indicators using function comb_func . Parameters: prob Outputs: entries comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to RPROB.run() . RPROBCX classRPROBCX ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Random exit signal generator based on probabilities. Generates chain of new_entries and exits based on entries and rand_by_prob_choice_nb() . See RPROB for notes on parameters. Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.signals.generators.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.signals.generators._RPROBCX custom_func methodSignalFactory . from_choice_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , flex_2d = None , entry_args = None , exit_args = None , cache_args = None , entry_kwargs = None , exit_kwargs = None , cache_kwargs = None , return_cache = False , use_cache = None , ** _kwargs ) entries methodInput array. entries_and methodRPROBCX . entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries AND other . See combine_objs() . entries_or methodRPROBCX . entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries OR other . See combine_objs() . entries_stats methodRPROBCX . entries_stats ( * args , ** kwargs ) Stats of entries as signals. entries_xor methodRPROBCX . entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries XOR other . See combine_objs() . exits propertyOutput array. exits_and methodRPROBCX . exits_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits AND other . See combine_objs() . exits_or methodRPROBCX . exits_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits OR other . See combine_objs() . exits_stats methodRPROBCX . exits_stats ( * args , ** kwargs ) Stats of exits as signals. exits_xor methodRPROBCX . exits_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits XOR other . See combine_objs() . new_entries propertyOutput array. new_entries_and methodRPROBCX . new_entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return new_entries AND other . See combine_objs() . new_entries_or methodRPROBCX . new_entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return new_entries OR other . See combine_objs() . new_entries_stats methodRPROBCX . new_entries_stats ( * args , ** kwargs ) Stats of new_entries as signals. new_entries_xor methodRPROBCX . new_entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return new_entries XOR other . See combine_objs() . plot methodSignalFactory . __init__ .< locals >. plot ( _self , entry_y = None , exit_y = None , entry_types = None , exit_types = None , entry_trace_kwargs = None , exit_trace_kwargs = None , fig = None , ** kwargs ) Plot RPROBCX.new_entries and RPROBCX.exits . Args entry_y : array_like Y-axis values to plot entry markers on. exit_y : array_like Y-axis values to plot exit markers on. entry_types : array_like Entry types in string format. exit_types : array_like Exit types in string format. entry_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_entry_markers() for RPROBCX.new_entries . exit_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_exit_markers() for RPROBCX.exits . fig : Figure or FigureWidget Figure to add traces to. **kwargs Keyword arguments passed to SignalsSRAccessor.plot_as_markers() . prob_list propertyList of prob values. run class methodRPROBCX . run ( entries , prob , short_name = 'rprobcx' , hide_params = None , hide_default = True , ** kwargs ) Run RPROBCX indicator. Inputs: entries Parameters: prob Outputs: new_entries , exits Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodRPROBCX . run_combs ( entries , prob , r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple RPROBCX indicators using function comb_func . Inputs: entries Parameters: prob Outputs: new_entries , exits comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to RPROBCX.run() . RPROBNX classRPROBNX ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Random entry and exit signal generator based on probabilities. Generates entries and exits based on rand_by_prob_choice_nb() . See RPROB for notes on parameters. Usage Test all probability combinations: import vectorbt as vbt rprobnx = vbt . RPROBNX . run ( ... input_shape = ( 5 ,), ... entry_prob = [ 0.5 , 1. ], ... exit_prob = [ 0.5 , 1. ], ... param_product = True , ... seed = 42 ) rprobnx . entries rprobnx_entry_prob 0.5 0.5 1.0 0.5 rprobnx_exit_prob 0.5 1.0 0.5 1.0 0 True True True True 1 False False False False 2 False False False True 3 False False False False 4 False False True True rprobnx . exits rprobnx_entry_prob 0.5 0.5 1.0 1.0 rprobnx_exit_prob 0.5 1.0 0.5 1.0 0 False False False False 1 False True False True 2 False False False False 3 False False True True 4 True False False False Probabilities can also be set per row, column, or element: import numpy as np entry_prob1 = np . asarray ([ 1. , 0. , 1. , 0. , 1. ]) entry_prob2 = np . asarray ([ 0. , 1. , 0. , 1. , 0. ]) rprobnx = vbt . RPROBNX . run ( ... input_shape = ( 5 ,), ... entry_prob = [ entry_prob1 , entry_prob2 ], ... exit_prob = 1. , ... seed = 42 ) rprobnx . entries rprobnx_entry_prob array_0 array_1 rprobnx_exit_prob 1.0 1.0 0 True False 1 False True 2 True False 3 False True 4 True False rprobnx . exits rprobnx_entry_prob array_0 array_1 rprobnx_exit_prob 1.0 1.0 0 False False 1 True False 2 False True 3 True False 4 False True Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.signals.generators.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.signals.generators._RPROBNX custom_func methodSignalFactory . from_choice_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , flex_2d = None , entry_args = None , exit_args = None , cache_args = None , entry_kwargs = None , exit_kwargs = None , cache_kwargs = None , return_cache = False , use_cache = None , ** _kwargs ) entries propertyOutput array. entries_and methodRPROBNX . entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries AND other . See combine_objs() . entries_or methodRPROBNX . entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries OR other . See combine_objs() . entries_stats methodRPROBNX . entries_stats ( * args , ** kwargs ) Stats of entries as signals. entries_xor methodRPROBNX . entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries XOR other . See combine_objs() . entry_prob_list propertyList of entry_prob values. exit_prob_list propertyList of exit_prob values. exits propertyOutput array. exits_and methodRPROBNX . exits_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits AND other . See combine_objs() . exits_or methodRPROBNX . exits_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits OR other . See combine_objs() . exits_stats methodRPROBNX . exits_stats ( * args , ** kwargs ) Stats of exits as signals. exits_xor methodRPROBNX . exits_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits XOR other . See combine_objs() . plot methodSignalFactory . __init__ .< locals >. plot ( _self , entry_y = None , exit_y = None , entry_types = None , exit_types = None , entry_trace_kwargs = None , exit_trace_kwargs = None , fig = None , ** kwargs ) Plot RPROBNX.entries and RPROBNX.exits . Args entry_y : array_like Y-axis values to plot entry markers on. exit_y : array_like Y-axis values to plot exit markers on. entry_types : array_like Entry types in string format. exit_types : array_like Exit types in string format. entry_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_entry_markers() for RPROBNX.entries . exit_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_exit_markers() for RPROBNX.exits . fig : Figure or FigureWidget Figure to add traces to. **kwargs Keyword arguments passed to SignalsSRAccessor.plot_as_markers() . run class methodRPROBNX . run ( input_shape , entry_prob , exit_prob , short_name = 'rprobnx' , hide_params = None , hide_default = True , ** kwargs ) Run RPROBNX indicator. Parameters: entry_prob , exit_prob Outputs: entries , exits Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodRPROBNX . run_combs ( input_shape , entry_prob , exit_prob , r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple RPROBNX indicators using function comb_func . Parameters: entry_prob , exit_prob Outputs: entries , exits comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to RPROBNX.run() . RPROBX classRPROBX ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Random exit signal generator based on probabilities. Generates exits based on entries and rand_by_prob_choice_nb() . See RPROB for notes on parameters. Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.signals.generators.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.signals.generators._RPROBX custom_func methodSignalFactory . from_choice_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , flex_2d = None , entry_args = None , exit_args = None , cache_args = None , entry_kwargs = None , exit_kwargs = None , cache_kwargs = None , return_cache = False , use_cache = None , ** _kwargs ) entries methodInput array. entries_and methodRPROBX . entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries AND other . See combine_objs() . entries_or methodRPROBX . entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries OR other . See combine_objs() . entries_stats methodRPROBX . entries_stats ( * args , ** kwargs ) Stats of entries as signals. entries_xor methodRPROBX . entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries XOR other . See combine_objs() . exits propertyOutput array. exits_and methodRPROBX . exits_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits AND other . See combine_objs() . exits_or methodRPROBX . exits_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits OR other . See combine_objs() . exits_stats methodRPROBX . exits_stats ( * args , ** kwargs ) Stats of exits as signals. exits_xor methodRPROBX . exits_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits XOR other . See combine_objs() . plot methodSignalFactory . __init__ .< locals >. plot ( _self , entry_y = None , exit_y = None , entry_types = None , exit_types = None , entry_trace_kwargs = None , exit_trace_kwargs = None , fig = None , ** kwargs ) Plot RPROBX.entries and RPROBX.exits . Args entry_y : array_like Y-axis values to plot entry markers on. exit_y : array_like Y-axis values to plot exit markers on. entry_types : array_like Entry types in string format. exit_types : array_like Exit types in string format. entry_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_entry_markers() for RPROBX.entries . exit_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_exit_markers() for RPROBX.exits . fig : Figure or FigureWidget Figure to add traces to. **kwargs Keyword arguments passed to SignalsSRAccessor.plot_as_markers() . prob_list propertyList of prob values. run class methodRPROBX . run ( entries , prob , short_name = 'rprobx' , hide_params = None , hide_default = True , ** kwargs ) Run RPROBX indicator. Inputs: entries Parameters: prob Outputs: exits Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodRPROBX . run_combs ( entries , prob , r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple RPROBX indicators using function comb_func . Inputs: entries Parameters: prob Outputs: exits comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to RPROBX.run() . STCX classSTCX ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Exit signal generator based on stop values. Generates chain of new_entries and exits based on entries and stop_choice_nb() . See STX for notes on parameters. Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.signals.generators.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.signals.generators._STCX custom_func methodSignalFactory . from_choice_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , flex_2d = None , entry_args = None , exit_args = None , cache_args = None , entry_kwargs = None , exit_kwargs = None , cache_kwargs = None , return_cache = False , use_cache = None , ** _kwargs ) entries methodInput array. entries_and methodSTCX . entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries AND other . See combine_objs() . entries_or methodSTCX . entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries OR other . See combine_objs() . entries_stats methodSTCX . entries_stats ( * args , ** kwargs ) Stats of entries as signals. entries_xor methodSTCX . entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries XOR other . See combine_objs() . exits propertyOutput array. exits_and methodSTCX . exits_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits AND other . See combine_objs() . exits_or methodSTCX . exits_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits OR other . See combine_objs() . exits_stats methodSTCX . exits_stats ( * args , ** kwargs ) Stats of exits as signals. exits_xor methodSTCX . exits_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits XOR other . See combine_objs() . new_entries propertyOutput array. new_entries_and methodSTCX . new_entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return new_entries AND other . See combine_objs() . new_entries_or methodSTCX . new_entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return new_entries OR other . See combine_objs() . new_entries_stats methodSTCX . new_entries_stats ( * args , ** kwargs ) Stats of new_entries as signals. new_entries_xor methodSTCX . new_entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return new_entries XOR other . See combine_objs() . plot methodSignalFactory . __init__ .< locals >. plot ( _self , entry_y = None , exit_y = None , entry_types = None , exit_types = None , entry_trace_kwargs = None , exit_trace_kwargs = None , fig = None , ** kwargs ) Plot STCX.new_entries and STCX.exits . Args entry_y : array_like Y-axis values to plot entry markers on. exit_y : array_like Y-axis values to plot exit markers on. entry_types : array_like Entry types in string format. exit_types : array_like Exit types in string format. entry_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_entry_markers() for STCX.new_entries . exit_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_exit_markers() for STCX.exits . fig : Figure or FigureWidget Figure to add traces to. **kwargs Keyword arguments passed to SignalsSRAccessor.plot_as_markers() . run class methodSTCX . run ( entries , ts , stop , trailing = Default ( False ), short_name = 'stcx' , hide_params = None , hide_default = True , ** kwargs ) Run STCX indicator. Inputs: entries , ts Parameters: stop , trailing Outputs: new_entries , exits Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodSTCX . run_combs ( entries , ts , stop , trailing = Default ( False ), r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple STCX indicators using function comb_func . Inputs: entries , ts Parameters: stop , trailing Outputs: new_entries , exits comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to STCX.run() . stop_list propertyList of stop values. trailing_list propertyList of trailing values. ts methodInput array. ts_above methodSTCX . ts_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ts is above other . See combine_objs() . ts_below methodSTCX . ts_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ts is below other . See combine_objs() . ts_crossed_above methodSTCX . ts_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ts is crossed_above other . See combine_objs() . ts_crossed_below methodSTCX . ts_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ts is crossed_below other . See combine_objs() . ts_equal methodSTCX . ts_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ts is equal other . See combine_objs() . ts_stats methodSTCX . ts_stats ( * args , ** kwargs ) Stats of ts as generic. STX classSTX ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Exit signal generator based on stop values. Generates exits based on entries and stop_choice_nb() . Hint All parameters can be either a single value (per frame) or a NumPy array (per row, column, or element). To generate multiple combinations, pass them as lists. Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.signals.generators.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.signals.generators._STX custom_func methodSignalFactory . from_choice_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , flex_2d = None , entry_args = None , exit_args = None , cache_args = None , entry_kwargs = None , exit_kwargs = None , cache_kwargs = None , return_cache = False , use_cache = None , ** _kwargs ) entries methodInput array. entries_and methodSTX . entries_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries AND other . See combine_objs() . entries_or methodSTX . entries_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries OR other . See combine_objs() . entries_stats methodSTX . entries_stats ( * args , ** kwargs ) Stats of entries as signals. entries_xor methodSTX . entries_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return entries XOR other . See combine_objs() . exits propertyOutput array. exits_and methodSTX . exits_and ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits AND other . See combine_objs() . exits_or methodSTX . exits_or ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits OR other . See combine_objs() . exits_stats methodSTX . exits_stats ( * args , ** kwargs ) Stats of exits as signals. exits_xor methodSTX . exits_xor ( other , level_name = None , allow_multiple = True , ** kwargs ) Return exits XOR other . See combine_objs() . plot methodSignalFactory . __init__ .< locals >. plot ( _self , entry_y = None , exit_y = None , entry_types = None , exit_types = None , entry_trace_kwargs = None , exit_trace_kwargs = None , fig = None , ** kwargs ) Plot STX.entries and STX.exits . Args entry_y : array_like Y-axis values to plot entry markers on. exit_y : array_like Y-axis values to plot exit markers on. entry_types : array_like Entry types in string format. exit_types : array_like Exit types in string format. entry_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_entry_markers() for STX.entries . exit_trace_kwargs : dict Keyword arguments passed to SignalsSRAccessor.plot_as_exit_markers() for STX.exits . fig : Figure or FigureWidget Figure to add traces to. **kwargs Keyword arguments passed to SignalsSRAccessor.plot_as_markers() . run class methodSTX . run ( entries , ts , stop , trailing = Default ( False ), short_name = 'stx' , hide_params = None , hide_default = True , ** kwargs ) Run STX indicator. Inputs: entries , ts Parameters: stop , trailing Outputs: exits Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodSTX . run_combs ( entries , ts , stop , trailing = Default ( False ), r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple STX indicators using function comb_func . Inputs: entries , ts Parameters: stop , trailing Outputs: exits comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to STX.run() . stop_list propertyList of stop values. trailing_list propertyList of trailing values. ts methodInput array. ts_above methodSTX . ts_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ts is above other . See combine_objs() . ts_below methodSTX . ts_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ts is below other . See combine_objs() . ts_crossed_above methodSTX . ts_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ts is crossed_above other . See combine_objs() . ts_crossed_below methodSTX . ts_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ts is crossed_below other . See combine_objs() . ts_equal methodSTX . ts_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ts is equal other . See combine_objs() . ts_stats methodSTX . ts_stats ( * args , ** kwargs ) Stats of ts as generic.