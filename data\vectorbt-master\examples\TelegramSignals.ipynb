{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["In this example, we will build a Telegram bot that sends a signal once any Bollinger Band has been crossed. We will periodically query for the latest OHLCV data of the selected cryptocurrencies and append this data to our data pool. Additionally to receiving signals, any Telegram user can join the group and ask the bot to provide him with the current information. If the price change is higher than some number of standard deviations from the mean, while crossing the band, the bot sends a funny GIF."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import vectorbt as vbt\n", "import logging"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Telegram\n", "vbt.settings.messaging['telegram']['token'] = \"YOUR_TOKEN\"\n", "\n", "# Giphy\n", "vbt.settings.messaging['giphy']['api_key'] = \"YOUR_API_KEY\"\n", "\n", "# Data\n", "SYMBOLS = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT']\n", "START = '1 hour ago UTC'\n", "TIMEFRAME = '1m'\n", "UPDATE_EVERY = vbt.utils.datetime.interval_to_ms(TIMEFRAME) // 1000  # in seconds\n", "DT_FORMAT = '%d %b %Y %H:%M:%S %z'\n", "IND_PARAMS = dict(\n", "    timeperiod=20, \n", "    nbdevup=2, \n", "    nbdevdn=2\n", ")\n", "CHANGE_NBDEV = 2"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2021-03-23 17:07:34.598000+00:00 - 2021-03-23 18:07:00+00:00: : 1it [00:00,  1.91it/s]\n", "2021-03-23 17:07:36.639000+00:00 - 2021-03-23 18:07:00+00:00: : 1it [00:00,  2.26it/s]\n", "2021-03-23 17:07:38.885000+00:00 - 2021-03-23 18:07:00+00:00: : 1it [00:00,  1.80it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["DatetimeIndex(['2021-03-23 17:08:00+00:00', '2021-03-23 17:09:00+00:00',\n", "               '2021-03-23 17:10:00+00:00', '2021-03-23 17:11:00+00:00',\n", "               '2021-03-23 17:12:00+00:00', '2021-03-23 17:13:00+00:00',\n", "               '2021-03-23 17:14:00+00:00', '2021-03-23 17:15:00+00:00',\n", "               '2021-03-23 17:16:00+00:00', '2021-03-23 17:17:00+00:00',\n", "               '2021-03-23 17:18:00+00:00', '2021-03-23 17:19:00+00:00',\n", "               '2021-03-23 17:20:00+00:00', '2021-03-23 17:21:00+00:00',\n", "               '2021-03-23 17:22:00+00:00', '2021-03-23 17:23:00+00:00',\n", "               '2021-03-23 17:24:00+00:00', '2021-03-23 17:25:00+00:00',\n", "               '2021-03-23 17:26:00+00:00', '2021-03-23 17:27:00+00:00',\n", "               '2021-03-23 17:28:00+00:00', '2021-03-23 17:29:00+00:00',\n", "               '2021-03-23 17:30:00+00:00', '2021-03-23 17:31:00+00:00',\n", "               '2021-03-23 17:32:00+00:00', '2021-03-23 17:33:00+00:00',\n", "               '2021-03-23 17:34:00+00:00', '2021-03-23 17:35:00+00:00',\n", "               '2021-03-23 17:36:00+00:00', '2021-03-23 17:37:00+00:00',\n", "               '2021-03-23 17:38:00+00:00', '2021-03-23 17:39:00+00:00',\n", "               '2021-03-23 17:40:00+00:00', '2021-03-23 17:41:00+00:00',\n", "               '2021-03-23 17:42:00+00:00', '2021-03-23 17:43:00+00:00',\n", "               '2021-03-23 17:44:00+00:00', '2021-03-23 17:45:00+00:00',\n", "               '2021-03-23 17:46:00+00:00', '2021-03-23 17:47:00+00:00',\n", "               '2021-03-23 17:48:00+00:00', '2021-03-23 17:49:00+00:00',\n", "               '2021-03-23 17:50:00+00:00', '2021-03-23 17:51:00+00:00',\n", "               '2021-03-23 17:52:00+00:00', '2021-03-23 17:53:00+00:00',\n", "               '2021-03-23 17:54:00+00:00', '2021-03-23 17:55:00+00:00',\n", "               '2021-03-23 17:56:00+00:00', '2021-03-23 17:57:00+00:00',\n", "               '2021-03-23 17:58:00+00:00', '2021-03-23 17:59:00+00:00',\n", "               '2021-03-23 18:00:00+00:00', '2021-03-23 18:01:00+00:00',\n", "               '2021-03-23 18:02:00+00:00', '2021-03-23 18:03:00+00:00',\n", "               '2021-03-23 18:04:00+00:00', '2021-03-23 18:05:00+00:00',\n", "               '2021-03-23 18:06:00+00:00', '2021-03-23 18:07:00+00:00'],\n", "              dtype='datetime64[ns, UTC]', name='Open time', freq='T')\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["data = vbt.CCXTData.download(SYMBOLS, start=START, timeframe=TIMEFRAME)\n", "\n", "print(data.wrapper.index)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def get_bbands(data):\n", "    return vbt.IndicatorFactory.from_talib('BBANDS').run(\n", "        data.get('Close'), **IND_PARAMS, hide_params=list(IND_PARAMS.keys()))\n", "\n", "\n", "def get_info(bbands):\n", "    info = dict()\n", "    info['last_price'] = bbands.close.iloc[-1]\n", "    info['last_change'] = (bbands.close.iloc[-1] - bbands.close.iloc[-2]) / bbands.close.iloc[-1]\n", "    info['last_crossed_above_upper'] = bbands.close_crossed_above(bbands.upperband).iloc[-1]\n", "    info['last_crossed_below_upper'] = bbands.close_crossed_below(bbands.upperband).iloc[-1]\n", "    info['last_crossed_below_lower'] = bbands.close_crossed_below(bbands.lowerband).iloc[-1]\n", "    info['last_crossed_above_lower'] = bbands.close_crossed_above(bbands.lowerband).iloc[-1]\n", "    info['bw'] = (bbands.upperband - bbands.lowerband) / bbands.middleband\n", "    info['last_bw_zscore'] = info['bw'].vbt.zscore().iloc[-1]\n", "    info['last_change_zscore'] = bbands.close.vbt.pct_change().vbt.zscore().iloc[-1]\n", "    info['last_change_pos'] = info['last_change_zscore'] >= CHANGE_NBDEV\n", "    info['last_change_neg'] = info['last_change_zscore'] <= -CHANGE_NBDEV\n", "    return info\n", "\n", "\n", "def format_symbol_info(symbol, info):\n", "    last_change = info['last_change'][symbol]\n", "    last_price = info['last_price'][symbol]\n", "    last_bw_zscore = info['last_bw_zscore'][symbol]\n", "    return \"{} ({:.2%}, {}, {:.2f})\".format(symbol, last_change, last_price, last_bw_zscore)\n", "\n", "\n", "def format_signals_info(emoji, signals, info):\n", "    symbols = signals.index[signals]\n", "    symbol_msgs = []\n", "    for symbol in symbols:\n", "        symbol_msgs.append(format_symbol_info(symbol, info))\n", "    return \"{} {}\".format(emoji, ', '.join(symbol_msgs))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from telegram.ext import CommandHandler\n", "\n", "class MyTelegramBot(vbt.TelegramBot):\n", "    def __init__(self, data, *args, **kwargs):\n", "        super().__init__(data=data, *args, **kwargs)\n", "        \n", "        self.data = data\n", "        self.update_ts = data.wrapper.index[-1]\n", "        \n", "    @property\n", "    def custom_handlers(self):\n", "        return (<PERSON><PERSON><PERSON><PERSON>('info', self.info_callback),)\n", "    \n", "    def info_callback(self, update, context):\n", "        chat_id = update.effective_chat.id\n", "        if len(context.args) != 1:\n", "            self.send_message(chat_id, \"Please provide one symbol.\")\n", "            return\n", "        symbol = context.args[0]\n", "        if symbol not in SYMBOLS:\n", "            self.send_message(chat_id, f\"There is no such symbol as \\\"{symbol}\\\".\")\n", "            return\n", "            \n", "        bbands = get_bbands(self.data)\n", "        info = get_info(bbands)\n", "        messages = [format_symbol_info(symbol, info)]\n", "        message = '\\n'.join([\"{}:\".format(self.update_ts.strftime(DT_FORMAT))] + messages)\n", "        self.send_message(chat_id, message)\n", "        \n", "    @property\n", "    def start_message(self):\n", "        index = self.data.wrapper.index\n", "        return f\"\"\"Hello! \n", "\n", "Starting with {len(index)} rows from {index[0].strftime(DT_FORMAT)} to {index[-1].strftime(DT_FORMAT)}.\"\"\"\n", "        \n", "    @property\n", "    def help_message(self):\n", "        return \"\"\"Message format:\n", "[event] [symbol] ([price change], [new price], [bandwidth z-score])\n", "    \n", "Event legend:\n", "⬆️ - Price went above upper band\n", "⤵️ - Price retraced below upper band\n", "⬇️ - Price went below lower band\n", "⤴️ - Price retraced above lower band\n", "\n", "GIF is sent once a band is crossed and the price change is 2 stds from the mean.\"\"\""]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2021-03-23 19:07:41,319 - vectorbt.utils.messaging - INFO - Initializing bot\n", "2021-03-23 19:07:41,325 - vectorbt.utils.messaging - INFO - Loaded chat ids [447924619]\n", "2021-03-23 19:07:41,444 - vectorbt.utils.messaging - INFO - Running bot vectorbt_bot\n", "2021-03-23 19:07:41,461 - apscheduler.scheduler - INFO - Scheduler started\n", "2021-03-23 19:07:41,629 - vectorbt.utils.messaging - INFO - 447924619 - <PERSON><PERSON>: \"I'm back online!\"\n"]}], "source": ["telegram_bot = MyTelegramBot(data)\n", "telegram_bot.start(in_background=True)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["class MyDataUpdater(vbt.DataUpdater):\n", "    def __init__(self, data, telegram_bot, **kwargs):\n", "        super().__init__(data, telegram_bot=telegram_bot, **kwargs)\n", "        \n", "        self.telegram_bot = telegram_bot\n", "        self.update_ts = data.wrapper.index[-1]\n", "        \n", "    def update(self):\n", "        super().update()\n", "        self.update_ts = pd.Timestamp.now(tz=TZ_CONVERT)\n", "        self.telegram_bot.data = self.data\n", "        self.telegram_bot.update_ts = self.update_ts\n", "        \n", "        bbands = get_bbands(self.data)\n", "        info = get_info(bbands)\n", "        \n", "        messages = []\n", "        if info['last_crossed_above_upper'].any():\n", "            messages.append(format_signals_info('⬆️', info['last_crossed_above_upper'], info))\n", "        if info['last_crossed_below_upper'].any():\n", "            messages.append(format_signals_info('⤵️', info['last_crossed_below_upper'], info))\n", "        if info['last_crossed_below_lower'].any():\n", "            messages.append(format_signals_info('⬇️', info['last_crossed_below_lower'], info))\n", "        if info['last_crossed_above_lower'].any():\n", "            messages.append(format_signals_info('⤴️', info['last_crossed_above_lower'], info))\n", "            \n", "        if len(messages) > 0:\n", "            message = '\\n'.join([\"{}:\".format(self.update_ts.strftime(DT_FORMAT))] + messages)\n", "            self.telegram_bot.send_message_to_all(message)\n", "        if (info['last_crossed_above_upper'] & info['last_change_pos']).any():\n", "            self.telegram_bot.send_giphy_to_all(\"launch\")\n", "        if (info['last_crossed_below_lower'] & info['last_change_neg']).any():\n", "            self.telegram_bot.send_giphy_to_all(\"fall\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2021-03-23 19:07:41,670 - vectorbt.utils.schedule - INFO - Starting schedule manager with jobs [Every 60 seconds do update() (last run: [never], next run: 2021-03-23 19:08:41)]\n", "2021-03-23 19:08:47,689 - vectorbt.data.updater - INFO - Updated data has 61 rows from 2021-03-23 17:08:00+00:00 to 2021-03-23 18:08:00+00:00\n", "2021-03-23 19:08:47,822 - numexpr.utils - INFO - NumExpr defaulting to 4 threads.\n", "2021-03-23 19:09:15,117 - vectorbt.utils.messaging - INFO - 447924619 - User: \"/info BTC/USDT\"\n", "2021-03-23 19:09:15,281 - vectorbt.utils.messaging - INFO - 447924619 - Bot: \"23 Mar 2021 18:08:47 +0000:\n", "BTC/USDT (0.08%, 55638.07, -1.06)\"\n", "2021-03-23 19:09:54,766 - vectorbt.data.updater - INFO - Updated data has 62 rows from 2021-03-23 17:08:00+00:00 to 2021-03-23 18:09:00+00:00\n", "2021-03-23 19:10:08,781 - vectorbt.utils.schedule - INFO - Stopping schedule manager\n"]}], "source": ["data_updater = MyDataUpdater(data, telegram_bot)\n", "data_updater.update_every(UPDATE_EVERY)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2021-03-23 19:10:08,791 - vectorbt.utils.messaging - INFO - Stopping bot\n", "2021-03-23 19:10:08,793 - apscheduler.scheduler - INFO - Scheduler has been shut down\n"]}], "source": ["telegram_bot.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 4}