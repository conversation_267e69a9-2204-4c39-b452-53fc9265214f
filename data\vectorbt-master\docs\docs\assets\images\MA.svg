<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="350" style="" viewBox="0 0 700 350"><rect x="0" y="0" width="700" height="350" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-693699"><g class="clips"><clipPath id="clip693699xyplot" class="plotclip"><rect width="633" height="274"/></clipPath><clipPath class="axesclip" id="clip693699x"><rect x="37" y="0" width="633" height="350"/></clipPath><clipPath class="axesclip" id="clip693699y"><rect x="0" y="46" width="700" height="274"/></clipPath><clipPath class="axesclip" id="clip693699xy"><rect x="37" y="46" width="633" height="274"/></clipPath></g><g class="gradients"/></defs><g class="bglayer"><rect class="bg" x="37" y="46" width="633" height="274" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(40.44,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(147.09,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(250.29,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(356.94,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(460.15,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(566.79,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,299.95)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,246.65)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,193.36)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,140.07)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,86.78)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"/><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(37, 46)" clip-path="url('#clip693699xyplot')"><g class="scatterlayer mlayer"><g class="trace scatter trace6202ec33-56e0-434d-9f44-405091289f60" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,257.82L10.32,258.02L13.76,260.3L17.2,256.71L20.64,256.51L27.52,256.58L30.96,254.92L34.4,255.24L37.84,256.47L48.16,255.96L51.6,254.99L55.04,252.65L58.48,253.27L61.92,253.08L65.36,252.05L68.8,251.62L72.24,253.17L82.57,253.36L86.01,254.93L89.45,254.34L92.89,251.63L96.33,252.11L99.77,251.33L106.65,251.14L110.09,249.73L113.53,230.5L116.97,228.02L120.41,229.36L123.85,226.32L127.29,225.71L130.73,222L134.17,219.58L137.61,221.84L141.05,218.65L144.49,225.58L151.37,224.73L154.81,222.83L158.25,225.51L161.69,221.02L165.13,220.59L168.57,219.35L172.01,219.21L175.45,218.3L178.89,218.92L182.33,216.66L185.77,212.05L189.21,214.91L192.65,221.69L196.09,219.86L206.41,220.71L209.85,217.96L213.29,216.57L216.73,213.84L220.17,206.83L223.61,205.15L230.49,207.4L233.93,205.2L240.82,196L244.26,190.56L247.7,168.55L251.14,174.74L254.58,152.29L258.02,147.51L261.46,141.9L264.9,150.43L268.34,164.85L271.78,166.78L275.22,142.09L278.66,147.94L282.1,148.34L285.54,155.89L288.98,150.51L292.42,147.7L295.86,145.96L299.3,129.42L302.74,125.89L306.18,128.18L309.62,129.79L313.06,138.85L316.5,132.05L319.94,132.33L323.38,127.57L326.82,141.79L330.26,155.15L333.7,152.05L337.14,152.1L340.58,146.19L344.02,148.58L347.46,155.67L350.9,147.35L354.34,149.29L357.78,143.48L361.22,141.21L364.66,128.87L368.1,125.02L371.54,120.86L374.98,112.18L378.42,118.54L381.86,113.43L385.3,106.67L388.74,90.22L392.18,75.37L395.63,71.28L399.07,67.13L402.51,46.35L405.95,13.7L409.39,62.55L412.83,29.92L416.27,41.86L419.71,72.3L423.15,78.53L426.59,72.71L430.03,41.81L433.47,61.68L436.91,68L443.79,55.41L447.23,33.16L450.67,25.49L454.11,36.61L457.55,57.87L460.99,45.68L464.43,56.97L467.87,87.25L471.31,70.22L474.75,107.99L478.19,102.23L481.63,76.31L485.07,79.93L488.51,73.63L491.95,78.11L495.39,84.93L498.83,96.71L502.27,99.08L505.71,96.42L509.15,97.53L512.59,107.99L516.03,105.98L519.47,106.88L522.91,104.53L526.35,91.79L529.79,83.42L533.23,80.26L536.67,72.17L540.11,68.22L543.55,45.96L546.99,54.68L550.43,42.32L553.88,41.67L557.32,44.43L560.76,57.99L564.2,53.47L567.64,57.23L571.08,70.2L574.52,92.69L577.96,85.77L581.4,84.1L584.84,87.9L588.28,84.86L591.72,69.66L595.16,73.73L598.6,90.39L602.04,90.58L605.48,83.2L608.92,89.81L612.36,90.38L615.8,84.19L619.24,89.13L622.68,100.61L626.12,107.12L629.56,104.78L633,103.91" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter tracec160323c-bffe-41b3-8375-d90236cab0c7" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M30.96,257.24L89.45,253.21L92.89,253.05L110.09,252.27L113.53,250.02L130.73,236.52L134.17,233.35L144.49,224.76L147.93,224.2L172.01,222.24L175.45,222.2L182.33,220.71L185.77,219.44L192.65,218.27L196.09,218.15L216.73,217.74L220.17,217.22L227.05,214.69L230.49,213.44L237.38,210.09L240.82,207.62L244.26,204.88L247.7,200.08L251.14,196.17L254.58,190.71L264.9,172.83L268.34,168.8L271.78,165.36L275.22,159.97L278.66,155.71L282.1,153.69L285.54,151.8L288.98,151.62L295.86,152.05L299.3,149.95L306.18,142.19L309.62,140.96L316.5,138.42L319.94,136.07L323.38,133.77L326.82,133.18L330.26,134.1L333.7,136.37L340.58,140.79L344.02,142.67L357.78,149.17L361.22,149.11L371.54,140.65L374.98,137.25L378.42,134.25L381.86,130.02L385.3,125.96L388.74,120.05L402.51,92.2L405.95,81.49L409.39,76.52L412.83,67.66L416.27,60.51L419.71,57.07L423.15,55.9L426.59,55.63L430.03,52.69L433.47,52.14L436.91,54.31L440.35,59.12L443.79,58.41L447.23,58.73L450.67,57.1L454.11,53.53L460.99,48.76L464.43,50.27L467.87,52.83L471.31,53.05L488.51,75.81L491.95,77.83L498.83,85.73L502.27,86.91L505.71,89.53L509.15,88.49L512.59,89.06L516.03,92.03L522.91,97.82L526.35,99.18L529.79,99.03L533.23,97.39L540.11,91.88L543.55,86.72L550.43,75.02L553.88,68.5L557.32,62.49L560.76,59.11L567.64,53.82L571.08,53.62L574.52,56.07L577.96,60.05L581.4,62.99L584.84,67.55L588.28,71.86L591.72,74.39L595.16,75.96L598.6,79.65L602.04,82.99L605.48,84.29L612.36,84.46L615.8,84.47L619.24,84.59L622.68,86.17L629.56,93.02L633,94.37" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(40.44,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Mar 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(147.09,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Apr 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(250.29,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">May 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(356.94,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jun 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(460.15,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jul 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(566.79,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Aug 2019</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,299.95)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">4k</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,246.65)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">6k</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,193.36)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">8k</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,140.07)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">10k</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,86.78)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">12k</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-693699"><g class="clips"/><clipPath id="legend693699"><rect width="141" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(529, 11.519999999999996)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="141" height="29" x="0" y="0"/><g class="scrollbox" transform="translate(0, 0)" clip-path="url('#legend693699')"><g class="groups"><g class="traces" transform="translate(0, 14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Close</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="74.859375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(77.359375, 14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">MA</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="60.828125" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-ytitle"/></g></svg>