<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="350" style="" viewBox="0 0 700 350"><rect x="0" y="0" width="700" height="350" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-b8132f"><g class="clips"><clipPath id="clipb8132fxyplot" class="plotclip"><rect width="637" height="274"/></clipPath><clipPath class="axesclip" id="clipb8132fx"><rect x="33" y="0" width="637" height="350"/></clipPath><clipPath class="axesclip" id="clipb8132fy"><rect x="0" y="46" width="700" height="274"/></clipPath><clipPath class="axesclip" id="clipb8132fxy"><rect x="33" y="46" width="637" height="274"/></clipPath></g><g class="gradients"/></defs><g class="bglayer"><rect class="bg" x="33" y="46" width="637" height="274" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(161.69,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(290.37,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(419.06,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(547.75,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,288.3)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,236.58)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,184.86)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,133.14)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,81.43)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="xzl zl crisp" transform="translate(33,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(33, 46)" clip-path="url('#clipb8132fxyplot')"><g class="scatterlayer mlayer"><g class="trace scatter traced0767399-cf49-4929-821b-6562a2fb5618" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,185.98L6.43,192.03L12.87,185.99L19.3,173.82L25.74,180.74L45.04,174.23L51.47,181.85L57.91,169.74L64.34,171.22L70.78,161.59L77.21,155.86L83.65,161.8L90.08,152.78L96.52,155.85L102.95,144.67L109.38,142.2L115.82,130.48L122.25,124.34L128.69,117.03L135.12,116.98L141.56,101.83L147.99,96.83L154.42,99.51L160.86,95.79L167.29,112.78L173.73,119.46L180.16,114.18L186.6,121.21L193.03,117.34L199.46,119.7L205.9,131.71L212.33,138.12L218.77,135.94L231.64,130.74L238.07,125.87L244.51,120.95L250.94,123.2L257.37,110.27L263.81,114.76L270.24,116.91L276.68,103.88L283.11,93.31L289.55,86.06L295.98,100.57L302.41,85.95L308.85,78.17L315.28,59.66L321.72,73.31L328.15,59.48L334.59,72.63L341.02,68.28L347.45,82.61L353.89,69.85L360.32,58.19L366.76,55.49L373.19,59.15L379.63,75.95L386.06,68.58L392.49,70.35L398.93,61.94L405.36,47.75L411.8,28.66L418.23,13.7L424.67,34.96L431.1,40.76L437.54,31.37L443.97,45.09L450.4,44.24L456.84,62.29L463.27,73.89L469.71,91.96L476.14,81.47L482.58,91.62L489.01,97.15L495.44,82.08L501.88,74.58L508.31,92.11L514.75,104.09L521.18,99.89L527.62,97.2L534.05,106.42L540.48,91.54L546.92,87.46L553.35,86.18L559.79,82.91L566.22,74.48L572.66,81.53L579.09,85.27L585.53,95.83L591.96,106.92L598.39,91.71L604.83,83.15L611.26,83.5L624.13,102.2L630.57,117.54L637,119.71" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(127, 127, 127); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter traced9213816-a662-48d6-b618-24fc2f10ab9e" style="stroke-miterlimit: 2;"><g class="fills"><g><path class="js-fill" d="M0,189.32L6.43,183.72L12.87,181L19.3,179.79L25.74,181.85L32.17,177.95L38.61,179.64L45.04,169.08L51.47,156.1L57.91,159.51L64.34,151.06L70.78,150.2L77.21,148.17L83.65,135.36L90.08,148.82L102.95,175.03L109.38,165.92L115.82,158.04L122.25,147.27L128.69,132.82L135.12,123.35L141.56,124.61L147.99,115.5L154.42,128.24L160.86,123.75L167.29,135.35L173.73,121.4L180.16,120.68L186.6,123.49L193.03,131.16L199.46,122.43L205.9,123.87L212.33,121.64L218.77,137.4L225.2,133.73L238.07,126.47L244.51,112.15L250.94,106.02L257.37,110.84L263.81,112.97L270.24,106.33L276.68,121.41L283.11,115.94L289.55,110.25L295.98,120.07L302.41,132.28L308.85,138.13L315.28,142.37L321.72,140.21L328.15,142.11L334.59,127.12L341.02,139.93L347.45,148.93L353.89,159.1L360.32,154.66L366.76,161.93L373.19,162.9L379.63,170.21L386.06,179.73L392.49,190.22L398.93,186.18L405.36,190.58L411.8,190.58L456.84,190.58L463.27,188.38L469.71,185.65L476.14,179.34L482.58,190.58L489.01,190.58L637,190.58L637,190.58L0,190.58Z" style="fill: rgb(0, 128, 0); fill-opacity: 0.3; stroke-width: 0;"/></g></g><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,190.58L637,190.58" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter trace3f6c8f3a-42fa-417f-b186-f5af4ce00797" style="stroke-miterlimit: 2;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,189.32L6.43,183.72L12.87,181L19.3,179.79L25.74,181.85L32.17,177.95L38.61,179.64L45.04,169.08L51.47,156.1L57.91,159.51L64.34,151.06L70.78,150.2L77.21,148.17L83.65,135.36L90.08,148.82L102.95,175.03L109.38,165.92L115.82,158.04L122.25,147.27L128.69,132.82L135.12,123.35L141.56,124.61L147.99,115.5L154.42,128.24L160.86,123.75L167.29,135.35L173.73,121.4L180.16,120.68L186.6,123.49L193.03,131.16L199.46,122.43L205.9,123.87L212.33,121.64L218.77,137.4L225.2,133.73L238.07,126.47L244.51,112.15L250.94,106.02L257.37,110.84L263.81,112.97L270.24,106.33L276.68,121.41L283.11,115.94L289.55,110.25L295.98,120.07L302.41,132.28L308.85,138.13L315.28,142.37L321.72,140.21L328.15,142.11L334.59,127.12L341.02,139.93L347.45,148.93L353.89,159.1L360.32,154.66L366.76,161.93L373.19,162.9L379.63,170.21L386.06,179.73L392.49,190.22L398.93,186.18L405.36,190.58L411.8,190.58L456.84,190.58L463.27,188.38L469.71,185.65L476.14,179.34L482.58,190.58L489.01,190.58L637,190.58" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter trace7aca652a-7fbf-4e60-a5e4-a265060d96a3" style="stroke-miterlimit: 2;"><g class="fills"><g><path class="js-fill" d="M0,190.58L398.93,190.58L405.36,195.69L411.8,203.38L418.23,206.61L424.67,198.82L431.1,208.91L437.54,200.79L443.97,210.82L450.4,199.46L456.84,200.25L463.27,190.58L469.71,190.58L476.14,190.58L482.58,191.77L489.01,197.37L495.44,206.93L501.88,211.87L508.31,220.91L514.75,225.07L521.18,226.99L527.62,236.67L534.05,232.58L540.48,231.14L546.92,236.26L553.35,235.76L559.79,244.43L566.22,242.87L572.66,234.02L579.09,237.92L585.53,234.38L591.96,242.29L598.39,237.82L604.83,242.27L611.26,248.82L617.7,247.09L624.13,256.79L630.57,250.46L637,260.3L637,190.58L0,190.58Z" style="fill: rgb(255, 0, 0); fill-opacity: 0.3; stroke-width: 0;"/></g></g><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,190.58L637,190.58" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter trace2851af25-4f80-42b7-8269-586c9b60911c" style="stroke-miterlimit: 2;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,190.58L398.93,190.58L405.36,195.69L411.8,203.38L418.23,206.61L424.67,198.82L431.1,208.91L437.54,200.79L443.97,210.82L450.4,199.46L456.84,200.25L463.27,190.58L469.71,190.58L476.14,190.58L482.58,191.77L489.01,197.37L495.44,206.93L501.88,211.87L508.31,220.91L514.75,225.07L521.18,226.99L527.62,236.67L534.05,232.58L540.48,231.14L546.92,236.26L553.35,235.76L559.79,244.43L566.22,242.87L572.66,234.02L579.09,237.92L585.53,234.38L591.96,242.29L598.39,237.82L604.83,242.27L611.26,248.82L617.7,247.09L624.13,256.79L630.57,250.46L637,260.3" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter trace16eefa0d-885d-423e-b3da-2b5dd4fe57b2" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,189.32L6.43,183.72L12.87,181L19.3,179.79L25.74,181.85L32.17,177.95L38.61,179.64L45.04,169.08L51.47,156.1L57.91,159.51L64.34,151.06L70.78,150.2L77.21,148.17L83.65,135.36L90.08,148.82L102.95,175.03L109.38,165.92L115.82,158.04L122.25,147.27L128.69,132.82L135.12,123.35L141.56,124.61L147.99,115.5L154.42,128.24L160.86,123.75L167.29,135.35L173.73,121.4L180.16,120.68L186.6,123.49L193.03,131.16L199.46,122.43L205.9,123.87L212.33,121.64L218.77,137.4L225.2,133.73L238.07,126.47L244.51,112.15L250.94,106.02L257.37,110.84L263.81,112.97L270.24,106.33L276.68,121.41L283.11,115.94L289.55,110.25L295.98,120.07L302.41,132.28L308.85,138.13L315.28,142.37L321.72,140.21L328.15,142.11L334.59,127.12L341.02,139.93L347.45,148.93L353.89,159.1L360.32,154.66L366.76,161.93L373.19,162.9L379.63,170.21L386.06,179.73L392.49,190.22L398.93,186.18L405.36,195.69L411.8,203.38L418.23,206.61L424.67,198.82L431.1,208.91L437.54,200.79L443.97,210.82L450.4,199.46L456.84,200.25L463.27,188.38L469.71,185.65L476.14,179.34L482.58,191.77L489.01,197.37L495.44,206.93L501.88,211.87L508.31,220.91L514.75,225.07L521.18,226.99L527.62,236.67L534.05,232.58L540.48,231.14L546.92,236.26L553.35,235.76L559.79,244.43L566.22,242.87L572.66,234.02L579.09,237.92L585.53,234.38L591.96,242.29L598.39,237.82L604.83,242.27L611.26,248.82L617.7,247.09L624.13,256.79L630.57,250.46L637,260.3" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(148, 103, 189); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter tracecc715a8b-aa22-4583-8df1-36d9dd17dd05" style="stroke-miterlimit: 2; opacity: 0;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,190.58L637,190.58" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;"/></g><g class="points"/><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(33,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(161.69,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">20</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(290.37,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">40</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(419.06,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">60</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(547.75,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">80</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,288.3)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0.8</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,236.58)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">1</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,184.86)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">1.2</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,133.14)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">1.4</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,81.43)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">1.6</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-b8132f"><g class="clips"/><clipPath id="legendb8132f"><rect width="113" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"><path data-index="0" fill-rule="evenodd" d="M33,236.58L670,236.58" clip-path="url('#clipb8132fy')" style="opacity: 1; stroke: rgb(128, 128, 128); stroke-opacity: 1; fill: rgb(0, 0, 0); fill-opacity: 0; stroke-dasharray: 9px, 9px; stroke-width: 2px;"/></g></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(557, 11.519999999999996)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="113" height="29" x="0" y="0"/><g class="scrollbox" transform="translate(0, 0)" clip-path="url('#legendb8132f')"><g class="groups"><g class="traces" transform="translate(0, 14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Benchmark</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(127, 127, 127); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="110.421875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-ytitle"/></g></svg>