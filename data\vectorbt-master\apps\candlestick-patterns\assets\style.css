:root {
    --bgcolor: #272a32;
    --dark_bgcolor: #1d2026;
    --fontcolor: #9fa6b7;
    --dark_fontcolor: #7b7d8d;
    --gridcolor: #323b56;
    --loadcolor: #387c9e;
    --active_color: #88ccee;
}

.container.scalable {
    width: 100%;
    max-width: none;
}

/* Remove Undo
–––––––––––––––––––––––––––––––––––––––––––––––––– */
._dash-undo-redo {
    display: none;
}

body {
    background-color: var(--dark_bgcolor);
    color: var(--fontcolor);
    overflow-x: hidden;
    padding: 15px 30px;
    margin: 0px;
}

button, .button {
    background-color: var(--dark_bgcolor) !important;
    color: var(--dark_fontcolor) !important;
    border: 1px solid dimgrey !important;
}

button:hover, .button:hover {
    color: var(--active_color) !important;
    border: 1px solid var(--active_color) !important;
}

.banner {
    height: 5rem;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: row nowrap;
    align-items: center;
    justify-content: space-between;
}

.banner h6 {
    font-family: 'Open Sans', sans-serif;
    font-size: 2rem;
    font-weight: 600;
    line-height: 1;
    text-transform: uppercase;
}

.control-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 10px;
}

.half-width {
    width: 45%;
}

.full-width {
    width: 100%;
}

*.Select-control,
Select--single,
.Select-multi-value-wrapper,
.Select-value,
.Select-value-label,
.Select-placeholder,
.Select-multi-value-wrapper,
.Select-input input,
.input-control {
    background-color: var(--dark_bgcolor) !important;
    color: var(--dark_fontcolor) !important;
}

.Select-control,
.input-control {
    border: 1px solid dimgrey !important;
}

.Select.is-focused .Select-input input {
    color: var(--active_color) !important;
}

.Select.is-focused .Select-value .Select-value-label {
    color: var(--active_color) !important;
}

.Select-menu-outer,
.Select.is-focused .Select-control,
.input-control:focus {
    color: var(--active_color) !important;
    border: 1px solid var(--active_color) !important;
}

.input-control {
    width: 100%;
    height: 36px;
}

span.Select-clear-zone {
    display: none !important;
}

.Select-menu-outer,
.Select-menu-outer div,
.Select--multi {
    background-color: var(--dark_bgcolor) !important;
    color: var(--dark_fontcolor) !important;
}

.Select-menu-outer div:hover {
    color: var(--active_color) !important;
    cursor: pointer;
}

label, small {
    color: var(--dark_fontcolor);
}

.control-outer {
    display: inline-block;
}

.section-title {
    font-size: 16px;
    font-weight: bold;
    display: block;
    margin-top: 20px;
    margin-bottom: 5px;
    cursor: pointer;
}
.section-title:focus {
    outline: none;
}

.app__left__section {
    min-height: 100vh;
    max-height: 100vh;
}

.app__right__section {
    min-height: 100vh;
    max-height: 100vh;
}

.pretty-container {
    margin: 15px 0px;
    padding: 25px;
    background-color: var(--bgcolor);
    border-radius: 5px;
    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
}

#yfinance_checklist {
    color: var(--dark_fontcolor);
}

.rc-slider {
    margin-top: 10px;
}

.rc-slider-rail {
    background-color: var(--dark_bgcolor) !important
}

.rc-slider-track {
    background-color: var(--loadcolor) !important
}

.rc-slider-dot-active, .rc-slider-handle {
    border-color: var(--active_color) !important;
    background-color: var(--active_color);
}

.rc-slider-handle {
    border: none !important;
}

.column-1 {
    padding-right: 15px !important;
}

.dash-cell.column-2 .input-active,
.dash-cell.column-3 .input-active {
    color: var(--active_color) !important;
}

.dash-spreadsheet-inner .row {
    margin: 0px;
}

