_settings moduleGlobal settings. settings config is also accessible via vectorbt.settings . Here are the main properties of the settings config: It's a nested config, that is, a config that consists of multiple sub-configs. one per sub-package (e.g., 'data'), module (e.g., 'array_wrapper'), or even class (e.g., 'configured'). Each sub-config may consist of other sub-configs. It has frozen keys - you cannot add other sub-configs or remove the existing ones, but you can modify them. Each sub-config can either inherit the properties of the parent one by using dict or overwrite them by using its own Config . The main reason for defining an own config is to allow adding new keys (e.g., 'plotting.layout'). For example, you can change default width and height of each plot: import vectorbt as vbt vbt . settings [ 'plotting' ][ 'layout' ][ 'width' ] = 800 vbt . settings [ 'plotting' ][ 'layout' ][ 'height' ] = 400 The main sub-configs such as for plotting can be also accessed/modified using the dot notation: Some sub-configs allow the dot notation too but this depends whether they inherit the rules of the root config. Since this is only visible when looking at the source code, the advice is to always use the bracket notation. Note Any change takes effect immediately. But whether its reflected immediately depends upon the place that accesses the settings. For example, changing 'array_wrapper.freq` has an immediate effect because the value is resolved every time ArrayWrapper.freq is called. On the other hand, changing 'portfolio.fillna_close' has only effect on Portfolio instances created in the future, not the existing ones, because the value is resolved upon the construction. But mostly you can still force-update the default value by replacing the instance using Configured.replace() . All places in vectorbt import settings from settings , not from vectorbt . Overwriting vectorbt.settings only overwrites the reference created for the user. Consider updating the settings config instead of replacing it. SavingLike any other class subclassing Config , we can save settings to the disk, load it back, and update in-place: vbt . settings . save ( 'my_settings' ) vbt . settings [ 'caching' ][ 'enabled' ] = False vbt . settings [ 'caching' ][ 'enabled' ] False vbt . settings . load_update ( 'my_settings' ) # load() would return a new object! vbt . settings [ 'caching' ][ 'enabled' ] True Bonus: You can do the same with any sub-config inside settings ! settings SettingsConfigGlobal settings config. numba Settings applied to Numba. Co nf ig( { "check_func_type" : true , "check_func_suffix" : false } ) config Settings applied to Config . Co nf ig( {} ) configured Settings applied to Configured . Co nf ig( { "config" : { "readonly" : true } } ) caching Settings applied across vectorbt.utils.decorators . See should_cache() . Co nf ig( { "enabled" : true , "whitelist" : [ { "instance" : null , "func" : null , "cls" : null , "base_cls" : "<class 'vectorbt.base.array_wrapper.ArrayWrapper'>" , "flags" : null , "rank" : null }, { "instance" : null , "func" : null , "cls" : null , "base_cls" : "<class 'vectorbt.base.column_grouper.ColumnGrouper'>" , "flags" : null , "rank" : null }, { "instance" : null , "func" : null , "cls" : null , "base_cls" : "<class 'vectorbt.records.col_mapper.ColumnMapper'>" , "flags" : null , "rank" : null } ], "blacklist" : [] } ) broadcasting Settings applied across vectorbt.base.reshape_fns . Co nf ig( { "align_index" : false , "align_columns" : true , "index_from" : "strict" , "columns_from" : "stack" , "ignore_sr_names" : true , "drop_duplicates" : true , "keep" : "last" , "drop_redundant" : true , "ignore_default" : true } ) array_wrapper Settings applied to ArrayWrapper . Co nf ig( { "column_only_select" : false , "group_select" : true , "freq" : null , "silence_warnings" : false } ) datetime Settings applied across vectorbt.utils.datetime_ . Co nf ig( { "naive_tz" : "UTC+01:00" , "to_py_timezone" : true } ) data Settings applied across vectorbt.data . Co nf ig( { "tz_localize" : "UTC" , "tz_convert" : "UTC" , "missing_index" : "nan" , "missing_columns" : "raise" , "alpaca" : { "api_key" : null , "secret_key" : null }, "binance" : { "api_key" : null , "api_secret" : null }, "ccxt" : { "enableRateLimit" : true }, "stats" : {}, "plots" : {} } ) binance: See binance.client.Client . ccxt: See Configuring API Keys . Keys can be defined per exchange. If a key is defined at the root, it applies to all exchanges. plotting Settings applied to plotting Plotly figures. Co nf ig( { "use_widgets" : true , "show_kwargs" : {}, "color_schema" : { "increasing" : "#1b9e76" , "decreasing" : "#d95f02" , "blue" : "#1f77b4" , "orange" : "#ff7f0e" , "green" : "#2ca02c" , "red" : "#dc3912" , "purple" : "#9467bd" , "brown" : "#8c564b" , "pink" : "#e377c2" , "gray" : "#7f7f7f" , "yellow" : "#bcbd22" , "cyan" : "#17becf" }, "contrast_color_schema" : { "blue" : "#4285F4" , "orange" : "#FFAA00" , "green" : "#37B13F" , "red" : "#EA4335" , "gray" : "#E2E2E2" }, "themes" : { "light" : { "color_schema" : { "blue" : "#1f77b4" , "orange" : "#ff7f0e" , "green" : "#2ca02c" , "red" : "#dc3912" , "purple" : "#9467bd" , "brown" : "#8c564b" , "pink" : "#e377c2" , "gray" : "#7f7f7f" , "yellow" : "#bcbd22" , "cyan" : "#17becf" }, "template" : "{ ... templates/light.json ... }" }, "dark" : { "color_schema" : { "blue" : "#1f77b4" , "orange" : "#ff7f0e" , "green" : "#2ca02c" , "red" : "#dc3912" , "purple" : "#9467bd" , "brown" : "#8c564b" , "pink" : "#e377c2" , "gray" : "#7f7f7f" , "yellow" : "#bcbd22" , "cyan" : "#17becf" }, "template" : "{ ... templates/dark.json ... }" }, "seaborn" : { "color_schema" : { "blue" : "#1f77b4" , "orange" : "#ff7f0e" , "green" : "#2ca02c" , "red" : "#dc3912" , "purple" : "#9467bd" , "brown" : "#8c564b" , "pink" : "#e377c2" , "gray" : "#7f7f7f" , "yellow" : "#bcbd22" , "cyan" : "#17becf" }, "template" : "{ ... templates/seaborn.json ... }" } }, "layout" : { "width" : 700 , "height" : 350 , "margin" : { "t" : 30 , "b" : 30 , "l" : 30 , "r" : 30 }, "legend" : { "orientation" : "h" , "yanchor" : "bottom" , "y" : 1.02 , "xanchor" : "right" , "x" : 1 , "traceorder" : "normal" }, "template" : "vbt_light" } } ) stats_builder Settings applied to StatsBuilderMixin . Co nf ig( { "metrics" : "all" , "tags" : "all" , "silence_warnings" : false , "template_mapping" : {}, "filters" : { "is_not_grouped" : { "filter_func" : "<function <lambda> at 0x1184c0d60>" , "warning_message" : "Sub(template=\"Metric '$metric_name' does not support grouped data\", mapping={})" }, "has_freq" : { "filter_func" : "<function <lambda> at 0x1184e5a80>" , "warning_message" : "Sub(template=\"Metric '$metric_name' requires frequency to be set\", mapping={})" } }, "settings" : { "to_timedelta" : null , "use_caching" : true }, "metric_settings" : {} } ) plots_builder Settings applied to PlotsBuilderMixin . Co nf ig( { "subplots" : "all" , "tags" : "all" , "silence_warnings" : false , "template_mapping" : {}, "filters" : { "is_not_grouped" : { "filter_func" : "<function <lambda> at 0x1184c04a0>" , "warning_message" : "Sub(template=\"Subplot '$subplot_name' does not support grouped data\", mapping={})" }, "has_freq" : { "filter_func" : "<function <lambda> at 0x1184e5bc0>" , "warning_message" : "Sub(template=\"Subplot '$subplot_name' requires frequency to be set\", mapping={})" } }, "settings" : { "use_caching" : true , "hline_shape_kwargs" : { "type" : "line" , "line" : { "color" : "gray" , "dash" : "dash" } } }, "subplot_settings" : {}, "show_titles" : true , "hide_id_labels" : true , "group_id_labels" : true , "make_subplots_kwargs" : {}, "layout_kwargs" : {} } ) generic Settings applied across vectorbt.generic . Co nf ig( { "stats" : { "filters" : { "has_mapping" : { "filter_func" : "<function <lambda> at 0x1184e5b20>" } }, "settings" : { "incl_all_keys" : false } }, "plots" : {} } ) ranges Settings applied across vectorbt.generic.ranges . Co nf ig( { "stats" : {}, "plots" : {} } ) drawdowns Settings applied across vectorbt.generic.drawdowns . Co nf ig( { "stats" : { "settings" : { "incl_active" : false } }, "plots" : {} } ) ohlcv Settings applied across vectorbt.ohlcv_accessors . Co nf ig( { "plot_type" : "OHLC" , "column_names" : { "open" : "Open" , "high" : "High" , "low" : "Low" , "close" : "Close" , "volume" : "Volume" }, "stats" : {}, "plots" : {} } ) signals Settings applied across vectorbt.signals . Co nf ig( { "stats" : { "filters" : { "silent_has_other" : { "filter_func" : "<function <lambda> at 0x1184e5d00>" } }, "settings" : { "other" : null , "other_name" : "Other" , "from_other" : false } }, "plots" : {} } ) returns Settings applied across vectorbt.returns . Co nf ig( { "year_freq" : "365 days" , "defaults" : { "start_value" : 0.0 , "window" : 10 , "minp" : null , "ddof" : 1 , "risk_free" : 0.0 , "levy_alpha" : 2.0 , "required_return" : 0.0 , "cutoff" : 0.05 }, "stats" : { "filters" : { "has_year_freq" : { "filter_func" : "<function <lambda> at 0x1184e5da0>" , "warning_message" : "Sub(template=\"Metric '$metric_name' requires year frequency to be set\", mapping={})" }, "has_benchmark_rets" : { "filter_func" : "<function <lambda> at 0x1184e5e40>" , "warning_message" : "Sub(template=\"Metric '$metric_name' requires benchmark_rets to be set\", mapping={})" } }, "settings" : { "check_is_not_grouped" : true } }, "plots" : {} } ) qs_adapter Settings applied across vectorbt.returns.qs_adapter . Co nf ig( { "defaults" : {} } ) records Settings applied across vectorbt.records.base . Co nf ig( { "stats" : {}, "plots" : {} } ) mapped_array Settings applied across vectorbt.records.mapped_array . Co nf ig( { "stats" : { "filters" : { "has_mapping" : { "filter_func" : "<function <lambda> at 0x1184e5c60>" } }, "settings" : { "incl_all_keys" : false } }, "plots" : {} } ) orders Settings applied across vectorbt.portfolio.orders . Co nf ig( { "stats" : {}, "plots" : {} } ) trades Settings applied across vectorbt.portfolio.trades . Co nf ig( { "stats" : { "settings" : { "incl_open" : false }, "template_mapping" : { "incl_open_tags" : "RepEval(expression=\"['open', 'closed'] if incl_open else ['closed']\", mapping={})" } }, "plots" : {} } ) logs Settings applied across vectorbt.portfolio.logs . Co nf ig( { "stats" : {} } ) portfolio Settings applied to Portfolio . Co nf ig( { "call_seq" : "default" , "init_cash" : 100.0 , "size" : I nf i n i t y , "size_type" : "amount" , "fees" : 0.0 , "fixed_fees" : 0.0 , "slippage" : 0.0 , "reject_prob" : 0.0 , "min_size" : 1e-08 , "max_size" : I nf i n i t y , "size_granularity" : NaN , "lock_cash" : false , "allow_partial" : true , "raise_reject" : false , "val_price" : I nf i n i t y , "accumulate" : false , "sl_stop" : NaN , "sl_trail" : false , "tp_stop" : NaN , "stop_entry_price" : "close" , "stop_exit_price" : "stoplimit" , "stop_conflict_mode" : "exit" , "upon_stop_exit" : "close" , "upon_stop_update" : "override" , "use_stops" : null , "log" : false , "upon_long_conflict" : "ignore" , "upon_short_conflict" : "ignore" , "upon_dir_conflict" : "ignore" , "upon_opposite_entry" : "reversereduce" , "signal_direction" : "longonly" , "order_direction" : "both" , "cash_sharing" : false , "call_pre_segment" : false , "call_post_segment" : false , "ffill_val_price" : true , "update_value" : false , "fill_pos_record" : true , "row_wise" : false , "flexible" : false , "use_numba" : true , "seed" : null , "freq" : null , "attach_call_seq" : false , "fillna_close" : true , "trades_type" : "exittrades" , "stats" : { "filters" : { "has_year_freq" : { "filter_func" : "<function <lambda> at 0x1184e5f80>" , "warning_message" : "Sub(template=\"Metric '$metric_name' requires year frequency to be set\", mapping={})" } }, "settings" : { "use_asset_returns" : false , "incl_open" : false }, "template_mapping" : { "incl_open_tags" : "RepEval(expression=\"['open', 'closed'] if incl_open else ['closed']\", mapping={})" } }, "plots" : { "subplots" : [ "orders" , "trade_pnl" , "cum_returns" ], "settings" : { "use_asset_returns" : false } } } ) messaging Settings applied across vectorbt.messaging . Co nf ig( { "telegram" : { "token" : null , "use_context" : true , "persistence" : "telegram_bot.pickle" , "defaults" : {}, "drop_pending_updates" : true }, "giphy" : { "api_key" : null , "weirdness" : 5 } } ) telegram: Settings applied to python-telegram-bot . Set persistence to string to use as filename in telegram.ext.PicklePersistence . For defaults , see telegram.ext.Defaults . Other settings will be distributed across telegram.ext.Updater and telegram.ext.updater.Updater.start_polling . giphy: Settings applied to GIPHY Translate Endpoint . SettingsConfig classSettingsConfig ( dct = None , copy_kwargs = None , reset_dct = None , reset_dct_copy_kwargs = None , frozen_keys = None , readonly = None , nested = None , convert_dicts = None , as_attrs = None ) Extends Config for global settings. Superclasses Config Documented Pickleable PickleableDict builtins.dict Inherited members Config.as_attrs_ Config.clear() Config.convert_dicts_ Config.copy() Config.copy_kwargs_ Config.dumps() Config.frozen_keys_ Config.load_update() Config.loads() Config.make_checkpoint() Config.merge_with() Config.nested_ Config.pop() Config.popitem() Config.readonly_ Config.reset() Config.reset_dct_ Config.reset_dct_copy_kwargs_ Config.to_dict() Config.to_doc() Config.update() Pickleable.load() Pickleable.save() register_template methodSettingsConfig . register_template ( theme ) Register template of a theme. register_templates methodSettingsConfig . register_templates () Register templates of all themes. reset_theme methodSettingsConfig . reset_theme () Reset to default theme. set_theme methodSettingsConfig . set_theme ( theme ) Set default theme.