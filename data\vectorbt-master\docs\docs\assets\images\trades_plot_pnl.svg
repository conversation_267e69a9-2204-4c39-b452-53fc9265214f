<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="350" style="" viewBox="0 0 700 350"><rect x="0" y="0" width="700" height="350" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-b1f952"><g class="clips"><clipPath id="clipb1f952xyplot" class="plotclip"><rect width="599" height="261"/></clipPath><clipPath class="axesclip" id="clipb1f952x"><rect x="71" y="0" width="599" height="350"/></clipPath><clipPath class="axesclip" id="clipb1f952y"><rect x="0" y="46" width="700" height="261"/></clipPath><clipPath class="axesclip" id="clipb1f952xy"><rect x="71" y="46" width="599" height="261"/></clipPath></g><g class="gradients"/><g class="patterns"/></defs><g class="bglayer"><rect class="bg" x="71" y="46" width="599" height="261" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(107.2,0)" d="M0,46v261" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(212.65,0)" d="M0,46v261" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(318.09000000000003,0)" d="M0,46v261" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(423.53,0)" d="M0,46v261" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(528.98,0)" d="M0,46v261" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(634.42,0)" d="M0,46v261" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,268.28)" d="M71,0h599" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,188.09)" d="M71,0h599" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,147.99)" d="M71,0h599" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,107.9)" d="M71,0h599" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,67.8)" d="M71,0h599" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="yzl zl crisp" transform="translate(0,228.18)" d="M71,0h599" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(71,46)" clip-path="url(#clipb1f952xyplot)"><g class="scatterlayer mlayer"><g class="trace scatter traced06dba6b-a6f0-4d02-bd0c-cfd3b4f2ad48" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(36.2,101.99)" d="M5,0A5,5 0 1,1 0,-5A5,5 0 0,1 5,0Z" style="opacity: 0.814286; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(141.65,21.8)" d="M7,0A7,7 0 1,1 0,-7A7,7 0 0,1 7,0Z" style="opacity: 0.9; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace1423324f-9bd4-4dd2-9725-022120fbde4c" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(352.53,202.23)" d="M3.5,0A3.5,3.5 0 1,1 0,-3.5A3.5,3.5 0 0,1 3.5,0Z" style="opacity: 0.75; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(457.98,222.28)" d="M4,0A4,4 0 1,1 0,-4A4,4 0 0,1 4,0Z" style="opacity: 0.771429; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(563.42,242.33)" d="M4.5,0A4.5,4.5 0 1,1 0,-4.5A4.5,4.5 0 0,1 4.5,0Z" style="opacity: 0.792857; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracea7c70884-137d-4065-a485-957193af2fc6" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(563.42,242.33)" d="M4.5,0A4.5,4.5 0 1,1 0,-4.5A4.5,4.5 0 0,1 4.5,0Z" style="opacity: 0.792857; stroke-width: 1px; fill: rgb(255, 170, 0); fill-opacity: 1; stroke: rgb(178, 118, 0); stroke-opacity: 1;"/></g><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="320" transform="translate(107.2,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;"><tspan class="line" dy="0em" x="0" y="320">Jan 2</tspan><tspan class="line" dy="1.3em" x="0" y="320">2020</tspan></text></g><g class="xtick"><text text-anchor="middle" x="0" y="320" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(212.65,0)">Jan 3</text></g><g class="xtick"><text text-anchor="middle" x="0" y="320" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(318.09000000000003,0)">Jan 4</text></g><g class="xtick"><text text-anchor="middle" x="0" y="320" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(423.53,0)">Jan 5</text></g><g class="xtick"><text text-anchor="middle" x="0" y="320" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(528.98,0)">Jan 6</text></g><g class="xtick"><text text-anchor="middle" x="0" y="320" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(634.42,0)">Jan 7</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="70" y="4.199999999999999" transform="translate(0,268.28)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">−50.00%</text></g><g class="ytick"><text text-anchor="end" x="70" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,228.18)">0.00%</text></g><g class="ytick"><text text-anchor="end" x="70" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,188.09)">50.00%</text></g><g class="ytick"><text text-anchor="end" x="70" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,147.99)">100.00%</text></g><g class="ytick"><text text-anchor="end" x="70" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,107.9)">150.00%</text></g><g class="ytick"><text text-anchor="end" x="70" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,67.8)">200.00%</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="iciclelayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-b1f952"><g class="clips"/><clipPath id="legendb1f952"><rect width="333" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"><path data-index="0" fill-rule="evenodd" d="M71,228.18L670,228.18" clip-path="url(#clipb1f952y)" style="opacity: 1; stroke: rgb(128, 128, 128); stroke-opacity: 1; fill: rgb(0, 0, 0); fill-opacity: 0; stroke-dasharray: 9px, 9px; stroke-width: 2px;"/></g></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(337,11.779999999999994)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="333" height="29" x="0" y="0"/><g class="scrollbox" transform="" clip-path="url(#legendb1f952)"><g class="groups"><g class="traces" transform="translate(0,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Closed - Profit</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M6,0A6,6 0 1,1 0,-6A6,6 0 0,1 6,0Z" style="opacity: 0.857143; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="128.109375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(130.609375,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Closed - Loss</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M4,0A4,4 0 1,1 0,-4A4,4 0 0,1 4,0Z" style="opacity: 0.771429; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="122.6875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(255.796875,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Open</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M4.5,0A4.5,4.5 0 1,1 0,-4.5A4.5,4.5 0 0,1 4.5,0Z" style="opacity: 0.792857; stroke-width: 1px; fill: rgb(255, 170, 0); fill-opacity: 1; stroke: rgb(178, 118, 0); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="74.171875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-ytitle"/></g></svg>