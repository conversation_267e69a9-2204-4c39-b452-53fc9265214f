<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="750" height="900" style="" viewBox="0 0 750 900"><rect x="0" y="0" width="750" height="900" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-a343b8"><g class="clips"><clipPath id="clipa343b8xyplot" class="plotclip"><rect width="625" height="231.72592592592594"/></clipPath><clipPath id="clipa343b8x2y2plot" class="plotclip"><rect width="625" height="231.72592592592596"/></clipPath><clipPath id="clipa343b8x3y3plot" class="plotclip"><rect width="625" height="231.72592592592594"/></clipPath><clipPath class="axesclip" id="clipa343b8x"><rect x="95" y="0" width="625" height="900"/></clipPath><clipPath class="axesclip" id="clipa343b8y"><rect x="0" y="85" width="750" height="231.72592592592594"/></clipPath><clipPath class="axesclip" id="clipa343b8xy"><rect x="95" y="85" width="625" height="231.72592592592594"/></clipPath><clipPath class="axesclip" id="clipa343b8y2"><rect x="0" y="350.637037037037" width="750" height="231.72592592592596"/></clipPath><clipPath class="axesclip" id="clipa343b8xy2"><rect x="95" y="350.637037037037" width="625" height="231.72592592592596"/></clipPath><clipPath class="axesclip" id="clipa343b8y3"><rect x="0" y="616.2740740740741" width="750" height="231.72592592592594"/></clipPath><clipPath class="axesclip" id="clipa343b8xy3"><rect x="95" y="616.2740740740741" width="625" height="231.72592592592594"/></clipPath><clipPath class="axesclip" id="clipa343b8x2"><rect x="95" y="0" width="625" height="900"/></clipPath><clipPath class="axesclip" id="clipa343b8x2y"><rect x="95" y="85" width="625" height="231.72592592592594"/></clipPath><clipPath class="axesclip" id="clipa343b8x2y2"><rect x="95" y="350.637037037037" width="625" height="231.72592592592596"/></clipPath><clipPath class="axesclip" id="clipa343b8x2y3"><rect x="95" y="616.2740740740741" width="625" height="231.72592592592594"/></clipPath><clipPath class="axesclip" id="clipa343b8x3"><rect x="95" y="0" width="625" height="900"/></clipPath><clipPath class="axesclip" id="clipa343b8x3y"><rect x="95" y="85" width="625" height="231.72592592592594"/></clipPath><clipPath class="axesclip" id="clipa343b8x3y2"><rect x="95" y="350.637037037037" width="625" height="231.72592592592596"/></clipPath><clipPath class="axesclip" id="clipa343b8x3y3"><rect x="95" y="616.2740740740741" width="625" height="231.72592592592594"/></clipPath></g><g class="gradients"/></defs><g class="bglayer"><rect class="bg" x="95" y="85" width="625" height="231.72592592592594" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/><rect class="bg" x="95" y="350.637037037037" width="625" height="231.72592592592596" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/><rect class="bg" x="95" y="616.2740740740741" width="625" height="231.72592592592594" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(116.71000000000001,0)" d="M0,85v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(191.82999999999998,0)" d="M0,85v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(262.11,0)" d="M0,85v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(337.23,0)" d="M0,85v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(409.92,0)" d="M0,85v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(485.04,0)" d="M0,85v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(557.74,0)" d="M0,85v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(632.86,0)" d="M0,85v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(707.98,0)" d="M0,85v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,275.66999999999996)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,218.4)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,161.14)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,103.87)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"/><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(95,85)" clip-path="url('#clipa343b8xyplot')"><g class="scatterlayer mlayer"><g class="trace scatter traceb40bb5a7-87f3-4541-b8b8-1c9a754b69be" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M21.71,156.31L24.13,162.45L26.56,152.16L28.98,150.28L31.4,150.26L36.25,128.72L38.67,131.12L41.1,136.87L43.52,128.63L45.94,132.33L48.37,127.89L50.79,129.27L53.21,109.7L55.64,110.3L58.06,112.68L60.48,106.8L62.91,106.41L65.33,113.18L67.75,114.57L70.18,112.05L72.6,113.91L75.02,121.76L77.45,120.65L79.87,122.87L82.29,116.31L84.71,107.35L87.14,94.5L89.56,95.7L91.98,90.2L94.41,94.73L96.83,93.52L101.68,96.37L104.1,99.59L106.52,87.21L108.95,83.87L113.79,80L116.22,72.8L118.64,80.24L121.06,70.17L123.49,66.8L125.91,70L128.33,67.2L130.76,79.3L133.18,78.01L135.6,85.01L138.03,72.07L140.45,86.63L142.87,87.35L145.3,85.12L147.72,85.78L150.14,78.3L154.99,94.99L157.41,109.91L159.84,110.94L162.26,114.15L164.68,116.24L167.11,117.3L169.53,108.5L171.95,110.85L174.38,111.78L176.8,102.52L179.22,101.26L181.64,107.35L184.07,130.31L186.49,135.59L191.34,135.94L193.76,220.14L196.18,203.16L198.61,213.57L201.03,208.07L203.45,218.89L205.88,212.84L208.3,212.48L210.72,185.2L213.15,184.98L215.57,185.37L217.99,195.53L220.42,178.75L222.84,169.63L225.26,171.17L227.69,170.16L234.96,192.9L237.38,178.36L239.8,178.11L242.23,173.3L244.65,167.95L247.07,169.67L249.5,165.83L251.92,168.02L254.34,154.25L256.77,156.99L259.19,152.47L261.61,153.39L264.04,165.89L266.46,166.07L268.88,162.86L271.3,166.47L273.73,166.55L276.15,172.28L278.57,158.69L283.42,154.66L285.84,156.61L288.27,165.42L290.69,165.46L293.11,158.68L295.54,149.73L297.96,146.26L300.38,145.72L302.81,142.57L305.23,139.26L307.65,138.93L310.08,110.47L312.5,114.55L314.92,108.64L317.35,105.1L319.77,107.71L322.19,107.27L324.62,104.68L327.04,97.07L329.46,77.53L331.89,80.64L334.31,87.77L336.73,111.74L339.16,116.17L341.58,110.37L346.43,83.76L348.85,95.37L351.27,93.98L353.7,85.56L356.12,83.97L358.54,83.9L360.96,89.8L363.39,102.43L365.81,99.54L368.23,98.78L370.66,110.77L373.08,107.44L375.5,109.49L377.93,99.59L380.35,89.72L382.77,92.2L385.2,84.72L387.62,91.57L390.04,71.35L392.47,89.6L397.31,81.85L399.74,85.71L402.16,86.05L404.58,83.04L409.43,81.99L411.85,79.86L414.28,95.56L416.7,91L419.12,91.16L421.55,93.7L426.39,89.36L428.82,91.02L431.24,92.98L433.66,96.52L436.09,95.25L438.51,96.08L440.93,86.2L443.36,86.74L445.78,95.79L448.2,97.19L453.05,103.47L455.47,100.66L457.89,99.31L460.32,100.82L462.74,98.23L465.16,101.24L467.59,102.27L470.01,100.98L472.43,102.65L474.86,94.02L477.28,97.55L479.7,92.51L482.13,96.81L484.55,96.79L486.97,97.89L489.4,96.85L491.82,97.79L494.24,97.81L499.09,100.98L501.51,100.44L506.36,99.45L508.78,100.07L513.63,89.73L516.05,88.13L518.48,89.4L520.9,85.38L523.32,78.85L525.75,47.77L528.17,50L530.59,44.63L533.02,44.32L535.44,38.24L537.86,25.75L540.29,45.97L542.71,40.45L545.13,41.61L547.55,26.11L549.98,25.18L552.4,30.28L554.82,25.91L557.25,28.16L559.67,22.36L562.09,35.75L564.52,30.76L566.94,25.05L569.36,25.49L571.79,22.72L574.21,21.94L576.63,11.59L579.06,19.12L581.48,25.79L583.9,22.35L586.33,30.54L588.75,27.98L591.17,28.47L593.6,25.32L596.02,37.02L598.44,33.52L600.87,38.24L603.29,31.97L605.71,32.99L608.14,27.13L610.56,28.01L612.98,19.72" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter trace61a4bf3d-2af0-4baa-93ca-5b744f1cfb6e" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(36.25,128.72)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(58.06,112.68)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(130.76,79.3)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(220.42,178.75)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(307.65,138.93)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(368.23,98.78)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(394.89,85.97)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(453.05,103.47)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(503.94,100.22)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(535.44,38.24)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceb2c7c91d-ba3d-407a-8cb5-c334c38d9fa0" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(43.52,128.63)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(79.87,122.87)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(181.64,107.35)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(266.46,166.07)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(324.62,104.68)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(377.93,99.59)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(450.62,100.11)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(489.4,96.85)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(508.78,100.07)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(588.75,27.98)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"/><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,275.66999999999996)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">6k</text></g><g class="ytick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,218.4)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">8k</text></g><g class="ytick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,161.14)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">10k</text></g><g class="ytick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,103.87)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">12k</text></g></g><g class="overaxes-above"/></g><g class="subplot x2y2"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x2"><path class="x2grid crisp" transform="translate(116.71000000000001,0)" d="M0,350.637037037037v231.72592592592596" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(191.82999999999998,0)" d="M0,350.637037037037v231.72592592592596" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(262.11,0)" d="M0,350.637037037037v231.72592592592596" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(337.23,0)" d="M0,350.637037037037v231.72592592592596" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(409.92,0)" d="M0,350.637037037037v231.72592592592596" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(485.04,0)" d="M0,350.637037037037v231.72592592592596" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(557.74,0)" d="M0,350.637037037037v231.72592592592596" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(632.86,0)" d="M0,350.637037037037v231.72592592592596" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(707.98,0)" d="M0,350.637037037037v231.72592592592596" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y2"><path class="y2grid crisp" transform="translate(0,564.3070370370369)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y2grid crisp" transform="translate(0,526.127037037037)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y2grid crisp" transform="translate(0,449.76703703703697)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y2grid crisp" transform="translate(0,411.59703703703696)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y2grid crisp" transform="translate(0,373.41703703703695)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="y2zl zl crisp" transform="translate(0,487.947037037037)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(95,350.637037037037)" clip-path="url('#clipa343b8x2y2plot')"><g class="scatterlayer mlayer"><g class="trace scatter trace7f0914b9-8589-44d8-a721-f189b8cf5bdc" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(43.52,137.05)" d="M3.5,0A3.5,3.5 0 1,1 0,-3.5A3.5,3.5 0 0,1 3.5,0Z" style="opacity: 0.75; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(266.46,84.62)" d="M5.07,0A5.07,5.07 0 1,1 0,-5.07A5.07,5.07 0 0,1 5.07,0Z" style="opacity: 0.817378; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(324.62,20.34)" d="M7,0A7,7 0 1,1 0,-7A7,7 0 0,1 7,0Z" style="opacity: 0.9; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(489.4,117.8)" d="M4.08,0A4.08,4.08 0 1,1 0,-4.08A4.08,4.08 0 0,1 4.08,0Z" style="opacity: 0.77473; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(508.78,136.88)" d="M3.5,0A3.5,3.5 0 1,1 0,-3.5A3.5,3.5 0 0,1 3.5,0Z" style="opacity: 0.750212; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(588.75,113.15)" d="M4.22,0A4.22,4.22 0 1,1 0,-4.22A4.22,4.22 0 0,1 4.22,0Z" style="opacity: 0.780714; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceb71b2207-5026-4306-b7ae-b4a2584edad7" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(79.87,168.47)" d="M4.43,0A4.43,4.43 0 1,1 0,-4.43A4.43,4.43 0 0,1 4.43,0Z" style="opacity: 0.789697; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(181.64,212.94)" d="M5.76,0A5.76,5.76 0 1,1 0,-5.76A5.76,5.76 0 0,1 5.76,0Z" style="opacity: 0.846854; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(377.93,139.66)" d="M3.56,0A3.56,3.56 0 1,1 0,-3.56A3.56,3.56 0 0,1 3.56,0Z" style="opacity: 0.752668; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(450.62,176.36)" d="M4.66,0A4.66,4.66 0 1,1 0,-4.66A4.66,4.66 0 0,1 4.66,0Z" style="opacity: 0.799839; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"/><g class="yaxislayer-above"><g class="y2tick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,564.3070370370369)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">−10.00%</text></g><g class="y2tick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,526.127037037037)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">−5.00%</text></g><g class="y2tick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,487.947037037037)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0.00%</text></g><g class="y2tick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,449.76703703703697)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">5.00%</text></g><g class="y2tick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,411.59703703703696)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">10.00%</text></g><g class="y2tick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,373.41703703703695)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">15.00%</text></g></g><g class="overaxes-above"/></g><g class="subplot x3y3"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x3"><path class="x3grid crisp" transform="translate(116.71000000000001,0)" d="M0,616.2740740740741v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x3grid crisp" transform="translate(191.82999999999998,0)" d="M0,616.2740740740741v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x3grid crisp" transform="translate(262.11,0)" d="M0,616.2740740740741v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x3grid crisp" transform="translate(337.23,0)" d="M0,616.2740740740741v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x3grid crisp" transform="translate(409.92,0)" d="M0,616.2740740740741v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x3grid crisp" transform="translate(485.04,0)" d="M0,616.2740740740741v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x3grid crisp" transform="translate(557.74,0)" d="M0,616.2740740740741v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x3grid crisp" transform="translate(632.86,0)" d="M0,616.2740740740741v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x3grid crisp" transform="translate(707.98,0)" d="M0,616.2740740740741v231.72592592592594" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y3"><path class="y3grid crisp" transform="translate(0,813.814074074074)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y3grid crisp" transform="translate(0,772.5840740740741)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y3grid crisp" transform="translate(0,731.3440740740741)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y3grid crisp" transform="translate(0,690.1140740740741)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y3grid crisp" transform="translate(0,648.8840740740741)" d="M95,0h625" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"/><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(95,616.2740740740741)" clip-path="url('#clipa343b8x3y3plot')"><g class="scatterlayer mlayer"><g class="trace scatter tracede281e39-449a-4e78-9bba-3800600726db" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M21.71,156.31L24.13,162.45L26.56,152.16L28.98,150.28L31.4,150.26L36.25,128.72L38.67,131.12L41.1,136.87L43.52,128.63L45.94,132.33L48.37,127.89L50.79,129.27L53.21,109.7L55.64,110.3L58.06,112.68L60.48,106.8L62.91,106.41L65.33,113.18L67.75,114.57L70.18,112.05L72.6,113.91L75.02,121.76L77.45,120.65L79.87,122.87L82.29,116.31L84.71,107.35L87.14,94.5L89.56,95.7L91.98,90.2L94.41,94.73L96.83,93.52L101.68,96.37L104.1,99.59L106.52,87.21L108.95,83.87L113.79,80L116.22,72.8L118.64,80.24L121.06,70.17L123.49,66.8L125.91,70L128.33,67.2L130.76,79.3L133.18,78.01L135.6,85.01L138.03,72.07L140.45,86.63L142.87,87.35L145.3,85.12L147.72,85.78L150.14,78.3L154.99,94.99L157.41,109.91L159.84,110.94L162.26,114.15L164.68,116.24L167.11,117.3L169.53,108.5L171.95,110.85L174.38,111.78L176.8,102.52L179.22,101.26L181.64,107.35L184.07,130.31L186.49,135.59L191.34,135.94L193.76,220.14L196.18,203.16L198.61,213.57L201.03,208.07L203.45,218.89L205.88,212.84L208.3,212.48L210.72,185.2L213.15,184.98L215.57,185.37L217.99,195.53L220.42,178.75L222.84,169.63L225.26,171.17L227.69,170.16L234.96,192.9L237.38,178.36L239.8,178.11L242.23,173.3L244.65,167.95L247.07,169.67L249.5,165.83L251.92,168.02L254.34,154.25L256.77,156.99L259.19,152.47L261.61,153.39L264.04,165.89L266.46,166.07L268.88,162.86L271.3,166.47L273.73,166.55L276.15,172.28L278.57,158.69L283.42,154.66L285.84,156.61L288.27,165.42L290.69,165.46L293.11,158.68L295.54,149.73L297.96,146.26L300.38,145.72L302.81,142.57L305.23,139.26L307.65,138.93L310.08,110.47L312.5,114.55L314.92,108.64L317.35,105.1L319.77,107.71L322.19,107.27L324.62,104.68L327.04,97.07L329.46,77.53L331.89,80.64L334.31,87.77L336.73,111.74L339.16,116.17L341.58,110.37L346.43,83.76L348.85,95.37L351.27,93.98L353.7,85.56L356.12,83.97L358.54,83.9L360.96,89.8L363.39,102.43L365.81,99.54L368.23,98.78L370.66,110.77L373.08,107.44L375.5,109.49L377.93,99.59L380.35,89.72L382.77,92.2L385.2,84.72L387.62,91.57L390.04,71.35L392.47,89.6L397.31,81.85L399.74,85.71L402.16,86.05L404.58,83.04L409.43,81.99L411.85,79.86L414.28,95.56L416.7,91L419.12,91.16L421.55,93.7L426.39,89.36L428.82,91.02L431.24,92.98L433.66,96.52L436.09,95.25L438.51,96.08L440.93,86.2L443.36,86.74L445.78,95.79L448.2,97.19L453.05,103.47L455.47,100.66L457.89,99.31L460.32,100.82L462.74,98.23L465.16,101.24L467.59,102.27L470.01,100.98L472.43,102.65L474.86,94.02L477.28,97.55L479.7,92.51L482.13,96.81L484.55,96.79L486.97,97.89L489.4,96.85L491.82,97.79L494.24,97.81L499.09,100.98L501.51,100.44L506.36,99.45L508.78,100.07L513.63,89.73L516.05,88.13L518.48,89.4L520.9,85.38L523.32,78.85L525.75,47.77L528.17,50L530.59,44.63L533.02,44.32L535.44,38.24L537.86,25.75L540.29,45.97L542.71,40.45L545.13,41.61L547.55,26.11L549.98,25.18L552.4,30.28L554.82,25.91L557.25,28.16L559.67,22.36L562.09,35.75L564.52,30.76L566.94,25.05L569.36,25.49L571.79,22.72L574.21,21.94L576.63,11.59L579.06,19.12L581.48,25.79L583.9,22.35L586.33,30.54L588.75,27.98L591.17,28.47L593.6,25.32L596.02,37.02L598.44,33.52L600.87,38.24L603.29,31.97L605.71,32.99L608.14,27.13L610.56,28.01L612.98,19.72" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(127, 127, 127); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter traceaf40ccf2-3fc8-4614-8115-0f1a1f859fb1" style="stroke-miterlimit: 2;"><g class="fills"><g><path class="js-fill" d="M21.71,156.31L58.06,156.23L60.48,151.38L62.91,151.06L65.33,156.31L307.65,156.31L310.08,147.68L312.5,151.16L314.92,146.13L317.35,143.11L319.77,145.33L322.19,144.96L324.62,142.75L368.23,142.75L370.66,152.75L373.08,149.97L375.5,151.68L377.93,143.43L380.35,143.43L394.89,143.43L397.31,140.16L399.74,143.23L402.16,143.5L404.58,141.11L407.01,140.82L409.43,140.27L411.85,138.59L414.28,151.02L416.7,147.42L419.12,147.54L421.55,149.55L426.39,146.12L428.82,147.43L431.24,148.98L433.66,151.79L436.09,150.79L438.51,151.44L440.93,143.61L443.36,144.04L445.78,151.21L448.2,152.32L450.62,154.63L453.05,154.63L455.47,152.37L457.89,151.29L460.32,152.5L462.74,150.42L465.16,152.84L467.59,153.67L470.01,152.63L472.43,153.97L474.86,147.04L477.28,149.87L479.7,145.83L482.13,149.28L484.55,149.26L486.97,150.15L489.4,149.32L491.82,149.32L535.44,149.2L537.86,140.98L540.29,154.28L542.71,150.65L545.13,151.41L547.55,141.22L549.98,140.6L552.4,143.96L554.82,141.09L557.25,142.56L559.67,138.75L562.09,147.56L566.94,140.52L569.36,140.81L571.79,138.98L574.21,138.47L576.63,131.66L579.06,136.62L581.48,141.01L583.9,138.75L586.33,144.13L588.75,142.45L612.98,142.45L612.98,156.31L21.71,156.31Z" style="fill: rgb(0, 128, 0); fill-opacity: 0.3; stroke-width: 0;"/></g></g><g class="errorbars"/><g class="lines"><path class="js-line" d="M21.71,156.31L612.98,156.31" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter tracead75d0f3-4dd4-48c1-ad4c-fde20f1a0395" style="stroke-miterlimit: 2;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M21.71,156.31L58.06,156.23L60.48,151.38L62.91,151.06L65.33,156.31L307.65,156.31L310.08,147.68L312.5,151.16L314.92,146.13L317.35,143.11L319.77,145.33L322.19,144.96L324.62,142.75L368.23,142.75L370.66,152.75L373.08,149.97L375.5,151.68L377.93,143.43L380.35,143.43L394.89,143.43L397.31,140.16L399.74,143.23L402.16,143.5L404.58,141.11L407.01,140.82L409.43,140.27L411.85,138.59L414.28,151.02L416.7,147.42L419.12,147.54L421.55,149.55L426.39,146.12L428.82,147.43L431.24,148.98L433.66,151.79L436.09,150.79L438.51,151.44L440.93,143.61L443.36,144.04L445.78,151.21L448.2,152.32L450.62,154.63L453.05,154.63L455.47,152.37L457.89,151.29L460.32,152.5L462.74,150.42L465.16,152.84L467.59,153.67L470.01,152.63L472.43,153.97L474.86,147.04L477.28,149.87L479.7,145.83L482.13,149.28L484.55,149.26L486.97,150.15L489.4,149.32L491.82,149.32L535.44,149.2L537.86,140.98L540.29,154.28L542.71,150.65L545.13,151.41L547.55,141.22L549.98,140.6L552.4,143.96L554.82,141.09L557.25,142.56L559.67,138.75L562.09,147.56L566.94,140.52L569.36,140.81L571.79,138.98L574.21,138.47L576.63,131.66L579.06,136.62L581.48,141.01L583.9,138.75L586.33,144.13L588.75,142.45L612.98,142.45" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter tracea2d56b95-4c50-4e38-8288-97bdbafd2df4" style="stroke-miterlimit: 2;"><g class="fills"><g><path class="js-fill" d="M21.71,156.31L36.25,156.31L38.67,158.42L41.1,163.49L43.52,156.31L65.33,156.65L67.75,157.8L70.18,156.31L72.6,157.25L75.02,163.73L77.45,162.81L79.87,164.65L82.29,164.65L130.76,164.65L133.18,163.75L135.6,168.63L138.03,159.6L140.45,169.77L142.87,170.27L145.3,168.71L147.72,169.17L150.14,163.95L152.57,169.43L154.99,175.6L157.41,186.03L159.84,186.75L162.26,188.99L167.11,191.19L169.53,185.05L171.95,186.68L174.38,187.33L176.8,180.86L179.22,179.99L181.64,184.24L184.07,184.24L220.42,184.24L222.84,175.39L225.26,176.89L227.69,175.9L234.96,197.97L237.38,183.86L239.8,183.62L242.23,178.95L244.65,173.76L247.07,175.43L249.5,171.71L251.92,173.83L254.34,160.48L256.77,163.13L259.19,158.75L261.61,159.64L264.04,171.76L266.46,171.94L307.65,171.94L310.08,156.31L612.98,156.31L612.98,156.31L21.71,156.31Z" style="fill: rgb(255, 0, 0); fill-opacity: 0.3; stroke-width: 0;"/></g></g><g class="errorbars"/><g class="lines"><path class="js-line" d="M21.71,156.31L612.98,156.31" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter trace292c5542-5c13-4335-b0a6-ec2a01730c59" style="stroke-miterlimit: 2;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M21.71,156.31L36.25,156.31L38.67,158.42L41.1,163.49L43.52,156.31L65.33,156.65L67.75,157.8L70.18,156.31L72.6,157.25L75.02,163.73L77.45,162.81L79.87,164.65L82.29,164.65L130.76,164.65L133.18,163.75L135.6,168.63L138.03,159.6L140.45,169.77L142.87,170.27L145.3,168.71L147.72,169.17L150.14,163.95L152.57,169.43L154.99,175.6L157.41,186.03L159.84,186.75L162.26,188.99L167.11,191.19L169.53,185.05L171.95,186.68L174.38,187.33L176.8,180.86L179.22,179.99L181.64,184.24L184.07,184.24L220.42,184.24L222.84,175.39L225.26,176.89L227.69,175.9L234.96,197.97L237.38,183.86L239.8,183.62L242.23,178.95L244.65,173.76L247.07,175.43L249.5,171.71L251.92,173.83L254.34,160.48L256.77,163.13L259.19,158.75L261.61,159.64L264.04,171.76L266.46,171.94L307.65,171.94L310.08,156.31L612.98,156.31" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter trace8f3d5ae1-78a1-4765-a4e7-6c72b35b7795" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M21.71,156.31L36.25,156.31L38.67,158.42L41.1,163.49L43.52,156.23L58.06,156.23L60.48,151.38L62.91,151.06L65.33,156.65L67.75,157.8L70.18,155.71L72.6,157.25L75.02,163.73L77.45,162.81L79.87,164.65L130.76,164.65L133.18,163.75L135.6,168.63L138.03,159.6L140.45,169.77L142.87,170.27L145.3,168.71L147.72,169.17L150.14,163.95L152.57,169.43L154.99,175.6L157.41,186.03L159.84,186.75L162.26,188.99L167.11,191.19L169.53,185.05L171.95,186.68L174.38,187.33L176.8,180.86L179.22,179.99L181.64,184.24L184.07,184.24L220.42,184.24L222.84,175.39L225.26,176.89L227.69,175.9L234.96,197.97L237.38,183.86L239.8,183.62L242.23,178.95L244.65,173.76L247.07,175.43L249.5,171.71L251.92,173.83L254.34,160.48L256.77,163.13L259.19,158.75L261.61,159.64L264.04,171.76L266.46,171.94L307.65,171.94L310.08,147.68L312.5,151.16L314.92,146.13L317.35,143.11L319.77,145.33L322.19,144.96L324.62,142.75L368.23,142.75L370.66,152.75L373.08,149.97L375.5,151.68L377.93,143.43L380.35,143.43L394.89,143.43L397.31,140.16L399.74,143.23L402.16,143.5L404.58,141.11L407.01,140.82L409.43,140.27L411.85,138.59L414.28,151.02L416.7,147.42L419.12,147.54L421.55,149.55L426.39,146.12L428.82,147.43L431.24,148.98L433.66,151.79L436.09,150.79L438.51,151.44L440.93,143.61L443.36,144.04L445.78,151.21L448.2,152.32L450.62,154.63L453.05,154.63L455.47,152.37L457.89,151.29L460.32,152.5L462.74,150.42L465.16,152.84L467.59,153.67L470.01,152.63L472.43,153.97L474.86,147.04L477.28,149.87L479.7,145.83L482.13,149.28L484.55,149.26L486.97,150.15L489.4,149.32L491.82,149.32L535.44,149.2L537.86,140.98L540.29,154.28L542.71,150.65L545.13,151.41L547.55,141.22L549.98,140.6L552.4,143.96L554.82,141.09L557.25,142.56L559.67,138.75L562.09,147.56L566.94,140.52L569.36,140.81L571.79,138.98L574.21,138.47L576.63,131.66L579.06,136.62L581.48,141.01L583.9,138.75L586.33,144.13L588.75,142.45L612.98,142.45" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(148, 103, 189); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter trace6421c9c1-41fe-48fa-bede-10389e46237c" style="stroke-miterlimit: 2; opacity: 0;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M21.71,156.31L612.98,156.31" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;"/></g><g class="points"/><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="x3tick"><text text-anchor="middle" x="0" y="861" transform="translate(116.71000000000001,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jan 2020</text></g><g class="x3tick"><text text-anchor="middle" x="0" y="861" transform="translate(191.82999999999998,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Feb 2020</text></g><g class="x3tick"><text text-anchor="middle" x="0" y="861" transform="translate(262.11,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Mar 2020</text></g><g class="x3tick"><text text-anchor="middle" x="0" y="861" transform="translate(337.23,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Apr 2020</text></g><g class="x3tick"><text text-anchor="middle" x="0" y="861" transform="translate(409.92,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">May 2020</text></g><g class="x3tick"><text text-anchor="middle" x="0" y="861" transform="translate(485.04,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jun 2020</text></g><g class="x3tick"><text text-anchor="middle" x="0" y="861" transform="translate(557.74,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jul 2020</text></g><g class="x3tick"><text text-anchor="middle" x="0" y="861" transform="translate(632.86,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Aug 2020</text></g><g class="x3tick"><text text-anchor="middle" x="0" y="861" transform="translate(707.98,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Sep 2020</text></g></g><g class="yaxislayer-above"><g class="y3tick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,813.814074074074)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0.8</text></g><g class="y3tick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,772.5840740740741)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">1</text></g><g class="y3tick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,731.3440740740741)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">1.2</text></g><g class="y3tick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,690.1140740740741)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">1.4</text></g><g class="y3tick"><text text-anchor="end" x="94" y="4.199999999999999" transform="translate(0,648.8840740740741)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">1.6</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-a343b8"><g class="clips"/><clipPath id="legenda343b8"><rect width="523" height="48" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"><path data-index="0" fill-rule="evenodd" d="M95,487.947037037037L720,487.947037037037" clip-path="url('#clipa343b8y2')" style="opacity: 1; stroke: rgb(128, 128, 128); stroke-opacity: 1; fill: rgb(0, 0, 0); fill-opacity: 0; stroke-dasharray: 9px, 9px; stroke-width: 2px;"/><path data-index="1" fill-rule="evenodd" d="M95,772.5840740740741L720,772.5840740740741" clip-path="url('#clipa343b8y3')" style="opacity: 1; stroke: rgb(128, 128, 128); stroke-opacity: 1; fill: rgb(0, 0, 0); fill-opacity: 0; stroke-dasharray: 9px, 9px; stroke-width: 2px;"/></g></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(197,11.566666666666592)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="523" height="48" x="0" y="0"/><g class="scrollbox" transform="" clip-path="url('#legenda343b8')"><g class="groups"><g class="traces" transform="translate(0,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Close</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="74.859375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(130.609375,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Buy</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="65.421875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(261.21875,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Sell</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="64.4375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(391.828125,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Closed - Profit</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M4.56,0A4.56,4.56 0 1,1 0,-4.56A4.56,4.56 0 0,1 4.56,0Z" style="opacity: 0.795506; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="128.109375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(0,33.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Closed - Loss</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M4.6,0A4.6,4.6 0 1,1 0,-4.6A4.6,4.6 0 0,1 4.6,0Z" style="opacity: 0.797265; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="122.6875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(130.609375,33.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Benchmark</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(127, 127, 127); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="110.421875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(261.21875,33.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Value</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(148, 103, 189); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="75.953125" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-x2title"/><g class="g-x3title"><text class="x3title" x="407.5" y="888.3" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Date</text></g><g class="g-ytitle"><text class="ytitle" transform="rotate(-90,46.840625,200.86296296296297)" x="46.840625" y="200.86296296296297" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Price</text></g><g class="g-y2title" transform="translate(2.4248046875,0)"><text class="y2title" transform="rotate(-90,11.575000000000003,466.49999999999994)" x="11.575000000000003" y="466.49999999999994" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Trade returns</text></g><g class="g-y3title"><text class="y3title" transform="rotate(-90,49.575,732.1370370370371)" x="49.575" y="732.1370370370371" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Cumulative returns</text></g><g class="annotation" data-index="0" style="opacity: 1;"><g class="annotation-text-g" transform="rotate(0,407.5,73)"><g class="cursor-pointer" transform="translate(379,61)"><rect class="bg" x="0.5" y="0.5" width="57" height="23" style="stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;"/><text class="annotation-text" text-anchor="middle" x="29.046875" y="18" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Orders</text></g></g></g><g class="annotation" data-index="1" style="opacity: 1;"><g class="annotation-text-g" transform="rotate(0,407.5,338.637037037037)"><g class="cursor-pointer" transform="translate(349,327)"><rect class="bg" x="0.5" y="0.5" width="117" height="23" style="stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;"/><text class="annotation-text" text-anchor="middle" x="58.90625" y="18" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Trade Returns</text></g></g></g><g class="annotation" data-index="2" style="opacity: 1;"><g class="annotation-text-g" transform="rotate(0,407.5,604.2740740740741)"><g class="cursor-pointer" transform="translate(326,592)"><rect class="bg" x="0.5" y="0.5" width="162" height="23" style="stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;"/><text class="annotation-text" text-anchor="middle" x="81.34375" y="18" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Cumulative Returns</text></g></g></g></g></svg>