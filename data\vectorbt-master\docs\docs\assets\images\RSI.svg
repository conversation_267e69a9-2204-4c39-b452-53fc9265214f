<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="350" style="" viewBox="0 0 700 350"><rect x="0" y="0" width="700" height="350" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-1c2297"><g class="clips"><clipPath id="clip1c2297xyplot" class="plotclip"><rect width="633" height="274"/></clipPath><clipPath class="axesclip" id="clip1c2297x"><rect x="37" y="0" width="633" height="350"/></clipPath><clipPath class="axesclip" id="clip1c2297y"><rect x="0" y="46" width="700" height="274"/></clipPath><clipPath class="axesclip" id="clip1c2297xy"><rect x="37" y="46" width="633" height="274"/></clipPath></g><g class="gradients"/></defs><g class="bglayer"><rect class="bg" x="37" y="46" width="633" height="274" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"><path data-index="0" fill-rule="evenodd" d="M37,232.82H670V133.18H37Z" clip-path="url('#clip1c2297xy')" style="opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(128, 0, 128); fill-opacity: 1; stroke-width: 0px;"/></g><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(40.44,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(147.09,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(250.29,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(356.94,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(460.15,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(566.79,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,257.73)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,207.91)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,158.09)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,108.27000000000001)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,58.45)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="yzl zl crisp" transform="translate(0,307.55)" d="M37,0h633" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(37, 46)" clip-path="url('#clip1c2297xyplot')"><g class="scatterlayer mlayer"><g class="trace scatter traceb1b43f54-eddd-4287-b84c-b7882409e86f" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M48.16,116.22L51.6,108.88L55.04,93.83L58.48,95.78L61.92,63.59L65.36,77.11L68.8,75.6L72.24,102.29L75.68,100.39L79.13,112.43L82.57,113.22L86.01,118.15L89.45,113.38L92.89,92.07L96.33,100.96L99.77,102.27L103.21,119.54L106.65,111.47L110.09,101.15L113.53,47.04L116.97,44.82L120.41,43.38L123.85,39.76L127.29,39.53L130.73,34.78L134.17,24.26L137.61,37.59L141.05,37.31L144.49,67.67L151.37,67.84L154.81,65.58L158.25,77.43L161.69,104.12L165.13,109.65L168.57,100.07L172.01,108.27L175.45,107.38L178.89,123.33L182.33,123.97L185.77,96.7L189.21,121.44L192.65,120.73L196.09,116.65L199.53,118.65L202.97,123.84L206.41,115.59L209.85,122.41L213.29,118.57L216.73,113.03L220.17,93.59L223.61,91.87L227.05,93.37L230.49,104.65L233.93,111.32L237.38,87.13L240.82,39.42L244.26,36.75L247.7,26.41L251.14,49.31L254.58,37.1L258.02,36.53L261.46,35.42L264.9,55.88L268.34,87.09L271.78,91.5L275.22,75.1L278.66,81.45L282.1,83.15L285.54,95.26L288.98,95.1L292.42,96.74L295.86,111.95L299.3,90.99L302.74,105.31L306.18,113.22L309.62,121.5L313.06,122.25L316.5,91.71L319.94,88.53L323.38,110.62L326.82,127.05L330.26,146.43L333.7,131.4L337.14,139.48L340.58,134.75L344.02,140.89L347.46,180.9L350.9,170.72L354.34,170.32L357.78,157.51L361.22,140.85L364.66,132.16L368.1,126.34L371.54,127.16L374.98,90.48L378.42,73.91L381.86,72.25L388.74,60.83L392.18,49.3L395.63,32.91L399.07,33.79L402.51,26.15L405.95,23.57L409.39,85.19L412.83,78.14L416.27,89.37L419.71,112.18L423.15,119.63L426.59,113.29L430.03,103.54L433.47,116.97L436.91,126.73L440.35,130.55L443.79,129.49L447.23,121.96L450.67,127.31L454.11,148.57L457.55,134.34L460.99,146.88L467.87,146.41L471.31,132.04L474.75,155.27L478.19,171.94L481.63,145.23L485.07,143.79L488.51,143.69L491.95,150.03L495.39,169L498.83,180.14L502.27,176.53L505.71,163.94L509.15,175.62L512.59,175.2L516.03,153.9L519.47,174.44L522.91,132.02L526.35,123.09L529.79,148.66L533.23,137.55L536.67,134.66L540.11,120.96L543.55,84.36L546.99,78.28L550.43,65.7L553.88,66.8L557.32,70.06L560.76,75.88L564.2,74.35L567.64,79.34L571.08,100.73L574.52,137.88L581.4,140.83L584.84,153.26L588.28,154.34L591.72,163.26L595.16,159.02L598.6,190.42L602.04,191.58L605.48,178.54L608.92,173.26L612.36,180.64L615.8,168.16L619.24,160.64L622.68,148.12L626.12,167.12L629.56,165.95L633,160.18" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(40.44,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Mar 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(147.09,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Apr 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(250.29,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">May 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(356.94,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jun 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(460.15,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jul 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(566.79,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Aug 2019</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,307.55)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,257.73)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">20</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,207.91)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">40</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,158.09)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">60</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,108.27000000000001)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">80</text></g><g class="ytick"><text text-anchor="end" x="36" y="4.199999999999999" transform="translate(0,58.45)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">100</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-1c2297"><g class="clips"/><clipPath id="legend1c2297"><rect width="67" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(603, 11.519999999999996)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="67" height="29" x="0" y="0"/><g class="scrollbox" transform="translate(0, 0)" clip-path="url('#legend1c2297')"><g class="groups"><g class="traces" transform="translate(0, 14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">RSI</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="64.109375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-ytitle"/></g></svg>