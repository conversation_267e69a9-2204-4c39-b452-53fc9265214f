{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ohlcv"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import vectorbt as vbt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"Collapsed": "false"}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "from numba import njit"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Config({\n", "    \"open\": \"Open\",\n", "    \"high\": \"High\",\n", "    \"low\": \"Low\",\n", "    \"close\": \"Close\",\n", "    \"volume\": \"Volume\"\n", "})\n"]}], "source": ["print(vbt.settings.ohlcv['column_names'])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Can only use .str accessor with string values!\n"]}], "source": ["try:\n", "    pd.DataFrame([1, 2, 3]).vbt.ohlcv.plot()\n", "except Exception as e:\n", "    print(e) # couldn't find default column names"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["ohlcv_ts = pd.DataFrame({\n", "    'open': [1, 2, 3], \n", "    'high': [1, 2, 3], \n", "    'low': [1, 2, 3], \n", "    'close': [1, 2, 3], \n", "    'volume': [1, 2, 3]\n", "})"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                open      high       low     close    volume\n", "2018-01-01 -0.385236 -0.339812 -0.941729 -1.058755 -0.881574\n", "2018-01-02 -0.243403  0.210847 -0.408890  0.158420 -1.149656\n", "2018-01-03  0.226744  1.207481  0.198341  1.168544 -2.071525\n", "2018-01-04  0.407461  1.294365 -0.223333  0.946101 -2.218826\n", "2018-01-05  1.221150  1.988841  0.810106  0.834179 -2.940705\n", "2018-01-06  2.022596  2.046477  1.293526  2.013281 -2.689034\n", "2018-01-07  2.724580  3.303932  1.976807  3.468771 -1.720546\n", "2018-01-08  2.057622  2.410773  1.251777  2.269435 -0.980795\n", "2018-01-09  1.607800  1.720066  1.169075  2.456895 -1.621470\n", "2018-01-10  2.069398  2.516643  1.838688  1.776437 -1.282516\n"]}], "source": ["ohlcv_ts = pd.DataFrame(\n", "    columns=['open', 'high', 'low', 'close', 'volume'],\n", "    index=[datetime(2018, 1, 1) + <PERSON>elta(days=i) for i in range(10)]\n", ")\n", "ohlcv_ts['open'] = np.cumsum(np.random.uniform(-0.8, 1, size=(10,)))\n", "ohlcv_ts['close'] = ohlcv_ts['open'] + np.random.uniform(-1, 1, size=(10,))\n", "ohlcv_ts['high'] = ohlcv_ts['open'] + np.random.uniform(0, 1, size=(10,))\n", "ohlcv_ts['low'] = ohlcv_ts['open'] - np.random.uniform(0, 1, size=(10,))\n", "ohlcv_ts['volume'] = np.cumsum(np.random.uniform(-1, 1, size=(10,)))\n", "\n", "print(ohlcv_ts)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["try:\n", "    ohlcv_ts.vbt.ohlcv.plot()\n", "except Exception as e:\n", "    print(e) # still couldn't find default column names"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-52d278\"><g class=\"clips\"><clipPath id=\"clip52d278xyplot\" class=\"plotclip\"><rect width=\"640\" height=\"182.7\"/></clipPath><clipPath id=\"clip52d278x2y2plot\" class=\"plotclip\"><rect width=\"640\" height=\"78.3\"/></clipPath><clipPath class=\"axesclip\" id=\"clip52d278x\"><rect x=\"30\" y=\"0\" width=\"640\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip52d278y\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"182.7\"/></clipPath><clipPath class=\"axesclip\" id=\"clip52d278xy\"><rect x=\"30\" y=\"46\" width=\"640\" height=\"182.7\"/></clipPath><clipPath class=\"axesclip\" id=\"clip52d278y2\"><rect x=\"0\" y=\"228.7\" width=\"700\" height=\"78.3\"/></clipPath><clipPath class=\"axesclip\" id=\"clip52d278xy2\"><rect x=\"30\" y=\"228.7\" width=\"640\" height=\"78.3\"/></clipPath><clipPath class=\"axesclip\" id=\"clip52d278x2\"><rect x=\"30\" y=\"0\" width=\"640\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip52d278x2y\"><rect x=\"30\" y=\"46\" width=\"640\" height=\"182.7\"/></clipPath><clipPath class=\"axesclip\" id=\"clip52d278x2y2\"><rect x=\"30\" y=\"228.7\" width=\"640\" height=\"78.3\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"30\" y=\"46\" width=\"640\" height=\"182.7\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/><rect class=\"bg\" x=\"30\" y=\"228.7\" width=\"640\" height=\"78.3\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(62,0)\" d=\"M0,46v182.7\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(190,0)\" d=\"M0,46v182.7\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(318,0)\" d=\"M0,46v182.7\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(446,0)\" d=\"M0,46v182.7\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(574,0)\" d=\"M0,46v182.7\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,221.82)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,144.36)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,105.63)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,66.91)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,183.09)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(30,46)\" clip-path=\"url(#clip52d278xyplot)\"><g class=\"ohlclayer mlayer\"><g class=\"trace ohlc\" style=\"opacity: 1;\"><path d=\"M12.8,152.01H32M32,150.25V173.57M51.2,178.1H32\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M76.8,146.52H96M96,128.93V152.93M115.2,130.96H96\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M140.8,128.31H160M160,90.33V129.41M179.2,91.84H160\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M204.8,121.31H224M224,86.96V145.74M243.2,100.45H224\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M268.8,89.8H288M288,60.07V105.72M307.2,104.79H288\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M332.8,58.76H352M352,57.83V87M371.2,59.12H352\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M396.8,31.57H416M416,9.14V60.53M435.2,2.75H416\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M460.8,57.4H480M480,43.73V88.61M499.2,49.2H480\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M524.8,74.82H544M544,70.48V91.82M563.2,41.94H544\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M588.8,56.95H608M608,39.63V65.88M627.2,68.29H608\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"/><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,221.82)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">−1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,183.09)\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,144.36)\">1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,105.63)\">2</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,66.91)\">3</text></g></g><g class=\"overaxes-above\"/></g><g class=\"subplot x2y2\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x2\"><path class=\"x2grid crisp\" transform=\"translate(62,0)\" d=\"M0,228.7v78.3\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(190,0)\" d=\"M0,228.7v78.3\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(318,0)\" d=\"M0,228.7v78.3\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(446,0)\" d=\"M0,228.7v78.3\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(574,0)\" d=\"M0,228.7v78.3\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,304.58)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,279.28999999999996)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,253.98999999999998)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"y2zl zl crisp\" transform=\"translate(0,228.7)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(30,228.7)\" clip-path=\"url(#clip52d278x2y2plot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" shape-rendering=\"crispEdges\" style=\"opacity: 0.5;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,0V22.3H64V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(217, 95, 2); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M64,0V29.08H128V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(27, 158, 118); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M128,0V52.4H192V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(27, 158, 118); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M192,0V56.13H256V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(27, 158, 118); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M256,0V74.38H320V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(217, 95, 2); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M320,0V68.02H384V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(217, 95, 2); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M384,0V43.52H448V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(27, 158, 118); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M448,0V24.81H512V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(27, 158, 118); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M512,0V41.02H576V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(27, 158, 118); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M576,0V32.44H640V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(217, 95, 2); fill-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(62,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2018</tspan></text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(190,0)\">Jan 3</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(318,0)\">Jan 5</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(446,0)\">Jan 7</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(574,0)\">Jan 9</text></g></g><g class=\"yaxislayer-above\"><g class=\"y2tick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,304.58)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">−3</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,279.28999999999996)\">−2</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,253.98999999999998)\">−1</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,228.7)\">0</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-52d278\"><g class=\"clips\"/><clipPath id=\"legend52d278\"><rect width=\"169\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(501,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"169\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legend52d278)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">OHLC</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendohlc\" d=\"M15,0H0M8,6V0\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; fill: none; stroke-width: 2px; stroke: rgb(217, 95, 2); stroke-opacity: 1;\"/><path class=\"legendohlc\" d=\"M-15,0H0M-8,-6V0\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; fill: none; stroke-width: 2px; stroke: rgb(27, 158, 118); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"76.03125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(78.53125,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Volume</text><g class=\"layers\" style=\"opacity: 0.5;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendundefined\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0px; fill: rgb(217, 95, 2); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"87.703125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-x2title\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Specify them manually\n", "ohlcv_ts.vbt.ohlcv(column_names={\n", "    'open': 'open', \n", "    'high': 'high', \n", "    'low': 'low', \n", "    'close': 'close', \n", "    'volume': 'volume'\n", "}).plot().show_svg()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Or by changing the defaults\n", "vbt.settings.ohlcv['column_names'] = {\n", "    'open': 'open', \n", "    'high': 'high', \n", "    'low': 'low', \n", "    'close': 'close', \n", "    'volume': 'volume'\n", "}"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-06a167\"><g class=\"clips\"><clipPath id=\"clip06a167xyplot\" class=\"plotclip\"><rect width=\"640\" height=\"182.7\"/></clipPath><clipPath id=\"clip06a167x2y2plot\" class=\"plotclip\"><rect width=\"640\" height=\"78.3\"/></clipPath><clipPath class=\"axesclip\" id=\"clip06a167x\"><rect x=\"30\" y=\"0\" width=\"640\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip06a167y\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"182.7\"/></clipPath><clipPath class=\"axesclip\" id=\"clip06a167xy\"><rect x=\"30\" y=\"46\" width=\"640\" height=\"182.7\"/></clipPath><clipPath class=\"axesclip\" id=\"clip06a167y2\"><rect x=\"0\" y=\"228.7\" width=\"700\" height=\"78.3\"/></clipPath><clipPath class=\"axesclip\" id=\"clip06a167xy2\"><rect x=\"30\" y=\"228.7\" width=\"640\" height=\"78.3\"/></clipPath><clipPath class=\"axesclip\" id=\"clip06a167x2\"><rect x=\"30\" y=\"0\" width=\"640\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip06a167x2y\"><rect x=\"30\" y=\"46\" width=\"640\" height=\"182.7\"/></clipPath><clipPath class=\"axesclip\" id=\"clip06a167x2y2\"><rect x=\"30\" y=\"228.7\" width=\"640\" height=\"78.3\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"30\" y=\"46\" width=\"640\" height=\"182.7\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/><rect class=\"bg\" x=\"30\" y=\"228.7\" width=\"640\" height=\"78.3\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(62,0)\" d=\"M0,46v182.7\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(190,0)\" d=\"M0,46v182.7\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(318,0)\" d=\"M0,46v182.7\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(446,0)\" d=\"M0,46v182.7\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(574,0)\" d=\"M0,46v182.7\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,221.82)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,144.36)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,105.63)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,66.91)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,183.09)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(30,46)\" clip-path=\"url(#clip06a167xyplot)\"><g class=\"ohlclayer mlayer\"><g class=\"trace ohlc\" style=\"opacity: 1;\"><path d=\"M12.8,152.01H32M32,150.25V173.57M51.2,178.1H32\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M76.8,146.52H96M96,128.93V152.93M115.2,130.96H96\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M140.8,128.31H160M160,90.33V129.41M179.2,91.84H160\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M204.8,121.31H224M224,86.96V145.74M243.2,100.45H224\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M268.8,89.8H288M288,60.07V105.72M307.2,104.79H288\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M332.8,58.76H352M352,57.83V87M371.2,59.12H352\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M396.8,31.57H416M416,9.14V60.53M435.2,2.75H416\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M460.8,57.4H480M480,43.73V88.61M499.2,49.2H480\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M524.8,74.82H544M544,70.48V91.82M563.2,41.94H544\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M588.8,56.95H608M608,39.63V65.88M627.2,68.29H608\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"/><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,221.82)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">−1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,183.09)\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,144.36)\">1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,105.63)\">2</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,66.91)\">3</text></g></g><g class=\"overaxes-above\"/></g><g class=\"subplot x2y2\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x2\"><path class=\"x2grid crisp\" transform=\"translate(62,0)\" d=\"M0,228.7v78.3\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(190,0)\" d=\"M0,228.7v78.3\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(318,0)\" d=\"M0,228.7v78.3\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(446,0)\" d=\"M0,228.7v78.3\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(574,0)\" d=\"M0,228.7v78.3\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,304.58)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,279.28999999999996)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,253.98999999999998)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"y2zl zl crisp\" transform=\"translate(0,228.7)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(30,228.7)\" clip-path=\"url(#clip06a167x2y2plot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" shape-rendering=\"crispEdges\" style=\"opacity: 0.5;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,0V22.3H64V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(217, 95, 2); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M64,0V29.08H128V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(27, 158, 118); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M128,0V52.4H192V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(27, 158, 118); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M192,0V56.13H256V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(27, 158, 118); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M256,0V74.38H320V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(217, 95, 2); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M320,0V68.02H384V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(217, 95, 2); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M384,0V43.52H448V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(27, 158, 118); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M448,0V24.81H512V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(27, 158, 118); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M512,0V41.02H576V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(27, 158, 118); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M576,0V32.44H640V0Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(217, 95, 2); fill-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(62,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2018</tspan></text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(190,0)\">Jan 3</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(318,0)\">Jan 5</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(446,0)\">Jan 7</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(574,0)\">Jan 9</text></g></g><g class=\"yaxislayer-above\"><g class=\"y2tick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,304.58)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">−3</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,279.28999999999996)\">−2</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,253.98999999999998)\">−1</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,228.7)\">0</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-06a167\"><g class=\"clips\"/><clipPath id=\"legend06a167\"><rect width=\"169\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(501,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"169\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legend06a167)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">OHLC</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendohlc\" d=\"M15,0H0M8,6V0\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; fill: none; stroke-width: 2px; stroke: rgb(217, 95, 2); stroke-opacity: 1;\"/><path class=\"legendohlc\" d=\"M-15,0H0M-8,-6V0\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; fill: none; stroke-width: 2px; stroke: rgb(27, 158, 118); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"76.03125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(78.53125,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Volume</text><g class=\"layers\" style=\"opacity: 0.5;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendundefined\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0px; fill: rgb(217, 95, 2); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"87.703125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-x2title\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ohlcv_ts.vbt.ohlcv.plot().show_svg()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["Start             2018-01-01 00:00:00\n", "End               2018-01-10 00:00:00\n", "Period               10 days 00:00:00\n", "First Price                 -0.385236\n", "Lowest Price                -1.058755\n", "Highest Price                3.468771\n", "Last Price                   1.776437\n", "First Volume                -0.881574\n", "Lowest Volume               -2.940705\n", "Highest Volume              -0.881574\n", "Last Volume                 -1.282516\n", "Name: agg_func_mean, dtype: object"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["ohlcv_ts.vbt.ohlcv.stats()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["7.62 ms ± 11.2 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["big_ohlcv_ts = pd.DataFrame(np.random.uniform(1, 2, (10000, 5)), columns=ohlcv_ts.columns)\n", "\n", "%timeit big_ohlcv_ts.vbt.ohlcv.stats()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"750\" height=\"380\" style=\"\" viewBox=\"0 0 750 380\"><rect x=\"0\" y=\"0\" width=\"750\" height=\"380\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-0ee5bf\"><g class=\"clips\"><clipPath id=\"clip0ee5bfxyplot\" class=\"plotclip\"><rect width=\"690\" height=\"245\"/></clipPath><clipPath class=\"axesclip\" id=\"clip0ee5bfx\"><rect x=\"30\" y=\"0\" width=\"690\" height=\"380\"/></clipPath><clipPath class=\"axesclip\" id=\"clip0ee5bfy\"><rect x=\"0\" y=\"67\" width=\"750\" height=\"245\"/></clipPath><clipPath class=\"axesclip\" id=\"clip0ee5bfxy\"><rect x=\"30\" y=\"67\" width=\"690\" height=\"245\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"30\" y=\"67\" width=\"690\" height=\"245\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(64.5,0)\" d=\"M0,67v245\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(202.5,0)\" d=\"M0,67v245\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(340.5,0)\" d=\"M0,67v245\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(478.5,0)\" d=\"M0,67v245\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(616.5,0)\" d=\"M0,67v245\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,302.78)\" d=\"M30,0h690\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,198.91)\" d=\"M30,0h690\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,146.97)\" d=\"M30,0h690\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,95.03)\" d=\"M30,0h690\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,250.84)\" d=\"M30,0h690\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(30,67)\" clip-path=\"url(#clip0ee5bfxyplot)\"><g class=\"ohlclayer mlayer\"><g class=\"trace ohlc\" style=\"opacity: 1;\"><path d=\"M13.8,203.85H34.5M34.5,201.49V232.75M55.2,238.83H34.5\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M82.8,196.48H103.5M103.5,172.89V205.08M124.2,175.61H103.5\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M151.8,172.06H172.5M172.5,121.13V173.54M193.2,123.15H172.5\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M220.8,162.68H241.5M241.5,116.62V195.44M262.2,134.7H241.5\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M289.8,120.42H310.5M310.5,80.55V141.77M331.2,140.52H310.5\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M358.8,78.8H379.5M379.5,77.56V116.66M400.2,79.28H379.5\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M427.8,42.34H448.5M448.5,12.25V81.17M469.2,3.69H448.5\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M496.8,76.98H517.5M517.5,58.64V118.83M538.2,65.98H517.5\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M565.8,100.34H586.5M586.5,94.51V123.12M607.2,56.24H586.5\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M634.8,76.37H655.5M655.5,53.14V88.35M676.2,91.58H655.5\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"325\" transform=\"translate(64.5,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"325\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"325\">2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"325\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(202.5,0)\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"325\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(340.5,0)\">Jan 5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"325\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(478.5,0)\">Jan 7</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"325\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(616.5,0)\">Jan 9</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,302.78)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">−1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,250.84)\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,198.91)\">1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,146.97)\">2</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,95.03)\">3</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-0ee5bf\"><g class=\"clips\"/><clipPath id=\"legend0ee5bf\"><rect width=\"79\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(641,12.210526315789501)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"79\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legend0ee5bf)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">OHLC</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendohlc\" d=\"M15,0H0M8,6V0\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; fill: none; stroke-width: 2px; stroke: rgb(217, 95, 2); stroke-opacity: 1;\"/><path class=\"legendohlc\" d=\"M-15,0H0M-8,-6V0\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; fill: none; stroke-width: 2px; stroke: rgb(27, 158, 118); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"76.03125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"375\" y=\"367.909375\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Index</text></g><g class=\"g-ytitle\"/><g class=\"annotation\" data-index=\"0\" style=\"opacity: 1;\"><g class=\"annotation-text-g\" transform=\"rotate(0,375,55)\"><g class=\"cursor-pointer\" transform=\"translate(351,43)\"><rect class=\"bg\" x=\"0.5\" y=\"0.5\" width=\"48\" height=\"23\" style=\"stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><text class=\"annotation-text\" text-anchor=\"middle\" x=\"24.359375\" y=\"18\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">OHLC</text></g></g></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ohlcv_ts.vbt.ohlcv.plots().show_svg()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {"2e398c67d83547b7b18c2c71eee3d13d": {"buffers": [{"data": "RnjBo8Yt7r/6/PV6zpbIv14H7K6rSuG/zMS+hM8q2j/K8wKK0ZHhPwO+H4Tjfue/FB9ziWca8L9dbsMziiHwP2jOsMtmhbq/THo0sY8X9T8=", "encoding": "base64", "path": ["_data", 0, "close", "value"]}, {"data": "G2yPWXSX1L805X9UXv3kP3vxHYb7PeI/BCY+QDzJzj9ydwtWve/jP0D/4SSIStE/jM9Ijb2Wyj/JglqsK/rwP0UwfFFgY/E/nwQuay63AEA=", "encoding": "base64", "path": ["_data", 0, "high", "value"]}, {"data": "uoy35UmW5r8uuPRCrXjCvywfntNQXva/gRfybgry7b+M0GcYG03cv0il/ILWFeO/kTFfYU9k7790CRb/61jFv3flYrjYnem/3OZTbr4e6T8=", "encoding": "base64", "path": ["_data", 0, "low", "value"]}, {"data": "/b3pTFEl279cSb9dazSzP+wJwCcrxNq/AFDbzy2Fab8Mvy53ApnTP5JH+gNKbtK/AMob9Zy9z7+mipGviqnkPxzi2mPs7cI/Guaczvmb8T8=", "encoding": "base64", "path": ["_data", 0, "open", "value"]}, {"data": "HCrjrash4T/QOMtYcTTCP4KfTWJ92+G/EBxBYKG5+L+iVW+Wi5P7v0PV/JMP8fO/nnZcSFR+/b+Tj20F8pj8vze5F96ASwHAcyrG2aX8/r8=", "encoding": "base64", "path": ["_data", 1, "y", "value"]}], "model_module": "plotly<PERSON>dget", "model_module_version": "^4.12.0", "model_name": "FigureModel", "state": {"_config": {"plotlyServerURL": "https://plot.ly"}, "_data": [{"close": {"dtype": "float64", "shape": [10], "value": {}}, "decreasing": {"line": {"color": "#d95f02"}}, "high": {"dtype": "float64", "shape": [10], "value": {}}, "increasing": {"line": {"color": "#1b9e76"}}, "low": {"dtype": "float64", "shape": [10], "value": {}}, "name": "OHLC", "open": {"dtype": "float64", "shape": [10], "value": {}}, "type": "ohlc", "uid": "08dcba2b-99cf-4032-bf56-b75e9437eb59", "x": ["2018-01-01T00:00:00.000000", "2018-01-02T00:00:00.000000", "2018-01-03T00:00:00.000000", "2018-01-04T00:00:00.000000", "2018-01-05T00:00:00.000000", "2018-01-06T00:00:00.000000", "2018-01-07T00:00:00.000000", "2018-01-08T00:00:00.000000", "2018-01-09T00:00:00.000000", "2018-01-10T00:00:00.000000"], "xaxis": "x", "yaxis": "y2"}, {"marker": {"color": ["#d95f02", "#d95f02", "#d95f02", "#1b9e76", "#1b9e76", "#d95f02", "#d95f02", "#1b9e76", "#d95f02", "#1b9e76"], "line": {"width": 0}, "opacity": 0.7}, "name": "Volume", "type": "bar", "uid": "b7b27803-d0f2-4141-8e61-edaabd1d9a8e", "x": ["2018-01-01T00:00:00.000000", "2018-01-02T00:00:00.000000", "2018-01-03T00:00:00.000000", "2018-01-04T00:00:00.000000", "2018-01-05T00:00:00.000000", "2018-01-06T00:00:00.000000", "2018-01-07T00:00:00.000000", "2018-01-08T00:00:00.000000", "2018-01-09T00:00:00.000000", "2018-01-10T00:00:00.000000"], "xaxis": "x", "y": {"dtype": "float64", "shape": [10], "value": {}}, "yaxis": "y"}], "_js2py_layoutDelta": {}, "_js2py_pointsCallback": {}, "_js2py_relayout": {}, "_js2py_restyle": {}, "_js2py_traceDeltas": {}, "_js2py_update": {}, "_last_layout_edit_id": 6, "_last_trace_edit_id": 6, "_layout": {"autosize": false, "bargap": 0, "colorway": ["#1f77b4", "#ff7f0e", "#2ca02c", "#dc3912", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"], "height": 350, "hovermode": "closest", "legend": {"orientation": "h", "traceorder": "normal", "x": 1, "xanchor": "right", "y": 1.02, "yanchor": "bottom"}, "margin": {"b": 30, "l": 30, "r": 30, "t": 30}, "showlegend": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 700, "xaxis": {"rangeslider": {"visible": false}, "showgrid": true}, "yaxis": {"domain": [0, 0.33], "showgrid": true}, "yaxis2": {"domain": [0.33, 1]}}, "_py2js_animate": {}, "_py2js_deleteTraces": {}, "_py2js_moveTraces": {}, "_py2js_removeLayoutProps": {}, "_py2js_removeTraceProps": {}, "_py2js_restyle": {}, "_view_count": 0}}, "ffbf570b1fd14d3da6bd1e2c4d55078b": {"buffers": [{"data": "RnjBo8Yt7r/6/PV6zpbIv14H7K6rSuG/zMS+hM8q2j/K8wKK0ZHhPwO+H4Tjfue/FB9ziWca8L9dbsMziiHwP2jOsMtmhbq/THo0sY8X9T8=", "encoding": "base64", "path": ["_data", 0, "close", "value"]}, {"data": "G2yPWXSX1L805X9UXv3kP3vxHYb7PeI/BCY+QDzJzj9ydwtWve/jP0D/4SSIStE/jM9Ijb2Wyj/JglqsK/rwP0UwfFFgY/E/nwQuay63AEA=", "encoding": "base64", "path": ["_data", 0, "high", "value"]}, {"data": "uoy35UmW5r8uuPRCrXjCvywfntNQXva/gRfybgry7b+M0GcYG03cv0il/ILWFeO/kTFfYU9k7790CRb/61jFv3flYrjYnem/3OZTbr4e6T8=", "encoding": "base64", "path": ["_data", 0, "low", "value"]}, {"data": "/b3pTFEl279cSb9dazSzP+wJwCcrxNq/AFDbzy2Fab8Mvy53ApnTP5JH+gNKbtK/AMob9Zy9z7+mipGviqnkPxzi2mPs7cI/Guaczvmb8T8=", "encoding": "base64", "path": ["_data", 0, "open", "value"]}, {"data": "HCrjrash4T/QOMtYcTTCP4KfTWJ92+G/EBxBYKG5+L+iVW+Wi5P7v0PV/JMP8fO/nnZcSFR+/b+Tj20F8pj8vze5F96ASwHAcyrG2aX8/r8=", "encoding": "base64", "path": ["_data", 1, "y", "value"]}], "model_module": "plotly<PERSON>dget", "model_module_version": "^4.12.0", "model_name": "FigureModel", "state": {"_config": {"plotlyServerURL": "https://plot.ly"}, "_data": [{"close": {"dtype": "float64", "shape": [10], "value": {}}, "decreasing": {"line": {"color": "#d95f02"}}, "high": {"dtype": "float64", "shape": [10], "value": {}}, "increasing": {"line": {"color": "#1b9e76"}}, "low": {"dtype": "float64", "shape": [10], "value": {}}, "name": "OHLC", "open": {"dtype": "float64", "shape": [10], "value": {}}, "type": "ohlc", "uid": "bc161987-eb09-4d14-885c-9de5ce70c55b", "x": ["2018-01-01T00:00:00.000000", "2018-01-02T00:00:00.000000", "2018-01-03T00:00:00.000000", "2018-01-04T00:00:00.000000", "2018-01-05T00:00:00.000000", "2018-01-06T00:00:00.000000", "2018-01-07T00:00:00.000000", "2018-01-08T00:00:00.000000", "2018-01-09T00:00:00.000000", "2018-01-10T00:00:00.000000"], "xaxis": "x", "yaxis": "y2"}, {"marker": {"color": ["#d95f02", "#d95f02", "#d95f02", "#1b9e76", "#1b9e76", "#d95f02", "#d95f02", "#1b9e76", "#d95f02", "#1b9e76"], "line": {"width": 0}, "opacity": 0.7}, "name": "Volume", "type": "bar", "uid": "a755c6b7-2976-4a9e-bc6a-217b14ba7701", "x": ["2018-01-01T00:00:00.000000", "2018-01-02T00:00:00.000000", "2018-01-03T00:00:00.000000", "2018-01-04T00:00:00.000000", "2018-01-05T00:00:00.000000", "2018-01-06T00:00:00.000000", "2018-01-07T00:00:00.000000", "2018-01-08T00:00:00.000000", "2018-01-09T00:00:00.000000", "2018-01-10T00:00:00.000000"], "xaxis": "x", "y": {"dtype": "float64", "shape": [10], "value": {}}, "yaxis": "y"}], "_js2py_layoutDelta": {}, "_js2py_pointsCallback": {}, "_js2py_relayout": {}, "_js2py_restyle": {}, "_js2py_traceDeltas": {}, "_js2py_update": {}, "_last_layout_edit_id": 6, "_last_trace_edit_id": 6, "_layout": {"autosize": false, "bargap": 0, "colorway": ["#1f77b4", "#ff7f0e", "#2ca02c", "#dc3912", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"], "height": 350, "hovermode": "closest", "legend": {"orientation": "h", "traceorder": "normal", "x": 1, "xanchor": "right", "y": 1.02, "yanchor": "bottom"}, "margin": {"b": 30, "l": 30, "r": 30, "t": 30}, "showlegend": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 700, "xaxis": {"rangeslider": {"visible": false}, "showgrid": true}, "yaxis": {"domain": [0, 0.33], "showgrid": true}, "yaxis2": {"domain": [0.33, 1]}}, "_py2js_animate": {}, "_py2js_deleteTraces": {}, "_py2js_moveTraces": {}, "_py2js_removeLayoutProps": {}, "_py2js_removeTraceProps": {}, "_py2js_restyle": {}, "_view_count": 0}}}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}