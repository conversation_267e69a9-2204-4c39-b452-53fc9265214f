<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="350" style="" viewBox="0 0 700 350"><rect x="0" y="0" width="700" height="350" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-7ea37c"><g class="clips"><clipPath id="clip7ea37cxyplot" class="plotclip"><rect width="637" height="274"/></clipPath><clipPath class="axesclip" id="clip7ea37cx"><rect x="33" y="0" width="637" height="350"/></clipPath><clipPath class="axesclip" id="clip7ea37cy"><rect x="0" y="46" width="700" height="274"/></clipPath><clipPath class="axesclip" id="clip7ea37cxy"><rect x="33" y="46" width="637" height="274"/></clipPath></g><g class="gradients"/></defs><g class="bglayer"><rect class="bg" x="33" y="46" width="637" height="274" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"/><g class="y"><path class="ygrid crisp" transform="translate(0,306.3)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,265.2)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,224.1)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,183)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,141.9)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,100.8)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,59.7)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"/><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(33, 46)" clip-path="url('#clip7ea37cxyplot')"><g class="boxlayer mlayer"><g class="trace boxes" style="opacity: 1;"><path class="box" d="M81.22,178.1H237.28M81.22,239.75H237.28V116.45H81.22ZM159.25,239.75V260.3M159.25,116.45V95.9M120.23,260.3H198.27M120.23,95.9H198.27" style="vector-effect: non-scaling-stroke; stroke-width: 2px; stroke: rgb(31, 119, 180); stroke-opacity: 1; fill: rgb(31, 119, 180); fill-opacity: 0.5;"/><g class="points"/></g><g class="trace boxes" style="opacity: 1;"><path class="box" d="M399.72,178.1H555.78M399.72,239.75H555.78V54.8H399.72ZM477.75,239.75V260.3M477.75,54.8V13.7M438.73,260.3H516.77M438.73,13.7H516.77" style="vector-effect: non-scaling-stroke; stroke-width: 2px; stroke: rgb(255, 127, 14); stroke-opacity: 1; fill: rgb(255, 127, 14); fill-opacity: 0.5;"/><g class="points"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(192.25,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">a</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(510.75,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">b</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,306.3)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">1</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,265.2)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">1.5</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,224.1)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">2</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,183)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">2.5</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,141.9)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">3</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,100.8)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">3.5</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,59.7)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">4</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-7ea37c"><g class="clips"/><clipPath id="legend7ea37c"><rect width="105" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(565, 11.519999999999996)"><rect class="bg" shape-rendering="crispEdges" width="105" height="29" x="0" y="0" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;"/><g class="scrollbox" transform="translate(0, 0)" clip-path="url('#legend7ea37c')"><g class="groups"><g class="traces" transform="translate(0, 14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">a</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="legendbox" d="M6,6H-6V-6H6Z" transform="translate(20,0)" style="stroke-width: 2px; fill: rgb(31, 119, 180); fill-opacity: 0.5; stroke: rgb(31, 119, 180); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="49.71875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(52.21875, 14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">b</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="legendbox" d="M6,6H-6V-6H6Z" transform="translate(20,0)" style="stroke-width: 2px; fill: rgb(255, 127, 14); fill-opacity: 0.5; stroke: rgb(255, 127, 14); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="49.984375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" x="0" y="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-ytitle"/></g></svg>