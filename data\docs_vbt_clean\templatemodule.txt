template moduleUtilities for working with templates. deep_substitute functiondeep_substitute ( obj , mapping = None , safe = False , make_copy = True ) Traverses the object recursively and, if any template found, substitutes it using a mapping. Traverses tuples, lists, dicts and (frozen-)sets. Does not look for templates in keys. If safe is True, won't raise an error but return the original template. Note If the object is deep (such as a dict or a list), creates a copy of it if any template found inside, thus loosing the reference to the original. Make sure to do a deep or hybrid copy of the object before proceeding for consistent behavior, or disable make_copy to override the original in place. Usage import vectorbt as vbt vbt . deep_substitute ( vbt . Sub ( '$key' , { 'key' : 100 })) 100 vbt . deep_substitute ( vbt . Sub ( '$key' , { 'key' : 100 }), { 'key' : 200 }) 200 vbt . deep_substitute ( vbt . Sub ( '$key$key' ), { 'key' : 100 }) 100100 vbt . deep_substitute ( vbt . Rep ( 'key' ), { 'key' : 100 }) 100 vbt . deep_substitute ([ vbt . Rep ( 'key' ), vbt . Sub ( '$key$key' )], { 'key' : 100 }) [100, '100100'] vbt . deep_substitute ( vbt . RepFunc ( lambda key : key == 100 ), { 'key' : 100 }) True vbt . deep_substitute ( vbt . RepEval ( 'key == 100' ), { 'key' : 100 }) True vbt . deep_substitute ( vbt . RepEval ( 'key == 100' , safe = False )) NameError: name 'key' is not defined vbt . deep_substitute ( vbt . RepEval ( 'key == 100' , safe = True )) <vectorbt.utils.template.RepEval at 0x7fe3ad2ab668> has_templates functionhas_templates ( obj ) Check if the object has any templates. Rep classRep ( key , mapping = None ) Key to be replaced with the respective value from mapping . Superclasses SafeToStr key propertyKey to be replaced. mapping propertyMapping object passed to the initializer. replace methodRep . replace ( mapping = None ) Replace Rep.key using mapping . Merges mapping and Rep.mapping . RepEval classRepEval ( expression , mapping = None ) Expression to be evaluated with mapping used as locals. Superclasses SafeToStr eval methodRepEval . eval ( mapping = None ) Evaluate RepEval.expression using mapping . Merges mapping and RepEval.mapping . expression propertyExpression to be evaluated. mapping propertyMapping object passed to the initializer. RepFunc classRepFunc ( func , mapping = None ) Function to be called with argument names from mapping . Superclasses SafeToStr call methodRepFunc . call ( mapping = None ) Call RepFunc.func using mapping . Merges mapping and RepFunc.mapping . func propertyReplacement function to be called. mapping propertyMapping object passed to the initializer. Sub classSub ( template , mapping = None ) Template to substitute parts of the string with the respective values from mapping . Returns a string. Superclasses SafeToStr mapping propertyMapping object passed to the initializer. substitute methodSub . substitute ( mapping = None ) Substitute parts of Sub.template using mapping . Merges mapping and Sub.mapping . template propertyTemplate to be processed.