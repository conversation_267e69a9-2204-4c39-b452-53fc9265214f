array_ moduleUtilities for working with arrays. get_ranges_arr functionget_ranges_arr ( starts , ends ) Build array from start and end indices. Based on https://stackoverflow.com/a/37626057 insert_argsort_nb functioninsert_argsort_nb ( A , I ) Perform argsort using insertion sort. In-memory and without recursion -> very fast for smaller arrays. is_sorted functionis_sorted ( a ) Checks if array is sorted. is_sorted_nb functionis_sorted_nb ( a ) Numba-compiled version of is_sorted() . max_rel_rescale functionmax_rel_rescale ( a , to_range ) Rescale elements in a relatively to maximum. min_rel_rescale functionmin_rel_rescale ( a , to_range ) Rescale elements in a relatively to minimum. renormalize functionrenormalize ( a , from_range , to_range ) Renormalize a from one range to another. renormalize_nb functionrenormalize ( a , from_range , to_range ) Renormalize a from one range to another. rescale_float_to_int_nb functionrescale_float_to_int_nb ( floats , int_range , total ) Rescale a float array into an int array. uniform_summing_to_one_nb functionuniform_summing_to_one_nb ( n ) Generate random floats summing to one. See # https://stackoverflow.com/a/2640067/8141780