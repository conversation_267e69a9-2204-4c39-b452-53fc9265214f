{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# plotting"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import vectorbt as vbt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"Collapsed": "false"}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from datetime import datetime\n", "from numba import njit\n", "import itertools\n", "import ipywidgets"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(100, 100)\n"]}], "source": ["big_df = pd.DataFrame(np.random.uniform(size=(100, 100)).astype(float))\n", "big_df.columns = list(map(str, big_df.columns))\n", "print(big_df.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Indicator"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'domain': {'x': [0, 1], 'y': [0, 1]},\n", "              'gauge': {'axis': {'range': […"]}, "metadata": {}, "output_type": "display_data"}], "source": ["gauge = vbt.plotting.Gauge(value=0, value_range=(-1, 1))\n", "gauge.fig"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["gauge.update(1)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["172 ms ± 1.65 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "468 µs ± 862 ns per loop (mean ± std. dev. of 7 runs, 1000 loops each)\n"]}], "source": ["%timeit vbt.plotting.Gauge(value=0)\n", "\n", "big_gauge = vbt.plotting.Gauge(value=0)\n", "%timeit big_gauge.update(0)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["ipywidgets.Widget.close_all()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Bar"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'name': 'a',\n", "              'showlegend': True,\n", "              'type': 'bar',\n", "     …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["bar = pd.DataFrame([[1, 2], [3, 4], [5, 6]], columns=['a', 'b']).vbt.barplot(return_fig=False)\n", "bar.fig"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["bar.update([[7, 8], [9, 10], [11, 12]])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'name': 'a',\n", "              'showlegend': True,\n", "              'type': 'bar',\n", "     …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["bar1 = pd.DataFrame([[1, 2], [3, 4], [5, 6]], columns=['a', 'b']).vbt.barplot(return_fig=False)\n", "bar2 = pd.DataFrame([[7, 8], [9, 10], [11, 12]], columns=['c', 'd']).vbt.barplot(return_fig=False, fig=bar1.fig)\n", "bar2.fig"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["bar1.update([[7, 8], [9, 10], [11, 12]])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["bar2.update([[1, 2], [3, 4], [5, 6]])"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["IOPub message rate exceeded.\n", "The notebook server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--NotebookApp.iopub_msg_rate_limit`.\n", "\n", "Current values:\n", "NotebookApp.iopub_msg_rate_limit=1000.0 (msgs/sec)\n", "NotebookApp.rate_limit_window=3.0 (secs)\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["350 ms ± 36.7 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "4.05 ms ± 17.3 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["%timeit pd.DataFrame(big_df).vbt.barplot()\n", "\n", "big_bar = pd.DataFrame(big_df).vbt.barplot(return_fig=False)\n", "%timeit big_bar.update(big_df.values * 2)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["ipywidgets.Widget.close_all()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON>er"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'name': 'a',\n", "              'showlegend': True,\n", "              'type': 'scatter',\n", " …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["scatter = pd.DataFrame([[1, 2], [3, 4], [5, 6]], columns=['a', 'b']).vbt.plot(return_fig=False)\n", "scatter.fig"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["scatter.update([[6, 5], [4, 3], [2, 1]])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'name': 'a',\n", "              'showlegend': True,\n", "              'type': 'scatter',\n", " …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["scatter1 = pd.DataFrame([[1, 2], [3, 4], [5, 6]], columns=['a', 'b']).vbt.plot(return_fig=False)\n", "scatter2 = pd.DataFrame([[7, 8], [9, 10], [11, 12]], columns=['c', 'd']).vbt.plot(return_fig=False, fig=scatter1.fig)\n", "scatter2.fig"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["scatter1.update([[7, 8], [9, 10], [11, 12]])"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["scatter2.update([[1, 2], [3, 4], [5, 6]])"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["IOPub message rate exceeded.\n", "The notebook server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--NotebookApp.iopub_msg_rate_limit`.\n", "\n", "Current values:\n", "NotebookApp.iopub_msg_rate_limit=1000.0 (msgs/sec)\n", "NotebookApp.rate_limit_window=3.0 (secs)\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2.34 ms ± 48.3 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["%timeit pd.DataFrame(big_df).vbt.plot()\n", "\n", "big_scatter = pd.DataFrame(big_df).vbt.plot(return_fig=False)\n", "%timeit big_scatter.update(big_df.values * 2)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["ipywidgets.Widget.close_all()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Histogram"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'name': 'a',\n", "              'opacity': 0.75,\n", "              'showlegend': True,\n", "   …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["hist = pd.DataFrame([[1, 2], [3, 4], [2, 1]], columns=['a', 'b']).vbt.histplot(return_fig=False)\n", "hist.fig"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["hist.update([[4, 9], [4, 5], [3, 0]])"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'name': 'a',\n", "              'opacity': 0.75,\n", "              'showlegend': True,\n", "   …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pd.DataFrame([[1, 2], [3, 4], [2, 1]], columns=['a', 'b']).vbt.histplot(horizontal=True)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'name': 'a',\n", "              'opacity': 0.75,\n", "              'showlegend': True,\n", "   …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["hist1 = pd.DataFrame([[1, 2], [3, 4], [2, 1]], columns=['a', 'b']).vbt.histplot(return_fig=False)\n", "hist2 = pd.DataFrame([[4, 9], [4, 5], [3, 0]], columns=['c', 'd']).vbt.histplot(return_fig=False, fig=hist1.fig)\n", "hist2.fig"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["hist1.update([[4, 9], [4, 5], [3, 0]])"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["hist2.update([[1, 2], [3, 4], [2, 1]])"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["IOPub message rate exceeded.\n", "The notebook server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--NotebookApp.iopub_msg_rate_limit`.\n", "\n", "Current values:\n", "NotebookApp.iopub_msg_rate_limit=1000.0 (msgs/sec)\n", "NotebookApp.rate_limit_window=3.0 (secs)\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["331 ms ± 24.6 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "4.32 ms ± 43.2 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["%timeit pd.DataFrame(big_df).vbt.histplot()\n", "\n", "big_hist = pd.DataFrame(big_df).vbt.histplot(return_fig=False)\n", "%timeit big_hist.update(big_df.values * 2)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["ipywidgets.Widget.close_all()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Box"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'name': 'a',\n", "              'showlegend': True,\n", "              'type': 'box',\n", "     …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["box = pd.DataFrame([[1, 2], [3, 4], [2, 1]], columns=['a', 'b']).vbt.boxplot(return_fig=False)\n", "box.fig"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["box.update([[4, 9], [4, 5], [3, 0]])"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'name': 'a',\n", "              'showlegend': True,\n", "              'type': 'box',\n", "     …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pd.DataFrame([[1, 2], [3, 4], [2, 1]], columns=['a', 'b']).vbt.boxplot(horizontal=True)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'name': 'a',\n", "              'showlegend': True,\n", "              'type': 'box',\n", "     …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["box1 = pd.DataFrame([[1, 2], [3, 4], [2, 1]], columns=['a', 'b']).vbt.boxplot(return_fig=False)\n", "box2 = pd.DataFrame([[4, 9], [4, 5], [3, 0]], columns=['c', 'd']).vbt.boxplot(return_fig=False, fig=box1.fig)\n", "box2.fig"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["box1.update([[4, 9], [4, 5], [3, 0]])"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["box2.update([[1, 2], [3, 4], [2, 1]])"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["IOPub message rate exceeded.\n", "The notebook server will temporarily stop sending output\n", "to the client in order to avoid crashing it.\n", "To change this limit, set the config variable\n", "`--NotebookApp.iopub_msg_rate_limit`.\n", "\n", "Current values:\n", "NotebookApp.iopub_msg_rate_limit=1000.0 (msgs/sec)\n", "NotebookApp.rate_limit_window=3.0 (secs)\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["329 ms ± 25.5 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "4.29 ms ± 36.6 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["%timeit pd.DataFrame(big_df).vbt.boxplot()\n", "\n", "big_box = pd.DataFrame(big_df).vbt.boxplot(return_fig=False)\n", "%timeit big_box.update(big_df.values * 2)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["ipywidgets.Widget.close_all()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Heatmap"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'colorscale': [[0.0, '#0d0887'], [0.1111111111111111, '#46039f'],\n", "               …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["heatmap = pd.DataFrame(\n", "    [[1, 2], [3, 4], [5, 6]], \n", "    columns=['a', 'b'], \n", "    index=['x', 'y', 'z']\n", ").vbt.heatmap(return_fig=False)\n", "heatmap.fig"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["heatmap.update([[6, 5], [4, 3], [2, 1]])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'colorscale': [[0.0, '#0d0887'], [0.1111111111111111, '#46039f'],\n", "               …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pd.DataFrame(\n", "    [[1, 2], [3, 4], [5, 6]], \n", "    columns=['a', 'b'], \n", "    index=['x', 'y', 'z']\n", ").vbt.ts_heatmap()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'colorscale': [[0.0, '#0d0887'], [0.1111111111111111, '#46039f'],\n", "               …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["heatmap1 = pd.DataFrame([[1, 2], [3, 4], [5, 6]], columns=['a', 'b'], index=['x', 'y', 'z']).vbt.heatmap(\n", "    return_fig=False, trace_kwargs=dict(showscale=False))\n", "heatmap2 = pd.DataFrame([[6, 5], [4, 3], [2, 1]], columns=['c', 'd'], index=['x2', 'y2', 'z2']).vbt.heatmap(\n", "    return_fig=False, fig=heatmap1.fig)\n", "heatmap2.fig"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["heatmap1.update([[6, 5], [4, 3], [2, 1]])"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["heatmap2.update([[1, 2], [3, 4], [5, 6]])"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'colorscale': [[0.0, '#0d0887'], [0.1111111111111111, '#46039f'],\n", "               …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["heatmap_sr = pd.Series(\n", "    [1, 2, 3, 6, 5, 4], \n", "    index=vbt.base.index_fns.stack_indexes([\n", "        pd.Index(['i1', 'i2', 'i3', 'i1', 'i2', 'i3'], name='first'),\n", "        pd.Index(['i4', 'i5', 'i6', 'i4', 'i5', 'i6'], name='second'),\n", "        pd.Index(['i7', 'i7', 'i7', 'i8', 'i8', 'i8'], name='third')\n", "    ])\n", ")\n", "heatmap_sr.vbt.heatmap(x_level=0, y_level=1, symmetric=True)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'colorscale': [[0.0, '#0d0887'], [0.1111111111111111, '#46039f'],\n", "               …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["heatmap_sr.vbt.heatmap(x_level=0, y_level=1, symmetric=True, slider_level=2)"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["427 ms ± 53.3 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "112 µs ± 7.16 µs per loop (mean ± std. dev. of 7 runs, 10000 loops each)\n"]}], "source": ["%timeit pd.DataFrame(big_df).vbt.heatmap()\n", "\n", "big_heatmap = pd.DataFrame(big_df).vbt.heatmap(return_fig=False)\n", "%timeit big_heatmap.update(big_df.values * 2)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["ipywidgets.Widget.close_all()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Volume"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'colorscale': [[0.0, '#0d0887'], [0.1111111111111111, '#46039f'],\n", "               …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x, y, z, g = np.mgrid[0:15, 15:25, 25:30, :2]\n", "volume_sr = pd.Series(\n", "    np.random.randint(1, 10, size=x.flatten().shape), \n", "    index=vbt.base.index_fns.stack_indexes([\n", "        pd.Index(x.flatten(), name='first'),\n", "        pd.Index(y.flatten(), name='second'),\n", "        pd.Index(z.flatten(), name='third'),\n", "        pd.Index(g.flatten(), name='fourth')\n", "    ])\n", ")\n", "volume = volume_sr.vbt.volume(x_level='first', y_level='second', z_level='third', return_fig=False)\n", "volume.fig"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["volume.update(np.random.randint(1, 10, size=x.flatten().shape))"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "", "version_major": 2, "version_minor": 0}, "text/plain": ["FigureWidget({\n", "    'data': [{'colorscale': [[0.0, '#0d0887'], [0.1111111111111111, '#46039f'],\n", "               …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["volume_sr.vbt.volume(x_level='first', y_level='second', z_level='third', slider_level='fourth')"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.33 s ± 96.9 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "822 µs ± 478 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["x, y, z = np.mgrid[:50, :50, :50]\n", "big_volume_sr = pd.Series(\n", "    np.random.randint(1, 10, size=x.flatten().shape), \n", "    index=vbt.base.index_fns.stack_indexes(\n", "        pd.Index(x.flatten(), name='i1'),\n", "        pd.Index(y.flatten(), name='i2'),\n", "        pd.Index(z.flatten(), name='i3')\n", "    )\n", ")\n", "%timeit big_volume_sr.vbt.volume()\n", "\n", "big_volume = big_volume_sr.vbt.volume(return_fig=False)\n", "%timeit big_volume.update(big_volume_sr.values * 2)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["ipywidgets.Widget.close_all()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}