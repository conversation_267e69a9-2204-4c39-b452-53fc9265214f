ohlcv_accessors moduleCustom pandas accessors for OHLC(V) data. Methods can be accessed as follows: OHLCVDFAccessor -> pd.DataFrame.vbt.ohlc.* OHLCVDFAccessor -> pd.DataFrame.vbt.ohlcv.* The accessors inherit vectorbt.generic.accessors . Note Accessors do not utilize caching. Column namesBy default, vectorbt searches for columns with names 'open', 'high', 'low', 'close', and 'volume' (case doesn't matter). You can change the naming either using ohlcv.column_names in settings , or by providing column_names directly to the accessor. import pandas as pd import vectorbt as vbt df = pd . DataFrame ({ ... 'my_open1' : [ 2 , 3 , 4 , 3.5 , 2.5 ], ... 'my_high2' : [ 3 , 4 , 4.5 , 4 , 3 ], ... 'my_low3' : [ 1.5 , 2.5 , 3.5 , 2.5 , 1.5 ], ... 'my_close4' : [ 2.5 , 3.5 , 4 , 3 , 2 ], ... 'my_volume5' : [ 10 , 11 , 10 , 9 , 10 ] ... }) # vectorbt can't find columns df . vbt . ohlcv . get_column ( 'open' ) None my_column_names = dict ( ... open = 'my_open1' , ... high = 'my_high2' , ... low = 'my_low3' , ... close = 'my_close4' , ... volume = 'my_volume5' , ... ) ohlcv_acc = df . vbt . ohlcv ( freq = 'd' , column_names = my_column_names ) ohlcv_acc . get_column ( 'open' ) 0 2.0 1 3.0 2 4.0 3 3.5 4 2.5 Name: my_open1, dtype: float64 StatsHint See StatsBuilderMixin.stats() and OHLCVDFAccessor.metrics . ohlcv_acc . stats () Start 0 End 4 Period 5 days 00:00:00 First Price 2.0 Lowest Price 1.5 Highest Price 4.5 Last Price 2.0 First Volume 10 Lowest Volume 9 Highest Volume 11 Last Volume 10 Name: agg_func_mean, dtype: object PlotsHint See PlotsBuilderMixin.plots() and OHLCVDFAccessor.subplots . OHLCVDFAccessor class has a single subplot based on OHLCVDFAccessor.plot() (without volume): ohlcv_acc . plots ( settings = dict ( plot_type = 'candlestick' )) OHLCVDFAccessor classOHLCVDFAccessor ( obj , column_names = None , ** kwargs ) Accessor on top of OHLCV data. For DataFrames only. Accessible through pd.DataFrame.vbt.ohlcv . Superclasses AttrResolver BaseAccessor BaseDFAccessor Configured Documented GenericAccessor GenericDFAccessor IndexingBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() BaseAccessor.align_to() BaseAccessor.apply() BaseAccessor.apply_and_concat() BaseAccessor.apply_on_index() BaseAccessor.broadcast() BaseAccessor.broadcast_to() BaseAccessor.combine() BaseAccessor.concat() BaseAccessor.drop_duplicate_levels() BaseAccessor.drop_levels() BaseAccessor.drop_redundant_levels() BaseAccessor.empty() BaseAccessor.empty_like() BaseAccessor.indexing_func() BaseAccessor.make_symmetric() BaseAccessor.rename_levels() BaseAccessor.repeat() BaseAccessor.select_levels() BaseAccessor.stack_index() BaseAccessor.tile() BaseAccessor.to_1d_array() BaseAccessor.to_2d_array() BaseAccessor.to_dict() BaseAccessor.unstack_to_array() BaseAccessor.unstack_to_df() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() GenericAccessor.apply_along_axis() GenericAccessor.apply_and_reduce() GenericAccessor.apply_mapping() GenericAccessor.applymap() GenericAccessor.barplot() GenericAccessor.bfill() GenericAccessor.binarize() GenericAccessor.boxplot() GenericAccessor.bshift() GenericAccessor.count() GenericAccessor.crossed_above() GenericAccessor.crossed_below() GenericAccessor.cumprod() GenericAccessor.cumsum() GenericAccessor.describe() GenericAccessor.diff() GenericAccessor.drawdown() GenericAccessor.ewm_mean() GenericAccessor.ewm_std() GenericAccessor.expanding_apply() GenericAccessor.expanding_max() GenericAccessor.expanding_mean() GenericAccessor.expanding_min() GenericAccessor.expanding_split() GenericAccessor.expanding_std() GenericAccessor.ffill() GenericAccessor.fillna() GenericAccessor.filter() GenericAccessor.fshift() GenericAccessor.get_drawdowns() GenericAccessor.get_ranges() GenericAccessor.groupby_apply() GenericAccessor.histplot() GenericAccessor.idxmax() GenericAccessor.idxmin() GenericAccessor.lineplot() GenericAccessor.max() GenericAccessor.maxabs_scale() GenericAccessor.mean() GenericAccessor.median() GenericAccessor.min() GenericAccessor.minmax_scale() GenericAccessor.normalize() GenericAccessor.pct_change() GenericAccessor.power_transform() GenericAccessor.product() GenericAccessor.quantile_transform() GenericAccessor.range_split() GenericAccessor.rebase() GenericAccessor.reduce() GenericAccessor.resample_apply() GenericAccessor.resolve_self() GenericAccessor.robust_scale() GenericAccessor.rolling_apply() GenericAccessor.rolling_max() GenericAccessor.rolling_mean() GenericAccessor.rolling_min() GenericAccessor.rolling_split() GenericAccessor.rolling_std() GenericAccessor.scale() GenericAccessor.scatterplot() GenericAccessor.shuffle() GenericAccessor.split() GenericAccessor.std() GenericAccessor.sum() GenericAccessor.to_mapped() GenericAccessor.to_returns() GenericAccessor.transform() GenericAccessor.value_counts() GenericAccessor.zscore() GenericDFAccessor.config GenericDFAccessor.df_accessor_cls GenericDFAccessor.drawdowns GenericDFAccessor.flatten_grouped() GenericDFAccessor.heatmap() GenericDFAccessor.iloc GenericDFAccessor.indexing_kwargs GenericDFAccessor.loc GenericDFAccessor.mapping GenericDFAccessor.obj GenericDFAccessor.ranges GenericDFAccessor.self_aliases GenericDFAccessor.squeeze_grouped() GenericDFAccessor.sr_accessor_cls GenericDFAccessor.ts_heatmap() GenericDFAccessor.wrapper GenericDFAccessor.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.select_one() Wrapping.select_one_from_obj() close propertyClose series. column_names propertyColumn names. get_column methodOHLCVDFAccessor . get_column ( col_name ) Get column from OHLCVDFAccessor.column_names . high propertyHigh series. low propertyLow series. metrics class variableMetrics supported by OHLCVDFAccessor . Co nf ig( { "start" : { "title" : "Start" , "calc_func" : "<function OHLCVDFAccessor.<lambda> at 0x12ddbb380>" , "agg_func" : null , "tags" : "wrapper" }, "end" : { "title" : "End" , "calc_func" : "<function OHLCVDFAccessor.<lambda> at 0x12ddbb420>" , "agg_func" : null , "tags" : "wrapper" }, "period" : { "title" : "Period" , "calc_func" : "<function OHLCVDFAccessor.<lambda> at 0x12ddbb4c0>" , "apply_to_timedelta" : true , "agg_func" : null , "tags" : "wrapper" }, "first_price" : { "title" : "First Price" , "calc_func" : "<function OHLCVDFAccessor.<lambda> at 0x12ddbb560>" , "resolve_ohlc" : true , "tags" : [ "ohlcv" , "ohlc" ] }, "lowest_price" : { "title" : "Lowest Price" , "calc_func" : "<function OHLCVDFAccessor.<lambda> at 0x12ddbb600>" , "resolve_ohlc" : true , "tags" : [ "ohlcv" , "ohlc" ] }, "highest_price" : { "title" : "Highest Price" , "calc_func" : "<function OHLCVDFAccessor.<lambda> at 0x12ddbb6a0>" , "resolve_ohlc" : true , "tags" : [ "ohlcv" , "ohlc" ] }, "last_price" : { "title" : "Last Price" , "calc_func" : "<function OHLCVDFAccessor.<lambda> at 0x12ddbb740>" , "resolve_ohlc" : true , "tags" : [ "ohlcv" , "ohlc" ] }, "first_volume" : { "title" : "First Volume" , "calc_func" : "<function OHLCVDFAccessor.<lambda> at 0x12ddbb7e0>" , "resolve_volume" : true , "tags" : [ "ohlcv" , "volume" ] }, "lowest_volume" : { "title" : "Lowest Volume" , "calc_func" : "<function OHLCVDFAccessor.<lambda> at 0x12ddbb880>" , "resolve_volume" : true , "tags" : [ "ohlcv" , "volume" ] }, "highest_volume" : { "title" : "Highest Volume" , "calc_func" : "<function OHLCVDFAccessor.<lambda> at 0x12ddbb920>" , "resolve_volume" : true , "tags" : [ "ohlcv" , "volume" ] }, "last_volume" : { "title" : "Last Volume" , "calc_func" : "<function OHLCVDFAccessor.<lambda> at 0x12ddbb9c0>" , "resolve_volume" : true , "tags" : [ "ohlcv" , "volume" ] } } ) Returns OHLCVDFAccessor._metrics , which gets (deep) copied upon creation of each instance. Thus, changing this config won't affect the class. To change metrics, you can either change the config in-place, override this property, or overwrite the instance variable OHLCVDFAccessor._metrics . ohlc propertyOpen, high, low, and close series. open propertyOpen series. plot methodOHLCVDFAccessor . plot ( plot_type = None , show_volume = None , ohlc_kwargs = None , volume_kwargs = None , ohlc_add_trace_kwargs = None , volume_add_trace_kwargs = None , fig = None , ** layout_kwargs ) Plot OHLCV data. Args plot_type Either 'OHLC', 'Candlestick' or Plotly trace. Pass None to use the default. show_volume : bool If True, shows volume as bar chart. ohlc_kwargs : dict Keyword arguments passed to plot_type . volume_kwargs : dict Keyword arguments passed to plotly.graph_objects.Bar . ohlc_add_trace_kwargs : dict Keyword arguments passed to add_trace for OHLC. volume_add_trace_kwargs : dict Keyword arguments passed to add_trace for volume. fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage import vectorbt as vbt vbt . YFData . download ( "BTC-USD" ) . get () . vbt . ohlcv . plot () plots_defaults propertyDefaults for PlotsBuilderMixin.plots() . Merges GenericAccessor.plots_defaults and ohlcv.plots from settings . stats_defaults propertyDefaults for StatsBuilderMixin.stats() . Merges GenericAccessor.stats_defaults and ohlcv.stats from settings . subplots class variableSubplots supported by OHLCVDFAccessor . Co nf ig( { "plot" : { "title" : "OHLC" , "xaxis_kwargs" : { "showgrid" : true , "rangeslider_visible" : false }, "yaxis_kwargs" : { "showgrid" : true }, "check_is_not_grouped" : true , "plot_func" : "plot" , "show_volume" : false , "tags" : "ohlcv" } } ) Returns OHLCVDFAccessor._subplots , which gets (deep) copied upon creation of each instance. Thus, changing this config won't affect the class. To change subplots, you can either change the config in-place, override this property, or overwrite the instance variable OHLCVDFAccessor._subplots . volume propertyVolume series.