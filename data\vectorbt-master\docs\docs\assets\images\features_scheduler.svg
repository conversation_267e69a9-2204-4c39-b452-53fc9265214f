<svg class="main-svg" height="350" viewBox="0 0 700 350" width="700" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><rect height="350" style="fill: rgb(255, 255, 255); fill-opacity: 1;" width="700" x="0" y="0"/><defs id="defs-9a1015"><g class="clips"><clipPath class="plotclip" id="clip9a1015xyplot"><rect height="277" width="607"/></clipPath><clipPath class="axesclip" id="clip9a1015x"><rect height="350" width="607" x="63" y="0"/></clipPath><clipPath class="axesclip" id="clip9a1015y"><rect height="277" width="700" x="0" y="30"/></clipPath><clipPath class="axesclip" id="clip9a1015xy"><rect height="277" width="607" x="63" y="30"/></clipPath></g><g class="gradients"/><g class="patterns"/></defs><g class="bglayer"><rect class="bg" height="277" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;" width="607" x="63" y="30"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" d="M0,30v277" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;" transform="translate(151.06,0)"/><path class="xgrid crisp" d="M0,30v277" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;" transform="translate(272.78,0)"/><path class="xgrid crisp" d="M0,30v277" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;" transform="translate(394.49,0)"/><path class="xgrid crisp" d="M0,30v277" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;" transform="translate(516.21,0)"/><path class="xgrid crisp" d="M0,30v277" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;" transform="translate(637.93,0)"/></g><g class="y"><path class="ygrid crisp" d="M63,0h607" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;" transform="translate(0,249.54)"/><path class="ygrid crisp" d="M63,0h607" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;" transform="translate(0,162.31)"/><path class="ygrid crisp" d="M63,0h607" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;" transform="translate(0,75.08)"/></g></g><g class="zerolinelayer"/><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" clip-path="url(#clip9a1015xyplot)" transform="translate(63,30)"><g class="scatterlayer mlayer"><g class="trace scatter trace9abafe58-f35c-40d8-9a93-4d0c3b3b4dfd" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,86.25L60.74,86.77L68.16,159.52L115.33,127.95L119.4,153.59L122.2,127.95L127.44,127.95L205.03,127.95L206.49,13.85L210.02,127.95L222.19,36.88L292.18,36.88L293.28,99.86L300.52,99.86L301.55,117.65L301.98,117.65L302.65,205.58L303.07,205.58L314.45,213.95L397.71,214.13L409.7,227.91L427.04,227.91L440.13,263.15L461.37,262.98L550.65,262.98L607,262.98" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" text-anchor="middle" transform="translate(151.06,0)" x="0" y="320"><tspan class="line" dy="0em" x="0" y="320">11:47:02</tspan><tspan class="line" dy="1.3em" x="0" y="320">Dec 30, 2021</tspan></text></g><g class="xtick"><text style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" text-anchor="middle" transform="translate(272.78,0)" x="0" y="320">11:47:04</text></g><g class="xtick"><text style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" text-anchor="middle" transform="translate(394.49,0)" x="0" y="320">11:47:06</text></g><g class="xtick"><text style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" text-anchor="middle" transform="translate(516.21,0)" x="0" y="320">11:47:08</text></g><g class="xtick"><text style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" text-anchor="middle" transform="translate(637.93,0)" x="0" y="320">11:47:10</text></g></g><g class="yaxislayer-above"><g class="ytick"><text style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" text-anchor="end" transform="translate(0,249.54)" x="62" y="4.199999999999999">47.355k</text></g><g class="ytick"><text style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" text-anchor="end" transform="translate(0,162.31)" x="62" y="4.199999999999999">47.36k</text></g><g class="ytick"><text style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" text-anchor="end" transform="translate(0,75.08)" x="62" y="4.199999999999999">47.365k</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="iciclelayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-9a1015"><g class="clips"/></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-ytitle"/></g></svg>