orders moduleBase class for working with order records. Order records capture information on filled orders. Orders are mainly populated when simulating a portfolio and can be accessed as Portfolio.orders . import pandas as pd import numpy as np from datetime import datetime , timedelta import vectorbt as vbt np . random . seed ( 42 ) price = pd . DataFrame ({ ... 'a' : np . random . uniform ( 1 , 2 , size = 100 ), ... 'b' : np . random . uniform ( 1 , 2 , size = 100 ) ... }, index = [ datetime ( 2020 , 1 , 1 ) + timedelta ( days = i ) for i in range ( 100 )]) size = pd . DataFrame ({ ... 'a' : np . random . uniform ( - 1 , 1 , size = 100 ), ... 'b' : np . random . uniform ( - 1 , 1 , size = 100 ), ... }, index = [ datetime ( 2020 , 1 , 1 ) + timedelta ( days = i ) for i in range ( 100 )]) pf = vbt . Portfolio . from_orders ( price , size , fees = 0.01 , freq = 'd' ) orders = pf . orders orders . buy . count () a 58 b 51 Name: count, dtype: int64 orders . sell . count () a 42 b 49 Name: count, dtype: int64 StatsHint See StatsBuilderMixin.stats() and Orders.metrics . orders [ 'a' ] . stats () Start 2020-01-01 00:00:00 End 2020-04-09 00:00:00 Period 100 days 00:00:00 Total Records 100 Total Buy Orders 58 Total Sell Orders 42 Min Size 0.003033 Max Size 0.989877 Avg Size 0.508608 Avg Buy Size 0.468802 Avg Sell Size 0.563577 Avg Buy Price 1.437037 Avg Sell Price 1.515951 Total Fees 0.740177 Min Fees 0.000052 Max Fees 0.016224 Avg Fees 0.007402 Avg Buy Fees 0.006771 Avg Sell Fees 0.008273 Name: a, dtype: object StatsBuilderMixin.stats() also supports (re-)grouping: orders . stats ( group_by = True ) Start 2020-01-01 00:00:00 End 2020-04-09 00:00:00 Period 100 days 00:00:00 Total Records 200 Total Buy Orders 109 Total Sell Orders 91 Min Size 0.003033 Max Size 0.989877 Avg Size 0.506279 Avg Buy Size 0.472504 Avg Sell Size 0.546735 Avg Buy Price 1.47336 Avg Sell Price 1.496759 Total Fees 1.483343 Min Fees 0.000052 Max Fees 0.018319 Avg Fees 0.007417 Avg Buy Fees 0.006881 Avg Sell Fees 0.008058 Name: group, dtype: object PlotsHint See PlotsBuilderMixin.plots() and Orders.subplots . Orders class has a single subplot based on Orders.plot() : orders [ 'a' ] . plots () orders_attach_field_config ConfigConfig of fields to be attached to Orders . Co nf ig( { "side" : { "attach_filters" : true } } ) orders_field_config ConfigField config for Orders . Co nf ig( { "dtype" : { "id" : "int64" , "col" : "int64" , "idx" : "int64" , "size" : "float64" , "price" : "float64" , "fees" : "float64" , "side" : "int64" }, "settings" : { "id" : { "title" : "Order Id" }, "size" : { "title" : "Size" }, "price" : { "title" : "Price" }, "fees" : { "title" : "Fees" }, "side" : { "title" : "Side" , "mapping" : { "Buy" : 0 , "Sell" : 1 } } } } ) Orders classOrders ( wrapper , records_arr , close = None , ** kwargs ) Extends Records for working with order records. Superclasses AttrResolver Configured Documented IndexingBase PandasIndexer Pickleable PlotsBuilderMixin Records RecordsWithFields StatsBuilderMixin Wrapping Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.to_doc() Configured.update_config() PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() Records.apply() Records.apply_mask() Records.build_field_config_doc() Records.col_arr Records.col_mapper Records.config Records.count() Records.get_apply_mapping_arr() Records.get_by_col_idxs() Records.get_field_arr() Records.get_field_mapping() Records.get_field_name() Records.get_field_setting() Records.get_field_title() Records.get_map_field() Records.get_map_field_to_index() Records.id_arr Records.idx_arr Records.iloc Records.indexing_func_meta() Records.indexing_kwargs Records.is_sorted() Records.loc Records.map() Records.map_array() Records.map_field() Records.override_field_config_doc() Records.records Records.records_arr Records.records_readable Records.replace() Records.self_aliases Records.sort() Records.values Records.wrapper Records.writeable_attrs StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() buy methodRecords filtered by side == 0 . close propertyReference price such as close (optional). col methodMapped array of the field col . fees methodMapped array of the field fees . field_config class variableField config of Orders . Co nf ig( { "dtype" : { "id" : "int64" , "col" : "int64" , "idx" : "int64" , "size" : "float64" , "price" : "float64" , "fees" : "float64" , "side" : "int64" }, "settings" : { "id" : { "name" : "id" , "title" : "Order Id" }, "col" : { "name" : "col" , "title" : "Column" , "mapping" : "columns" }, "idx" : { "name" : "idx" , "title" : "Timestamp" , "mapping" : "index" }, "size" : { "title" : "Size" }, "price" : { "title" : "Price" }, "fees" : { "title" : "Fees" }, "side" : { "title" : "Side" , "mapping" : { "Buy" : 0 , "Sell" : 1 } } } } ) id methodMapped array of the field id . idx methodMapped array of the field idx . indexing_func methodOrders . indexing_func ( pd_indexing_func , ** kwargs ) Perform indexing on Orders . metrics class variableMetrics supported by Orders . Co nf ig( { "start" : { "title" : "Start" , "calc_func" : "<function Orders.<lambda> at 0x12d3f8ea0>" , "agg_func" : null , "tags" : "wrapper" }, "end" : { "title" : "End" , "calc_func" : "<function Orders.<lambda> at 0x12d3f8f40>" , "agg_func" : null , "tags" : "wrapper" }, "period" : { "title" : "Period" , "calc_func" : "<function Orders.<lambda> at 0x12d3f8fe0>" , "apply_to_timedelta" : true , "agg_func" : null , "tags" : "wrapper" }, "total_records" : { "title" : "Total Records" , "calc_func" : "count" , "tags" : "records" }, "total_buy_orders" : { "title" : "Total Buy Orders" , "calc_func" : "buy.count" , "tags" : [ "orders" , "buy" ] }, "total_sell_orders" : { "title" : "Total Sell Orders" , "calc_func" : "sell.count" , "tags" : [ "orders" , "sell" ] }, "min_size" : { "title" : "Min Size" , "calc_func" : "size.min" , "tags" : [ "orders" , "size" ] }, "max_size" : { "title" : "Max Size" , "calc_func" : "size.max" , "tags" : [ "orders" , "size" ] }, "avg_size" : { "title" : "Avg Size" , "calc_func" : "size.mean" , "tags" : [ "orders" , "size" ] }, "avg_buy_size" : { "title" : "Avg Buy Size" , "calc_func" : "buy.size.mean" , "tags" : [ "orders" , "buy" , "size" ] }, "avg_sell_size" : { "title" : "Avg Sell Size" , "calc_func" : "sell.size.mean" , "tags" : [ "orders" , "sell" , "size" ] }, "avg_buy_price" : { "title" : "Avg Buy Price" , "calc_func" : "buy.price.mean" , "tags" : [ "orders" , "buy" , "price" ] }, "avg_sell_price" : { "title" : "Avg Sell Price" , "calc_func" : "sell.price.mean" , "tags" : [ "orders" , "sell" , "price" ] }, "total_fees" : { "title" : "Total Fees" , "calc_func" : "fees.sum" , "tags" : [ "orders" , "fees" ] }, "min_fees" : { "title" : "Min Fees" , "calc_func" : "fees.min" , "tags" : [ "orders" , "fees" ] }, "max_fees" : { "title" : "Max Fees" , "calc_func" : "fees.max" , "tags" : [ "orders" , "fees" ] }, "avg_fees" : { "title" : "Avg Fees" , "calc_func" : "fees.mean" , "tags" : [ "orders" , "fees" ] }, "avg_buy_fees" : { "title" : "Avg Buy Fees" , "calc_func" : "buy.fees.mean" , "tags" : [ "orders" , "buy" , "fees" ] }, "avg_sell_fees" : { "title" : "Avg Sell Fees" , "calc_func" : "sell.fees.mean" , "tags" : [ "orders" , "sell" , "fees" ] } } ) Returns Orders._metrics , which gets (deep) copied upon creation of each instance. Thus, changing this config won't affect the class. To change metrics, you can either change the config in-place, override this property, or overwrite the instance variable Orders._metrics . plot methodOrders . plot ( column = None , close_trace_kwargs = None , buy_trace_kwargs = None , sell_trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Plot orders. Args column : str Name of the column to plot. close_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for Orders.close . buy_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for "Buy" markers. sell_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for "Sell" markers. add_trace_kwargs : dict Keyword arguments passed to add_trace . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage import pandas as pd from datetime import datetime , timedelta import vectorbt as vbt price = pd . Series ([ 1. , 2. , 3. , 2. , 1. ], name = 'Price' ) price . index = [ datetime ( 2020 , 1 , 1 ) + timedelta ( days = i ) for i in range ( len ( price ))] size = pd . Series ([ 1. , 1. , 1. , 1. , - 1. ]) orders = vbt . Portfolio . from_orders ( price , size ) . orders orders . plot () plots_defaults propertyDefaults for PlotsBuilderMixin.plots() . Merges Records.plots_defaults and orders.plots from settings . price methodMapped array of the field price . sell methodRecords filtered by side == 1 . side methodMapped array of the field side . size methodMapped array of the field size . stats_defaults propertyDefaults for StatsBuilderMixin.stats() . Merges Records.stats_defaults and orders.stats from settings . subplots class variableSubplots supported by Orders . Co nf ig( { "plot" : { "title" : "Orders" , "yaxis_kwargs" : { "title" : "Price" }, "check_is_not_grouped" : true , "plot_func" : "plot" , "tags" : "orders" } } ) Returns Orders._subplots , which gets (deep) copied upon creation of each instance. Thus, changing this config won't affect the class. To change subplots, you can either change the config in-place, override this property, or overwrite the instance variable Orders._subplots .