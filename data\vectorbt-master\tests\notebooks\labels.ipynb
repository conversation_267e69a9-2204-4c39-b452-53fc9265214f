{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# labels"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import vectorbt as vbt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"Collapsed": "false"}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "from numba import njit"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Disable caching for performance testing\n", "vbt.settings.caching['enabled'] = False"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["close = pd.DataFrame({\n", "    'a': [1, 2, 1, 2, 3, 2],\n", "    'b': [3, 2, 3, 2, 1, 2]\n", "}, index=pd.Index([\n", "    datetime(2020, 1, 1),\n", "    datetime(2020, 1, 2),\n", "    datetime(2020, 1, 3),\n", "    datetime(2020, 1, 4),\n", "    datetime(2020, 1, 5),\n", "    datetime(2020, 1, 6)\n", "]))\n", "\n", "pos_ths = [np.array([1, 1 / 2]), np.array([2, 1 / 2]), np.array([3, 1 / 2])]\n", "neg_ths = [np.array([1 / 2, 1 / 3]), np.array([1 / 2, 2 / 3]), np.array([1 / 2, 3 / 4])]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1000, 1000)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["big_close = pd.DataFrame(np.random.randint(1, 10, size=(1000, 1000)).astype(float))\n", "big_close.index = [datetime(2018, 1, 1) + timedelta(days=i) for i in range(1000)]\n", "big_close.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Look-ahead indicators"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["fmean_window     2                                  3                    \\\n", "fmean_ewm    False          True                False             True    \n", "                 a    b         a         b         a         b       a   \n", "2020-01-01     1.5  2.5  1.802469  2.197531  1.666667  2.333333  1.8125   \n", "2020-01-02     1.5  2.5  1.407407  2.592593  2.000000  2.000000  1.6250   \n", "2020-01-03     2.5  1.5  2.222222  1.777778  2.333333  1.666667  2.2500   \n", "2020-01-04     2.5  1.5  2.666667  1.333333       NaN       NaN     NaN   \n", "2020-01-05     NaN  NaN       NaN       NaN       NaN       NaN     NaN   \n", "2020-01-06     NaN  NaN       NaN       NaN       NaN       NaN     NaN   \n", "\n", "fmean_window          \n", "fmean_ewm             \n", "                   b  \n", "2020-01-01    2.1875  \n", "2020-01-02    2.3750  \n", "2020-01-03    1.7500  \n", "2020-01-04       NaN  \n", "2020-01-05       NaN  \n", "2020-01-06       NaN  \n", "11.2 ms ± 1.5 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "77.1 ms ± 1.63 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "(1000, 8000)\n"]}], "source": ["print(vbt.FMEAN.run(close, window=(2, 3), ewm=(False, True), param_product=True).fmean)\n", "\n", "%timeit vbt.FMEAN.run(big_close, window=2)\n", "%timeit vbt.FMEAN.run(big_close, window=np.arange(2, 10).tolist())\n", "\n", "print(vbt.FMEAN.run(big_close, window=np.arange(2, 10).tolist()).wrapper.shape)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["fstd_window     2                                  3                      \\\n", "fstd_ewm    False          True                False               True    \n", "                a    b         a         b         a         b         a   \n", "2020-01-01    0.5  0.5  0.644867  0.644867  0.471405  0.471405  0.646256   \n", "2020-01-02    0.5  0.5  0.883301  0.883301  0.816497  0.816497  0.859125   \n", "2020-01-03    0.5  0.5  0.591608  0.591608  0.471405  0.471405  0.547723   \n", "2020-01-04    0.5  0.5  0.707107  0.707107       NaN       NaN       NaN   \n", "2020-01-05    NaN  NaN       NaN       NaN       NaN       NaN       NaN   \n", "2020-01-06    NaN  NaN       NaN       NaN       NaN       NaN       NaN   \n", "\n", "fstd_window            \n", "fstd_ewm               \n", "                    b  \n", "2020-01-01   0.646256  \n", "2020-01-02   0.859125  \n", "2020-01-03   0.547723  \n", "2020-01-04        NaN  \n", "2020-01-05        NaN  \n", "2020-01-06        NaN  \n", "13.2 ms ± 565 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "107 ms ± 1.88 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "(1000, 8000)\n"]}], "source": ["print(vbt.FSTD.run(close, window=(2, 3), ewm=(False, True), param_product=True).fstd)\n", "\n", "%timeit vbt.FSTD.run(big_close, window=2)\n", "%timeit vbt.FSTD.run(big_close, window=np.arange(2, 10).tolist())\n", "\n", "print(vbt.FSTD.run(big_close, window=np.arange(2, 10).tolist()).wrapper.shape)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["fmin_window    2         3     \n", "               a    b    a    b\n", "2020-01-01   1.0  2.0  1.0  2.0\n", "2020-01-02   1.0  2.0  1.0  1.0\n", "2020-01-03   2.0  1.0  2.0  1.0\n", "2020-01-04   2.0  1.0  NaN  NaN\n", "2020-01-05   NaN  NaN  NaN  NaN\n", "2020-01-06   NaN  NaN  NaN  NaN\n", "9.06 ms ± 323 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "113 ms ± 875 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "(1000, 8000)\n"]}], "source": ["print(vbt.FMIN.run(close, window=(2, 3)).fmin)\n", "\n", "%timeit vbt.FMIN.run(big_close, window=2)\n", "%timeit vbt.FMIN.run(big_close, window=np.arange(2, 10).tolist())\n", "\n", "print(vbt.FMIN.run(big_close, window=np.arange(2, 10).tolist()).wrapper.shape)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["fmax_window    2         3     \n", "               a    b    a    b\n", "2020-01-01   2.0  3.0  2.0  3.0\n", "2020-01-02   2.0  3.0  3.0  3.0\n", "2020-01-03   3.0  2.0  3.0  2.0\n", "2020-01-04   3.0  2.0  NaN  NaN\n", "2020-01-05   NaN  NaN  NaN  NaN\n", "2020-01-06   NaN  NaN  NaN  NaN\n", "9.08 ms ± 418 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "114 ms ± 700 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "(1000, 8000)\n"]}], "source": ["print(vbt.FMAX.run(close, window=(2, 3)).fmax)\n", "\n", "%timeit vbt.FMAX.run(big_close, window=2)\n", "%timeit vbt.FMAX.run(big_close, window=np.arange(2, 10).tolist())\n", "\n", "print(vbt.FMAX.run(big_close, window=np.arange(2, 10).tolist()).wrapper.shape)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Label generators"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["fixlb_n       2              3          \n", "              a         b    a         b\n", "2020-01-01  0.0  0.000000  1.0 -0.333333\n", "2020-01-02  0.0  0.000000  0.5 -0.500000\n", "2020-01-03  2.0 -0.666667  1.0 -0.333333\n", "2020-01-04  0.0  0.000000  NaN       NaN\n", "2020-01-05  NaN       NaN  NaN       NaN\n", "2020-01-06  NaN       NaN  NaN       NaN\n", "3.11 ms ± 84.1 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "43.6 ms ± 2.82 ms per loop (mean ± std. dev. of 7 runs, 100 loops each)\n", "(1000, 8000)\n"]}], "source": ["print(vbt.FIXLB.run(close, n=(2, 3)).labels)\n", "\n", "%timeit vbt.FIXLB.run(big_close, n=2)\n", "%timeit vbt.FIXLB.run(big_close, n=np.arange(2, 10).tolist())\n", "\n", "print(vbt.FIXLB.run(big_close, n=np.arange(2, 10).tolist()).wrapper.shape)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"800\" height=\"350\" style=\"\" viewBox=\"0 0 800 350\"><rect x=\"0\" y=\"0\" width=\"800\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-2aca23\"><g class=\"clips\"><clipPath id=\"clip2aca23xyplot\" class=\"plotclip\"><rect width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath id=\"clip2aca23xy2plot\" class=\"plotclip\"><rect width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip2aca23x\"><rect x=\"48\" y=\"0\" width=\"617.5799999999999\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip2aca23y\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip2aca23xy\"><rect x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip2aca23y2\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip2aca23xy2\"><rect x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\"/></clipPath></g><g class=\"gradients\"><linearGradient x1=\"0\" x2=\"0\" y1=\"1\" y2=\"0\" id=\"g2aca23-cb02e2a0ad-aba9-4d5d-afbb-8528290e1c22\"><stop offset=\"0%\" stop-color=\"rgb(13, 8, 135)\" stop-opacity=\"1\"/><stop offset=\"11.11111111111111%\" stop-color=\"rgb(70, 3, 159)\" stop-opacity=\"1\"/><stop offset=\"22.22222222222222%\" stop-color=\"rgb(114, 1, 168)\" stop-opacity=\"1\"/><stop offset=\"33.33333333333333%\" stop-color=\"rgb(156, 23, 158)\" stop-opacity=\"1\"/><stop offset=\"44.44444444444444%\" stop-color=\"rgb(189, 55, 134)\" stop-opacity=\"1\"/><stop offset=\"55.55555555555556%\" stop-color=\"rgb(216, 87, 107)\" stop-opacity=\"1\"/><stop offset=\"66.66666666666666%\" stop-color=\"rgb(237, 121, 83)\" stop-opacity=\"1\"/><stop offset=\"77.77777777777779%\" stop-color=\"rgb(251, 159, 58)\" stop-opacity=\"1\"/><stop offset=\"88.88888888888889%\" stop-color=\"rgb(253, 202, 38)\" stop-opacity=\"1\"/><stop offset=\"100%\" stop-color=\"rgb(240, 249, 33)\" stop-opacity=\"1\"/></linearGradient></g></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(99.46000000000001,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(202.39,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(305.32,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(408.25,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(511.18,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(614.11,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,290.2)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,233.35)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,119.65)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,62.8)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"><path class=\"xy2-x\"/><path class=\"xy2-y\"/></g><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"><g class=\"xy2-x\"/><g class=\"xy2-y\"/></g><g class=\"plot\" transform=\"translate(48,46)\" clip-path=\"url('#clip2aca23xyplot')\"><g class=\"heatmaplayer mlayer\"><g class=\"hm\"><image xmlns=\"http://www.w3.org/2000/svg\" preserveAspectRatio=\"none\" height=\"261\" width=\"618\" x=\"0\" y=\"0\" xlink:href=\"data:image/png;base64,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\" style=\"opacity: 1;\"/></g></g></g><g class=\"overplot\"><g class=\"xy2\" transform=\"translate(48,46)\" clip-path=\"url('#clip2aca23xy2plot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace3b48906a-cbc0-44bb-9563-eb346a4df9df\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M51.46,244.2L154.39,130.5L257.32,244.2L463.18,16.8L566.11,130.5\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(51.46,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(154.39,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(257.32,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(360.25,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(463.18,16.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(566.11,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"text\"/></g></g></g></g><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"><path class=\"xy2-x crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"xy2-y crisp\" d=\"M0,0\" style=\"fill: none;\"/></g><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(99.46000000000001,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2020</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(202.39,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(305.32,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(408.25,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(511.18,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(614.11,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 6</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"47\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">labels</text></g></g><g class=\"overaxes-above\"><g class=\"xy2-x\"/><g class=\"xy2-y\"><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,290.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,233.35)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,119.65)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,62.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g></g></g></g><g class=\"subplot xy2\"/></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-2aca23\"><g class=\"clips\"/><clipPath id=\"legend2aca23\"><rect width=\"76\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(629,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"76\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend2aca23')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"72.734375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"cb02e2a0ad-aba9-4d5d-afbb-8528290e1c22 colorbar\" transform=\"translate(48,46)\"><rect class=\"cbbg\" x=\"670\" y=\"0\" width=\"69.640625\" height=\"261\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0; stroke: rgb(68, 68, 68); stroke-opacity: 1; stroke-width: 0;\"/><g class=\"cbfills\" transform=\"translate(0,10)\"><rect class=\"cbfill\" x=\"680\" width=\"30\" y=\"0\" height=\"241\" style=\"fill: url('#g2aca23-cb02e2a0ad-aba9-4d5d-afbb-8528290e1c22');\"/></g><g class=\"cblines\" transform=\"translate(0,10)\"/><g class=\"cbaxis crisp\" transform=\"translate(0,-46)\"><g class=\"ycb02e2a0ad-aba9-4d5d-afbb-8528290e1c22tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,297)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ycb02e2a0ad-aba9-4d5d-afbb-8528290e1c22tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,236.75)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g><g class=\"ycb02e2a0ad-aba9-4d5d-afbb-8528290e1c22tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"ycb02e2a0ad-aba9-4d5d-afbb-8528290e1c22tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,116.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"ycb02e2a0ad-aba9-4d5d-afbb-8528290e1c22tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,56)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g></g><g class=\"cbtitleunshift\" transform=\"translate(-48,-46)\"><g class=\"cbtitle\"/></g><rect class=\"cboutline\" x=\"680\" y=\"10\" width=\"30\" height=\"241\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: none; stroke-width: 0;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vbt.FIXLB.run(close['a'], n=2).plot().show_svg()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["meanlb_window     2                                       3                    \\\n", "meanlb_ewm    False               True                False             True    \n", "                  a         b         a         b         a         b       a   \n", "2020-01-01     0.50 -0.166667  0.802469 -0.267490  0.666667 -0.222222  0.8125   \n", "2020-01-02    -0.25  0.250000 -0.296296  0.296296  0.000000  0.000000 -0.1875   \n", "2020-01-03     1.50 -0.500000  1.222222 -0.407407  1.333333 -0.444444  1.2500   \n", "2020-01-04     0.25 -0.250000  0.333333 -0.333333       NaN       NaN     NaN   \n", "2020-01-05      NaN       NaN       NaN       NaN       NaN       NaN     NaN   \n", "2020-01-06      NaN       NaN       NaN       NaN       NaN       NaN     NaN   \n", "\n", "meanlb_window            \n", "meanlb_ewm               \n", "                      b  \n", "2020-01-01    -0.270833  \n", "2020-01-02     0.187500  \n", "2020-01-03    -0.416667  \n", "2020-01-04          NaN  \n", "2020-01-05          NaN  \n", "2020-01-06          NaN  \n", "12.8 ms ± 2.4 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "77.2 ms ± 1.27 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "(1000, 8000)\n"]}], "source": ["print(vbt.MEANLB.run(close, window=(2, 3), ewm=(False, True), param_product=True).labels)\n", "\n", "%timeit vbt.MEANLB.run(big_close, window=2)\n", "%timeit vbt.MEANLB.run(big_close, window=np.arange(2, 10).tolist())\n", "\n", "print(vbt.MEANLB.run(big_close, window=np.arange(2, 10).tolist()).wrapper.shape)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"800\" height=\"350\" style=\"\" viewBox=\"0 0 800 350\"><rect x=\"0\" y=\"0\" width=\"800\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-5e38f2\"><g class=\"clips\"><clipPath id=\"clip5e38f2xyplot\" class=\"plotclip\"><rect width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath id=\"clip5e38f2xy2plot\" class=\"plotclip\"><rect width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip5e38f2x\"><rect x=\"48\" y=\"0\" width=\"617.5799999999999\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip5e38f2y\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip5e38f2xy\"><rect x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip5e38f2y2\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip5e38f2xy2\"><rect x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\"/></clipPath></g><g class=\"gradients\"><linearGradient x1=\"0\" x2=\"0\" y1=\"1\" y2=\"0\" id=\"g5e38f2-cb0b55b784-37cb-4fa6-abd9-2a31b4563656\"><stop offset=\"0%\" stop-color=\"rgb(13, 8, 135)\" stop-opacity=\"1\"/><stop offset=\"11.11111111111111%\" stop-color=\"rgb(70, 3, 159)\" stop-opacity=\"1\"/><stop offset=\"22.22222222222222%\" stop-color=\"rgb(114, 1, 168)\" stop-opacity=\"1\"/><stop offset=\"33.33333333333333%\" stop-color=\"rgb(156, 23, 158)\" stop-opacity=\"1\"/><stop offset=\"44.44444444444444%\" stop-color=\"rgb(189, 55, 134)\" stop-opacity=\"1\"/><stop offset=\"55.55555555555556%\" stop-color=\"rgb(216, 87, 107)\" stop-opacity=\"1\"/><stop offset=\"66.66666666666666%\" stop-color=\"rgb(237, 121, 83)\" stop-opacity=\"1\"/><stop offset=\"77.77777777777779%\" stop-color=\"rgb(251, 159, 58)\" stop-opacity=\"1\"/><stop offset=\"88.88888888888889%\" stop-color=\"rgb(253, 202, 38)\" stop-opacity=\"1\"/><stop offset=\"100%\" stop-color=\"rgb(240, 249, 33)\" stop-opacity=\"1\"/></linearGradient></g></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(99.46000000000001,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(202.39,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(305.32,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(408.25,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(511.18,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(614.11,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,290.2)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,233.35)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,119.65)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,62.8)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"><path class=\"xy2-x\"/><path class=\"xy2-y\"/></g><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"><g class=\"xy2-x\"/><g class=\"xy2-y\"/></g><g class=\"plot\" transform=\"translate(48, 46)\" clip-path=\"url('#clip5e38f2xyplot')\"><g class=\"heatmaplayer mlayer\"><g class=\"hm\"><image xmlns=\"http://www.w3.org/2000/svg\" preserveAspectRatio=\"none\" height=\"261\" width=\"618\" x=\"0\" y=\"0\" xlink:href=\"data:image/png;base64,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\" style=\"opacity: 1;\"/></g></g></g><g class=\"overplot\"><g class=\"xy2\" transform=\"translate(48, 46)\" clip-path=\"url('#clip5e38f2xy2plot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace679cece0-e88a-4201-86ab-50e45f2f5a41\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M51.46,244.2L154.39,130.5L257.32,244.2L463.18,16.8L566.11,130.5\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(51.46,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(154.39,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(257.32,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(360.25,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(463.18,16.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(566.11,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"text\"/></g></g></g></g><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"><path class=\"xy2-x crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"xy2-y crisp\" d=\"M0,0\" style=\"fill: none;\"/></g><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(99.46000000000001,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2020</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(202.39,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(305.32,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(408.25,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(511.18,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(614.11,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 6</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"47\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">labels</text></g></g><g class=\"overaxes-above\"><g class=\"xy2-x\"/><g class=\"xy2-y\"><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,290.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,233.35)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,119.65)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,62.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g></g></g></g><g class=\"subplot xy2\"/></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-5e38f2\"><g class=\"clips\"/><clipPath id=\"legend5e38f2\"><rect width=\"76\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(629, 11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"76\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"translate(0, 0)\" clip-path=\"url('#legend5e38f2')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0, 14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"72.734375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"cb0b55b784-37cb-4fa6-abd9-2a31b4563656 colorbar\" transform=\"translate(48,46)\"><rect class=\"cbbg\" x=\"670\" y=\"0\" width=\"69.640625\" height=\"261\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0; stroke: rgb(68, 68, 68); stroke-opacity: 1; stroke-width: 0;\"/><g class=\"cbfills\" transform=\"translate(0,10)\"><rect class=\"cbfill\" x=\"680\" width=\"30\" y=\"0\" height=\"241\" style=\"fill: url('#g5e38f2-cb0b55b784-37cb-4fa6-abd9-2a31b4563656');\"/></g><g class=\"cblines\" transform=\"translate(0,10)\"/><g class=\"cbaxis crisp\" transform=\"translate(0,-46)\"><g class=\"ycb0b55b784-37cb-4fa6-abd9-2a31b4563656tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,262.57)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ycb0b55b784-37cb-4fa6-abd9-2a31b4563656tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,193.71)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g><g class=\"ycb0b55b784-37cb-4fa6-abd9-2a31b4563656tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,124.86)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"ycb0b55b784-37cb-4fa6-abd9-2a31b4563656tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,56)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g></g><g class=\"cbtitleunshift\" transform=\"translate(-48,-46)\"><g class=\"cbtitle\"/></g><rect class=\"cboutline\" x=\"680\" y=\"10\" width=\"30\" height=\"241\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: none; stroke-width: 0;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vbt.MEANLB.run(close['a'], window=2).plot().show_svg()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["lexlb_pos_th array_0    array_1    array_2   \n", "lexlb_neg_th array_0    array_1    array_2   \n", "                   a  b       a  b       a  b\n", "2020-01-01        -1  1      -1  1       0  0\n", "2020-01-02         1 -1       0  0       0  0\n", "2020-01-03        -1  1       0  0       0  0\n", "2020-01-04         0  0       0  0       0  0\n", "2020-01-05         1 -1       1 -1       0  0\n", "2020-01-06         0  1       0  1       0  0\n", "15.6 ms ± 230 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "(1000, 1000)\n"]}], "source": ["print(vbt.LEXLB.run(close, pos_th=pos_ths, neg_th=neg_ths).labels)\n", "\n", "%timeit vbt.LEXLB.run(big_close, pos_th=1, neg_th=0.5)\n", "\n", "print(vbt.LEXLB.run(big_close, pos_th=1, neg_th=0.5).wrapper.shape)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"800\" height=\"350\" style=\"\" viewBox=\"0 0 800 350\"><rect x=\"0\" y=\"0\" width=\"800\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-a12fb9\"><g class=\"clips\"><clipPath id=\"clipa12fb9xyplot\" class=\"plotclip\"><rect width=\"609.12\" height=\"261\"/></clipPath><clipPath id=\"clipa12fb9xy2plot\" class=\"plotclip\"><rect width=\"609.12\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipa12fb9x\"><rect x=\"48\" y=\"0\" width=\"609.12\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clipa12fb9y\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipa12fb9xy\"><rect x=\"48\" y=\"46\" width=\"609.12\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipa12fb9y2\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipa12fb9xy2\"><rect x=\"48\" y=\"46\" width=\"609.12\" height=\"261\"/></clipPath></g><g class=\"gradients\"><linearGradient x1=\"0\" x2=\"0\" y1=\"1\" y2=\"0\" id=\"ga12fb9-cb62a84054-a133-472d-9565-8b72e3f8add3\"><stop offset=\"0%\" stop-color=\"rgb(13, 8, 135)\" stop-opacity=\"1\"/><stop offset=\"11.11111111111111%\" stop-color=\"rgb(70, 3, 159)\" stop-opacity=\"1\"/><stop offset=\"22.22222222222222%\" stop-color=\"rgb(114, 1, 168)\" stop-opacity=\"1\"/><stop offset=\"33.33333333333333%\" stop-color=\"rgb(156, 23, 158)\" stop-opacity=\"1\"/><stop offset=\"44.44444444444444%\" stop-color=\"rgb(189, 55, 134)\" stop-opacity=\"1\"/><stop offset=\"55.55555555555556%\" stop-color=\"rgb(216, 87, 107)\" stop-opacity=\"1\"/><stop offset=\"66.66666666666666%\" stop-color=\"rgb(237, 121, 83)\" stop-opacity=\"1\"/><stop offset=\"77.77777777777779%\" stop-color=\"rgb(251, 159, 58)\" stop-opacity=\"1\"/><stop offset=\"88.88888888888889%\" stop-color=\"rgb(253, 202, 38)\" stop-opacity=\"1\"/><stop offset=\"100%\" stop-color=\"rgb(240, 249, 33)\" stop-opacity=\"1\"/></linearGradient></g></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"48\" y=\"46\" width=\"609.12\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(98.75999999999999,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(200.28,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(301.8,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(403.32,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(504.84,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(606.36,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,290.2)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,233.35)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,119.65)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,62.8)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"><path class=\"xy2-x\"/><path class=\"xy2-y\"/></g><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"><g class=\"xy2-x\"/><g class=\"xy2-y\"/></g><g class=\"plot\" transform=\"translate(48, 46)\" clip-path=\"url('#clipa12fb9xyplot')\"><g class=\"heatmaplayer mlayer\"><g class=\"hm\"><image xmlns=\"http://www.w3.org/2000/svg\" preserveAspectRatio=\"none\" height=\"261\" width=\"609\" x=\"0\" y=\"0\" xlink:href=\"data:image/png;base64,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\" style=\"opacity: 1;\"/></g></g></g><g class=\"overplot\"><g class=\"xy2\" transform=\"translate(48, 46)\" clip-path=\"url('#clipa12fb9xy2plot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace61f94ffd-e4bb-45b9-90d6-bd8f457401ae\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M50.76,244.2L152.28,130.5L253.8,244.2L456.84,16.8L558.36,130.5\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(50.76,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(152.28,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(253.8,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(355.32,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(456.84,16.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(558.36,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"text\"/></g></g></g></g><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"><path class=\"xy2-x crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"xy2-y crisp\" d=\"M0,0\" style=\"fill: none;\"/></g><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(98.75999999999999,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2020</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(200.28,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(301.8,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(403.32,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(504.84,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(606.36,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 6</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"47\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">labels</text></g></g><g class=\"overaxes-above\"><g class=\"xy2-x\"/><g class=\"xy2-y\"><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,290.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,233.35)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,119.65)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,62.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g></g></g></g><g class=\"subplot xy2\"/></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-a12fb9\"><g class=\"clips\"/><clipPath id=\"legenda12fb9\"><rect width=\"76\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(620, 11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"76\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"translate(0, 0)\" clip-path=\"url('#legenda12fb9')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0, 14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"72.734375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"cb62a84054-a133-472d-9565-8b72e3f8add3 colorbar\" transform=\"translate(48,46)\"><rect class=\"cbbg\" x=\"661\" y=\"0\" width=\"79.453125\" height=\"261\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0; stroke: rgb(68, 68, 68); stroke-opacity: 1; stroke-width: 0;\"/><g class=\"cbfills\" transform=\"translate(0,10)\"><rect class=\"cbfill\" x=\"671\" width=\"30\" y=\"0\" height=\"241\" style=\"fill: url('#ga12fb9-cb62a84054-a133-472d-9565-8b72e3f8add3');\"/></g><g class=\"cblines\" transform=\"translate(0,10)\"/><g class=\"cbaxis crisp\" transform=\"translate(0,-46)\"><g class=\"ycb62a84054-a133-472d-9565-8b72e3f8add3tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,297)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">−1</text></g><g class=\"ycb62a84054-a133-472d-9565-8b72e3f8add3tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,236.75)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">−0.5</text></g><g class=\"ycb62a84054-a133-472d-9565-8b72e3f8add3tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ycb62a84054-a133-472d-9565-8b72e3f8add3tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,116.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g><g class=\"ycb62a84054-a133-472d-9565-8b72e3f8add3tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,56)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g></g><g class=\"cbtitleunshift\" transform=\"translate(-48,-46)\"><g class=\"cbtitle\"/></g><rect class=\"cboutline\" x=\"671\" y=\"10\" width=\"30\" height=\"241\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: none; stroke-width: 0;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vbt.LEXLB.run(close['a'], pos_th=1, neg_th=0.5).plot().show_svg()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["trendlb_pos_th array_0      array_1      array_2    \n", "trendlb_neg_th array_0      array_1      array_2    \n", "trendlb_mode         0            0            0    \n", "                     a    b       a    b       a   b\n", "2020-01-01         1.0  0.0     1.0  0.0     NaN NaN\n", "2020-01-02         0.0  1.0     1.0  0.0     NaN NaN\n", "2020-01-03         1.0  0.0     1.0  0.0     NaN NaN\n", "2020-01-04         1.0  0.0     1.0  0.0     NaN NaN\n", "2020-01-05         NaN  1.0     NaN  1.0     NaN NaN\n", "2020-01-06         NaN  NaN     NaN  NaN     NaN NaN\n", "28.3 ms ± 335 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "(1000, 1000)\n"]}], "source": ["print(vbt.TRENDLB.run(close, pos_th=pos_ths, neg_th=neg_ths, mode='Binary').labels)\n", "\n", "%timeit vbt.TRENDLB.run(big_close, pos_th=1, neg_th=0.5, mode='Binary')\n", "\n", "print(vbt.TRENDLB.run(big_close, pos_th=1, neg_th=0.5, mode='Binary').wrapper.shape)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"800\" height=\"350\" style=\"\" viewBox=\"0 0 800 350\"><rect x=\"0\" y=\"0\" width=\"800\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-4a1048\"><g class=\"clips\"><clipPath id=\"clip4a1048xyplot\" class=\"plotclip\"><rect width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath id=\"clip4a1048xy2plot\" class=\"plotclip\"><rect width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip4a1048x\"><rect x=\"48\" y=\"0\" width=\"617.5799999999999\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip4a1048y\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip4a1048xy\"><rect x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip4a1048y2\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip4a1048xy2\"><rect x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\"/></clipPath></g><g class=\"gradients\"><linearGradient x1=\"0\" x2=\"0\" y1=\"1\" y2=\"0\" id=\"g4a1048-cbc9c8447e-db1a-4f15-b918-9da81beaed3e\"><stop offset=\"0%\" stop-color=\"rgb(13, 8, 135)\" stop-opacity=\"1\"/><stop offset=\"11.11111111111111%\" stop-color=\"rgb(70, 3, 159)\" stop-opacity=\"1\"/><stop offset=\"22.22222222222222%\" stop-color=\"rgb(114, 1, 168)\" stop-opacity=\"1\"/><stop offset=\"33.33333333333333%\" stop-color=\"rgb(156, 23, 158)\" stop-opacity=\"1\"/><stop offset=\"44.44444444444444%\" stop-color=\"rgb(189, 55, 134)\" stop-opacity=\"1\"/><stop offset=\"55.55555555555556%\" stop-color=\"rgb(216, 87, 107)\" stop-opacity=\"1\"/><stop offset=\"66.66666666666666%\" stop-color=\"rgb(237, 121, 83)\" stop-opacity=\"1\"/><stop offset=\"77.77777777777779%\" stop-color=\"rgb(251, 159, 58)\" stop-opacity=\"1\"/><stop offset=\"88.88888888888889%\" stop-color=\"rgb(253, 202, 38)\" stop-opacity=\"1\"/><stop offset=\"100%\" stop-color=\"rgb(240, 249, 33)\" stop-opacity=\"1\"/></linearGradient></g></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(99.46000000000001,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(202.39,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(305.32,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(408.25,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(511.18,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(614.11,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,290.2)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,233.35)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,119.65)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,62.8)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"><path class=\"xy2-x\"/><path class=\"xy2-y\"/></g><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"><g class=\"xy2-x\"/><g class=\"xy2-y\"/></g><g class=\"plot\" transform=\"translate(48, 46)\" clip-path=\"url('#clip4a1048xyplot')\"><g class=\"heatmaplayer mlayer\"><g class=\"hm\"><image xmlns=\"http://www.w3.org/2000/svg\" preserveAspectRatio=\"none\" height=\"261\" width=\"618\" x=\"0\" y=\"0\" xlink:href=\"data:image/png;base64,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\" style=\"opacity: 1;\"/></g></g></g><g class=\"overplot\"><g class=\"xy2\" transform=\"translate(48, 46)\" clip-path=\"url('#clip4a1048xy2plot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace011af3ad-9c13-4525-832d-a11e223a4377\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M51.46,244.2L154.39,130.5L257.32,244.2L463.18,16.8L566.11,130.5\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(51.46,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(154.39,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(257.32,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(360.25,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(463.18,16.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(566.11,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"text\"/></g></g></g></g><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"><path class=\"xy2-x crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"xy2-y crisp\" d=\"M0,0\" style=\"fill: none;\"/></g><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(99.46000000000001,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2020</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(202.39,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(305.32,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(408.25,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(511.18,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(614.11,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 6</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"47\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">labels</text></g></g><g class=\"overaxes-above\"><g class=\"xy2-x\"/><g class=\"xy2-y\"><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,290.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,233.35)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,119.65)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,62.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g></g></g></g><g class=\"subplot xy2\"/></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-4a1048\"><g class=\"clips\"/><clipPath id=\"legend4a1048\"><rect width=\"76\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(629, 11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"76\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"translate(0, 0)\" clip-path=\"url('#legend4a1048')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0, 14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"72.734375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"cbc9c8447e-db1a-4f15-b918-9da81beaed3e colorbar\" transform=\"translate(48,46)\"><rect class=\"cbbg\" x=\"670\" y=\"0\" width=\"69.640625\" height=\"261\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0; stroke: rgb(68, 68, 68); stroke-opacity: 1; stroke-width: 0;\"/><g class=\"cbfills\" transform=\"translate(0,10)\"><rect class=\"cbfill\" x=\"680\" width=\"30\" y=\"0\" height=\"241\" style=\"fill: url('#g4a1048-cbc9c8447e-db1a-4f15-b918-9da81beaed3e');\"/></g><g class=\"cblines\" transform=\"translate(0,10)\"/><g class=\"cbaxis crisp\" transform=\"translate(0,-46)\"><g class=\"ycbc9c8447e-db1a-4f15-b918-9da81beaed3etick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,297)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ycbc9c8447e-db1a-4f15-b918-9da81beaed3etick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,248.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.2</text></g><g class=\"ycbc9c8447e-db1a-4f15-b918-9da81beaed3etick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,200.6)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.4</text></g><g class=\"ycbc9c8447e-db1a-4f15-b918-9da81beaed3etick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,152.4)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.6</text></g><g class=\"ycbc9c8447e-db1a-4f15-b918-9da81beaed3etick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,104.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.8</text></g><g class=\"ycbc9c8447e-db1a-4f15-b918-9da81beaed3etick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,56)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g></g><g class=\"cbtitleunshift\" transform=\"translate(-48,-46)\"><g class=\"cbtitle\"/></g><rect class=\"cboutline\" x=\"680\" y=\"10\" width=\"30\" height=\"241\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: none; stroke-width: 0;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vbt.TRENDLB.run(close['a'], pos_th=1, neg_th=0.5, mode='Binary').plot().show_svg()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["trendlb_pos_th array_0      array_1      array_2    \n", "trendlb_neg_th array_0      array_1      array_2    \n", "trendlb_mode         1            1            1    \n", "                     a    b       a    b       a   b\n", "2020-01-01         1.0  0.0     1.0  0.0     NaN NaN\n", "2020-01-02         0.0  1.0     0.5  0.5     NaN NaN\n", "2020-01-03         1.0  0.0     1.0  0.0     NaN NaN\n", "2020-01-04         0.5  0.5     0.5  0.5     NaN NaN\n", "2020-01-05         NaN  1.0     NaN  1.0     NaN NaN\n", "2020-01-06         NaN  NaN     NaN  NaN     NaN NaN\n", "106 ms ± 718 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "(1000, 1000)\n"]}], "source": ["print(vbt.TRENDLB.run(close, pos_th=pos_ths, neg_th=neg_ths, mode='BinaryCont').labels)\n", "\n", "%timeit vbt.TRENDLB.run(big_close, pos_th=1, neg_th=0.5, mode='BinaryCont')\n", "\n", "print(vbt.TRENDLB.run(big_close, pos_th=1, neg_th=0.5, mode='BinaryCont').wrapper.shape)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"800\" height=\"350\" style=\"\" viewBox=\"0 0 800 350\"><rect x=\"0\" y=\"0\" width=\"800\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-ba72e5\"><g class=\"clips\"><clipPath id=\"clipba72e5xyplot\" class=\"plotclip\"><rect width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath id=\"clipba72e5xy2plot\" class=\"plotclip\"><rect width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipba72e5x\"><rect x=\"48\" y=\"0\" width=\"617.5799999999999\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clipba72e5y\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipba72e5xy\"><rect x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipba72e5y2\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipba72e5xy2\"><rect x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\"/></clipPath></g><g class=\"gradients\"><linearGradient x1=\"0\" x2=\"0\" y1=\"1\" y2=\"0\" id=\"gba72e5-cba0b4d3d2-7d0b-483e-9f9c-c34e047dca01\"><stop offset=\"0%\" stop-color=\"rgb(13, 8, 135)\" stop-opacity=\"1\"/><stop offset=\"11.11111111111111%\" stop-color=\"rgb(70, 3, 159)\" stop-opacity=\"1\"/><stop offset=\"22.22222222222222%\" stop-color=\"rgb(114, 1, 168)\" stop-opacity=\"1\"/><stop offset=\"33.33333333333333%\" stop-color=\"rgb(156, 23, 158)\" stop-opacity=\"1\"/><stop offset=\"44.44444444444444%\" stop-color=\"rgb(189, 55, 134)\" stop-opacity=\"1\"/><stop offset=\"55.55555555555556%\" stop-color=\"rgb(216, 87, 107)\" stop-opacity=\"1\"/><stop offset=\"66.66666666666666%\" stop-color=\"rgb(237, 121, 83)\" stop-opacity=\"1\"/><stop offset=\"77.77777777777779%\" stop-color=\"rgb(251, 159, 58)\" stop-opacity=\"1\"/><stop offset=\"88.88888888888889%\" stop-color=\"rgb(253, 202, 38)\" stop-opacity=\"1\"/><stop offset=\"100%\" stop-color=\"rgb(240, 249, 33)\" stop-opacity=\"1\"/></linearGradient></g></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(99.46000000000001,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(202.39,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(305.32,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(408.25,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(511.18,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(614.11,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,290.2)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,233.35)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,119.65)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,62.8)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"><path class=\"xy2-x\"/><path class=\"xy2-y\"/></g><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"><g class=\"xy2-x\"/><g class=\"xy2-y\"/></g><g class=\"plot\" transform=\"translate(48, 46)\" clip-path=\"url('#clipba72e5xyplot')\"><g class=\"heatmaplayer mlayer\"><g class=\"hm\"><image xmlns=\"http://www.w3.org/2000/svg\" preserveAspectRatio=\"none\" height=\"261\" width=\"618\" x=\"0\" y=\"0\" xlink:href=\"data:image/png;base64,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\" style=\"opacity: 1;\"/></g></g></g><g class=\"overplot\"><g class=\"xy2\" transform=\"translate(48, 46)\" clip-path=\"url('#clipba72e5xy2plot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace538c522e-0361-4279-b407-a2cb5b0ee8ee\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M51.46,244.2L154.39,130.5L257.32,244.2L463.18,16.8L566.11,130.5\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(51.46,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(154.39,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(257.32,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(360.25,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(463.18,16.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(566.11,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"text\"/></g></g></g></g><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"><path class=\"xy2-x crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"xy2-y crisp\" d=\"M0,0\" style=\"fill: none;\"/></g><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(99.46000000000001,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2020</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(202.39,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(305.32,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(408.25,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(511.18,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(614.11,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 6</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"47\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">labels</text></g></g><g class=\"overaxes-above\"><g class=\"xy2-x\"/><g class=\"xy2-y\"><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,290.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,233.35)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,119.65)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,62.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g></g></g></g><g class=\"subplot xy2\"/></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-ba72e5\"><g class=\"clips\"/><clipPath id=\"legendba72e5\"><rect width=\"76\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(629, 11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"76\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"translate(0, 0)\" clip-path=\"url('#legendba72e5')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0, 14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"72.734375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"cba0b4d3d2-7d0b-483e-9f9c-c34e047dca01 colorbar\" transform=\"translate(48,46)\"><rect class=\"cbbg\" x=\"670\" y=\"0\" width=\"69.640625\" height=\"261\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0; stroke: rgb(68, 68, 68); stroke-opacity: 1; stroke-width: 0;\"/><g class=\"cbfills\" transform=\"translate(0,10)\"><rect class=\"cbfill\" x=\"680\" width=\"30\" y=\"0\" height=\"241\" style=\"fill: url('#gba72e5-cba0b4d3d2-7d0b-483e-9f9c-c34e047dca01');\"/></g><g class=\"cblines\" transform=\"translate(0,10)\"/><g class=\"cbaxis crisp\" transform=\"translate(0,-46)\"><g class=\"ycba0b4d3d2-7d0b-483e-9f9c-c34e047dca01tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,297)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ycba0b4d3d2-7d0b-483e-9f9c-c34e047dca01tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,248.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.2</text></g><g class=\"ycba0b4d3d2-7d0b-483e-9f9c-c34e047dca01tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,200.6)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.4</text></g><g class=\"ycba0b4d3d2-7d0b-483e-9f9c-c34e047dca01tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,152.4)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.6</text></g><g class=\"ycba0b4d3d2-7d0b-483e-9f9c-c34e047dca01tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,104.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.8</text></g><g class=\"ycba0b4d3d2-7d0b-483e-9f9c-c34e047dca01tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,56)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g></g><g class=\"cbtitleunshift\" transform=\"translate(-48,-46)\"><g class=\"cbtitle\"/></g><rect class=\"cboutline\" x=\"680\" y=\"10\" width=\"30\" height=\"241\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: none; stroke-width: 0;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vbt.TRENDLB.run(close['a'], pos_th=1, neg_th=0.5, mode='BinaryCont').plot().show_svg()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["trendlb_pos_th   array_0      array_1      array_2    \n", "trendlb_neg_th   array_0      array_1      array_2    \n", "trendlb_mode           2            2            2    \n", "                       a    b       a    b       a   b\n", "2020-01-01      1.000000  0.0     1.0  0.0     NaN NaN\n", "2020-01-02      0.000000  1.0     0.5  0.5     NaN NaN\n", "2020-01-03      1.000000  0.0     1.0  0.0     NaN NaN\n", "2020-01-04      0.666667  0.0     0.5  0.5     NaN NaN\n", "2020-01-05           NaN  1.0     NaN  1.0     NaN NaN\n", "2020-01-06           NaN  NaN     NaN  NaN     NaN NaN\n", "43.5 ms ± 1.24 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "(1000, 1000)\n"]}], "source": ["print(vbt.TRENDLB.run(close, pos_th=pos_ths, neg_th=neg_ths, mode='BinaryContSat').labels)\n", "\n", "%timeit vbt.TRENDLB.run(big_close, pos_th=1, neg_th=0.5, mode='BinaryContSat')\n", "\n", "print(vbt.TRENDLB.run(big_close, pos_th=1, neg_th=0.5, mode='BinaryContSat').wrapper.shape)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"800\" height=\"350\" style=\"\" viewBox=\"0 0 800 350\"><rect x=\"0\" y=\"0\" width=\"800\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-972762\"><g class=\"clips\"><clipPath id=\"clip972762xyplot\" class=\"plotclip\"><rect width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath id=\"clip972762xy2plot\" class=\"plotclip\"><rect width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip972762x\"><rect x=\"48\" y=\"0\" width=\"617.5799999999999\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip972762y\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip972762xy\"><rect x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip972762y2\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip972762xy2\"><rect x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\"/></clipPath></g><g class=\"gradients\"><linearGradient x1=\"0\" x2=\"0\" y1=\"1\" y2=\"0\" id=\"g972762-cb9ab78014-66dc-4ae0-8c00-cd73b14ec922\"><stop offset=\"0%\" stop-color=\"rgb(13, 8, 135)\" stop-opacity=\"1\"/><stop offset=\"11.11111111111111%\" stop-color=\"rgb(70, 3, 159)\" stop-opacity=\"1\"/><stop offset=\"22.22222222222222%\" stop-color=\"rgb(114, 1, 168)\" stop-opacity=\"1\"/><stop offset=\"33.33333333333333%\" stop-color=\"rgb(156, 23, 158)\" stop-opacity=\"1\"/><stop offset=\"44.44444444444444%\" stop-color=\"rgb(189, 55, 134)\" stop-opacity=\"1\"/><stop offset=\"55.55555555555556%\" stop-color=\"rgb(216, 87, 107)\" stop-opacity=\"1\"/><stop offset=\"66.66666666666666%\" stop-color=\"rgb(237, 121, 83)\" stop-opacity=\"1\"/><stop offset=\"77.77777777777779%\" stop-color=\"rgb(251, 159, 58)\" stop-opacity=\"1\"/><stop offset=\"88.88888888888889%\" stop-color=\"rgb(253, 202, 38)\" stop-opacity=\"1\"/><stop offset=\"100%\" stop-color=\"rgb(240, 249, 33)\" stop-opacity=\"1\"/></linearGradient></g></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"48\" y=\"46\" width=\"617.5799999999999\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(99.46000000000001,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(202.39,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(305.32,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(408.25,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(511.18,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(614.11,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,290.2)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,233.35)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,119.65)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,62.8)\" d=\"M48,0h617.5799999999999\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"><path class=\"xy2-x\"/><path class=\"xy2-y\"/></g><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"><g class=\"xy2-x\"/><g class=\"xy2-y\"/></g><g class=\"plot\" transform=\"translate(48, 46)\" clip-path=\"url('#clip972762xyplot')\"><g class=\"heatmaplayer mlayer\"><g class=\"hm\"><image xmlns=\"http://www.w3.org/2000/svg\" preserveAspectRatio=\"none\" height=\"261\" width=\"618\" x=\"0\" y=\"0\" xlink:href=\"data:image/png;base64,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\" style=\"opacity: 1;\"/></g></g></g><g class=\"overplot\"><g class=\"xy2\" transform=\"translate(48, 46)\" clip-path=\"url('#clip972762xy2plot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace300af6d1-7934-45fb-9f51-967b46b0cf93\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M51.46,244.2L154.39,130.5L257.32,244.2L463.18,16.8L566.11,130.5\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(51.46,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(154.39,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(257.32,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(360.25,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(463.18,16.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(566.11,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"text\"/></g></g></g></g><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"><path class=\"xy2-x crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"xy2-y crisp\" d=\"M0,0\" style=\"fill: none;\"/></g><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(99.46000000000001,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2020</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(202.39,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(305.32,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(408.25,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(511.18,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(614.11,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 6</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"47\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">labels</text></g></g><g class=\"overaxes-above\"><g class=\"xy2-x\"/><g class=\"xy2-y\"><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,290.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,233.35)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,119.65)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"666.5799999999999\" y=\"4.199999999999999\" transform=\"translate(0,62.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g></g></g></g><g class=\"subplot xy2\"/></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-972762\"><g class=\"clips\"/><clipPath id=\"legend972762\"><rect width=\"76\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(629, 11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"76\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"translate(0, 0)\" clip-path=\"url('#legend972762')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0, 14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"72.734375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"cb9ab78014-66dc-4ae0-8c00-cd73b14ec922 colorbar\" transform=\"translate(48,46)\"><rect class=\"cbbg\" x=\"670\" y=\"0\" width=\"69.640625\" height=\"261\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0; stroke: rgb(68, 68, 68); stroke-opacity: 1; stroke-width: 0;\"/><g class=\"cbfills\" transform=\"translate(0,10)\"><rect class=\"cbfill\" x=\"680\" width=\"30\" y=\"0\" height=\"241\" style=\"fill: url('#g972762-cb9ab78014-66dc-4ae0-8c00-cd73b14ec922');\"/></g><g class=\"cblines\" transform=\"translate(0,10)\"/><g class=\"cbaxis crisp\" transform=\"translate(0,-46)\"><g class=\"ycb9ab78014-66dc-4ae0-8c00-cd73b14ec922tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,297)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ycb9ab78014-66dc-4ae0-8c00-cd73b14ec922tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,248.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.2</text></g><g class=\"ycb9ab78014-66dc-4ae0-8c00-cd73b14ec922tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,200.6)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.4</text></g><g class=\"ycb9ab78014-66dc-4ae0-8c00-cd73b14ec922tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,152.4)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.6</text></g><g class=\"ycb9ab78014-66dc-4ae0-8c00-cd73b14ec922tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,104.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.8</text></g><g class=\"ycb9ab78014-66dc-4ae0-8c00-cd73b14ec922tick\"><text text-anchor=\"start\" x=\"712.9\" y=\"4.199999999999999\" transform=\"translate(0,56)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g></g><g class=\"cbtitleunshift\" transform=\"translate(-48,-46)\"><g class=\"cbtitle\"/></g><rect class=\"cboutline\" x=\"680\" y=\"10\" width=\"30\" height=\"241\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: none; stroke-width: 0;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vbt.TRENDLB.run(close['a'], pos_th=1, neg_th=0.5, mode='BinaryContSat').plot().show_svg()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["trendlb_pos_th array_0           array_1           array_2    \n", "trendlb_neg_th array_0           array_1           array_2    \n", "trendlb_mode         3                 3                 3    \n", "                     a         b       a         b       a   b\n", "2020-01-01         1.0 -0.333333     2.0 -0.666667     NaN NaN\n", "2020-01-02        -0.5  0.500000     0.5 -0.500000     NaN NaN\n", "2020-01-03         2.0 -0.666667     2.0 -0.666667     NaN NaN\n", "2020-01-04         0.5 -0.500000     0.5 -0.500000     NaN NaN\n", "2020-01-05         NaN  1.000000     NaN  1.000000     NaN NaN\n", "2020-01-06         NaN       NaN     NaN       NaN     NaN NaN\n", "27.9 ms ± 112 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "(1000, 1000)\n"]}], "source": ["print(vbt.TRENDLB.run(close, pos_th=pos_ths, neg_th=neg_ths, mode='PctChange').labels)\n", "\n", "%timeit vbt.TRENDLB.run(big_close, pos_th=1, neg_th=0.5, mode='PctChange')\n", "\n", "print(vbt.TRENDLB.run(big_close, pos_th=1, neg_th=0.5, mode='PctChange').wrapper.shape)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"800\" height=\"350\" style=\"\" viewBox=\"0 0 800 350\"><rect x=\"0\" y=\"0\" width=\"800\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-16f217\"><g class=\"clips\"><clipPath id=\"clip16f217xyplot\" class=\"plotclip\"><rect width=\"609.12\" height=\"261\"/></clipPath><clipPath id=\"clip16f217xy2plot\" class=\"plotclip\"><rect width=\"609.12\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip16f217x\"><rect x=\"48\" y=\"0\" width=\"609.12\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip16f217y\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip16f217xy\"><rect x=\"48\" y=\"46\" width=\"609.12\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip16f217y2\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip16f217xy2\"><rect x=\"48\" y=\"46\" width=\"609.12\" height=\"261\"/></clipPath></g><g class=\"gradients\"><linearGradient x1=\"0\" x2=\"0\" y1=\"1\" y2=\"0\" id=\"g16f217-cb841f581b-870a-41fc-baa6-372297c0abf4\"><stop offset=\"0%\" stop-color=\"rgb(13, 8, 135)\" stop-opacity=\"1\"/><stop offset=\"11.11111111111111%\" stop-color=\"rgb(70, 3, 159)\" stop-opacity=\"1\"/><stop offset=\"22.22222222222222%\" stop-color=\"rgb(114, 1, 168)\" stop-opacity=\"1\"/><stop offset=\"33.33333333333333%\" stop-color=\"rgb(156, 23, 158)\" stop-opacity=\"1\"/><stop offset=\"44.44444444444444%\" stop-color=\"rgb(189, 55, 134)\" stop-opacity=\"1\"/><stop offset=\"55.55555555555556%\" stop-color=\"rgb(216, 87, 107)\" stop-opacity=\"1\"/><stop offset=\"66.66666666666666%\" stop-color=\"rgb(237, 121, 83)\" stop-opacity=\"1\"/><stop offset=\"77.77777777777779%\" stop-color=\"rgb(251, 159, 58)\" stop-opacity=\"1\"/><stop offset=\"88.88888888888889%\" stop-color=\"rgb(253, 202, 38)\" stop-opacity=\"1\"/><stop offset=\"100%\" stop-color=\"rgb(240, 249, 33)\" stop-opacity=\"1\"/></linearGradient></g></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"48\" y=\"46\" width=\"609.12\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(98.75999999999999,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(200.28,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(301.8,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(403.32,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(504.84,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(606.36,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,290.2)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,233.35)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,119.65)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,62.8)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"><path class=\"xy2-x\"/><path class=\"xy2-y\"/></g><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"><g class=\"xy2-x\"/><g class=\"xy2-y\"/></g><g class=\"plot\" transform=\"translate(48, 46)\" clip-path=\"url('#clip16f217xyplot')\"><g class=\"heatmaplayer mlayer\"><g class=\"hm\"><image xmlns=\"http://www.w3.org/2000/svg\" preserveAspectRatio=\"none\" height=\"261\" width=\"609\" x=\"0\" y=\"0\" xlink:href=\"data:image/png;base64,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\" style=\"opacity: 1;\"/></g></g></g><g class=\"overplot\"><g class=\"xy2\" transform=\"translate(48, 46)\" clip-path=\"url('#clip16f217xy2plot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter traced9acda75-b4b1-4bb9-936d-8f8c35ad7dfc\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M50.76,244.2L152.28,130.5L253.8,244.2L456.84,16.8L558.36,130.5\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(50.76,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(152.28,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(253.8,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(355.32,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(456.84,16.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(558.36,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"text\"/></g></g></g></g><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"><path class=\"xy2-x crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"xy2-y crisp\" d=\"M0,0\" style=\"fill: none;\"/></g><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(98.75999999999999,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2020</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(200.28,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(301.8,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(403.32,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(504.84,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(606.36,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 6</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"47\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">labels</text></g></g><g class=\"overaxes-above\"><g class=\"xy2-x\"/><g class=\"xy2-y\"><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,290.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,233.35)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,119.65)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,62.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g></g></g></g><g class=\"subplot xy2\"/></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-16f217\"><g class=\"clips\"/><clipPath id=\"legend16f217\"><rect width=\"76\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(620, 11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"76\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"translate(0, 0)\" clip-path=\"url('#legend16f217')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0, 14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"72.734375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"cb841f581b-870a-41fc-baa6-372297c0abf4 colorbar\" transform=\"translate(48,46)\"><rect class=\"cbbg\" x=\"661\" y=\"0\" width=\"79.453125\" height=\"261\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0; stroke: rgb(68, 68, 68); stroke-opacity: 1; stroke-width: 0;\"/><g class=\"cbfills\" transform=\"translate(0,10)\"><rect class=\"cbfill\" x=\"671\" width=\"30\" y=\"0\" height=\"241\" style=\"fill: url('#g16f217-cb841f581b-870a-41fc-baa6-372297c0abf4');\"/></g><g class=\"cblines\" transform=\"translate(0,10)\"/><g class=\"cbaxis crisp\" transform=\"translate(0,-46)\"><g class=\"ycb841f581b-870a-41fc-baa6-372297c0abf4tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,297)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">−0.5</text></g><g class=\"ycb841f581b-870a-41fc-baa6-372297c0abf4tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,248.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ycb841f581b-870a-41fc-baa6-372297c0abf4tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,200.6)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g><g class=\"ycb841f581b-870a-41fc-baa6-372297c0abf4tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,152.4)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"ycb841f581b-870a-41fc-baa6-372297c0abf4tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,104.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"ycb841f581b-870a-41fc-baa6-372297c0abf4tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,56)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g></g><g class=\"cbtitleunshift\" transform=\"translate(-48,-46)\"><g class=\"cbtitle\"/></g><rect class=\"cboutline\" x=\"671\" y=\"10\" width=\"30\" height=\"241\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: none; stroke-width: 0;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vbt.TRENDLB.run(close['a'], pos_th=1, neg_th=0.5, mode='PctChange').plot().show_svg()"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["trendlb_pos_th   array_0             array_1           array_2    \n", "trendlb_neg_th   array_0             array_1           array_2    \n", "trendlb_mode           4                   4                 4    \n", "                       a         b         a         b       a   b\n", "2020-01-01      0.500000 -0.333333  0.666667 -0.666667     NaN NaN\n", "2020-01-02     -0.500000  0.333333  0.333333 -0.500000     NaN NaN\n", "2020-01-03      0.666667 -0.666667  0.666667 -0.666667     NaN NaN\n", "2020-01-04      0.333333 -0.500000  0.333333 -0.500000     NaN NaN\n", "2020-01-05           NaN  0.500000       NaN  0.500000     NaN NaN\n", "2020-01-06           NaN       NaN       NaN       NaN     NaN NaN\n", "28.2 ms ± 44.8 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "(1000, 1000)\n"]}], "source": ["print(vbt.TRENDLB.run(close, pos_th=pos_ths, neg_th=neg_ths, mode='PctChangeNorm').labels)\n", "\n", "%timeit vbt.TRENDLB.run(big_close, pos_th=1, neg_th=0.5, mode='PctChangeNorm')\n", "\n", "print(vbt.TRENDLB.run(big_close, pos_th=1, neg_th=0.5, mode='PctChangeNorm').wrapper.shape)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"800\" height=\"350\" style=\"\" viewBox=\"0 0 800 350\"><rect x=\"0\" y=\"0\" width=\"800\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-0a83f1\"><g class=\"clips\"><clipPath id=\"clip0a83f1xyplot\" class=\"plotclip\"><rect width=\"609.12\" height=\"261\"/></clipPath><clipPath id=\"clip0a83f1xy2plot\" class=\"plotclip\"><rect width=\"609.12\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip0a83f1x\"><rect x=\"48\" y=\"0\" width=\"609.12\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip0a83f1y\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip0a83f1xy\"><rect x=\"48\" y=\"46\" width=\"609.12\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip0a83f1y2\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip0a83f1xy2\"><rect x=\"48\" y=\"46\" width=\"609.12\" height=\"261\"/></clipPath></g><g class=\"gradients\"><linearGradient x1=\"0\" x2=\"0\" y1=\"1\" y2=\"0\" id=\"g0a83f1-cba9e1da00-c046-43f5-8561-3fb62abadb86\"><stop offset=\"0%\" stop-color=\"rgb(13, 8, 135)\" stop-opacity=\"1\"/><stop offset=\"11.11111111111111%\" stop-color=\"rgb(70, 3, 159)\" stop-opacity=\"1\"/><stop offset=\"22.22222222222222%\" stop-color=\"rgb(114, 1, 168)\" stop-opacity=\"1\"/><stop offset=\"33.33333333333333%\" stop-color=\"rgb(156, 23, 158)\" stop-opacity=\"1\"/><stop offset=\"44.44444444444444%\" stop-color=\"rgb(189, 55, 134)\" stop-opacity=\"1\"/><stop offset=\"55.55555555555556%\" stop-color=\"rgb(216, 87, 107)\" stop-opacity=\"1\"/><stop offset=\"66.66666666666666%\" stop-color=\"rgb(237, 121, 83)\" stop-opacity=\"1\"/><stop offset=\"77.77777777777779%\" stop-color=\"rgb(251, 159, 58)\" stop-opacity=\"1\"/><stop offset=\"88.88888888888889%\" stop-color=\"rgb(253, 202, 38)\" stop-opacity=\"1\"/><stop offset=\"100%\" stop-color=\"rgb(240, 249, 33)\" stop-opacity=\"1\"/></linearGradient></g></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"48\" y=\"46\" width=\"609.12\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(98.75999999999999,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(200.28,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(301.8,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(403.32,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(504.84,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(606.36,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,290.2)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,233.35)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,119.65)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,62.8)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"><path class=\"xy2-x\"/><path class=\"xy2-y\"/></g><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"><g class=\"xy2-x\"/><g class=\"xy2-y\"/></g><g class=\"plot\" transform=\"translate(48, 46)\" clip-path=\"url('#clip0a83f1xyplot')\"><g class=\"heatmaplayer mlayer\"><g class=\"hm\"><image xmlns=\"http://www.w3.org/2000/svg\" preserveAspectRatio=\"none\" height=\"261\" width=\"609\" x=\"0\" y=\"0\" xlink:href=\"data:image/png;base64,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\" style=\"opacity: 1;\"/></g></g></g><g class=\"overplot\"><g class=\"xy2\" transform=\"translate(48, 46)\" clip-path=\"url('#clip0a83f1xy2plot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace09d556a7-6db6-4b33-a6c4-a449120c001b\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M50.76,244.2L152.28,130.5L253.8,244.2L456.84,16.8L558.36,130.5\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(50.76,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(152.28,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(253.8,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(355.32,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(456.84,16.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(558.36,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"text\"/></g></g></g></g><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"><path class=\"xy2-x crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"xy2-y crisp\" d=\"M0,0\" style=\"fill: none;\"/></g><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(98.75999999999999,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2020</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(200.28,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(301.8,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(403.32,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(504.84,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(606.36,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 6</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"47\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">labels</text></g></g><g class=\"overaxes-above\"><g class=\"xy2-x\"/><g class=\"xy2-y\"><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,290.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,233.35)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,119.65)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,62.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g></g></g></g><g class=\"subplot xy2\"/></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-0a83f1\"><g class=\"clips\"/><clipPath id=\"legend0a83f1\"><rect width=\"76\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(620, 11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"76\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"translate(0, 0)\" clip-path=\"url('#legend0a83f1')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0, 14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"72.734375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"cba9e1da00-c046-43f5-8561-3fb62abadb86 colorbar\" transform=\"translate(48,46)\"><rect class=\"cbbg\" x=\"661\" y=\"0\" width=\"79.453125\" height=\"261\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0; stroke: rgb(68, 68, 68); stroke-opacity: 1; stroke-width: 0;\"/><g class=\"cbfills\" transform=\"translate(0,10)\"><rect class=\"cbfill\" x=\"671\" width=\"30\" y=\"0\" height=\"241\" style=\"fill: url('#g0a83f1-cba9e1da00-c046-43f5-8561-3fb62abadb86');\"/></g><g class=\"cblines\" transform=\"translate(0,10)\"/><g class=\"cbaxis crisp\" transform=\"translate(0,-46)\"><g class=\"ycba9e1da00-c046-43f5-8561-3fb62abadb86tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,276.34000000000003)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">−0.4</text></g><g class=\"ycba9e1da00-c046-43f5-8561-3fb62abadb86tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,235.03)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">−0.2</text></g><g class=\"ycba9e1da00-c046-43f5-8561-3fb62abadb86tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,193.71)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ycba9e1da00-c046-43f5-8561-3fb62abadb86tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,152.4)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.2</text></g><g class=\"ycba9e1da00-c046-43f5-8561-3fb62abadb86tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,111.09)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.4</text></g><g class=\"ycba9e1da00-c046-43f5-8561-3fb62abadb86tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,69.77)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.6</text></g></g><g class=\"cbtitleunshift\" transform=\"translate(-48,-46)\"><g class=\"cbtitle\"/></g><rect class=\"cboutline\" x=\"671\" y=\"10\" width=\"30\" height=\"241\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: none; stroke-width: 0;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vbt.TRENDLB.run(close['a'], pos_th=1, neg_th=0.5, mode='PctChangeNorm').plot().show_svg()"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["bolb_window       1                               \n", "bolb_pos_th array_0      array_1      array_2     \n", "bolb_neg_th array_0      array_1      array_2     \n", "                  a    b       a    b       a    b\n", "2020-01-01      1.0 -1.0     0.0  0.0     0.0  0.0\n", "2020-01-02     -1.0  1.0    -1.0  1.0    -1.0  1.0\n", "2020-01-03      1.0 -1.0     0.0  0.0     0.0  0.0\n", "2020-01-04      0.0 -1.0     0.0  0.0     0.0  0.0\n", "2020-01-05      0.0  1.0     0.0  1.0     0.0  1.0\n", "2020-01-06      0.0  0.0     0.0  0.0     0.0  0.0\n", "bolb_window       2                               \n", "bolb_pos_th array_0      array_1      array_2     \n", "bolb_neg_th array_0      array_1      array_2     \n", "                  a    b       a    b       a    b\n", "2020-01-01      1.0 -1.0     0.0  0.0     0.0  0.0\n", "2020-01-02     -1.0  1.0    -1.0  1.0    -1.0  1.0\n", "2020-01-03      1.0 -1.0     1.0 -1.0     0.0  0.0\n", "2020-01-04      0.0 -1.0     0.0  0.0     0.0  0.0\n", "2020-01-05      0.0  1.0     0.0  1.0     0.0  1.0\n", "2020-01-06      0.0  0.0     0.0  0.0     0.0  0.0\n", "18.2 ms ± 277 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "137 ms ± 341 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "(1000, 8000)\n"]}], "source": ["print(vbt.BOLB.run(close, window=1, pos_th=pos_ths, neg_th=neg_ths).labels)\n", "print(vbt.BOLB.run(close, window=2, pos_th=pos_ths, neg_th=neg_ths).labels)\n", "\n", "%timeit vbt.BOLB.run(big_close, window=2, pos_th=1, neg_th=0.5)\n", "%timeit vbt.BOLB.run(big_close, window=np.arange(2, 10).tolist(), pos_th=1, neg_th=0.5)\n", "\n", "print(vbt.BOLB.run(big_close, window=np.arange(2, 10).tolist(), pos_th=1, neg_th=0.5).wrapper.shape)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"800\" height=\"350\" style=\"\" viewBox=\"0 0 800 350\"><rect x=\"0\" y=\"0\" width=\"800\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-9d8626\"><g class=\"clips\"><clipPath id=\"clip9d8626xyplot\" class=\"plotclip\"><rect width=\"609.12\" height=\"261\"/></clipPath><clipPath id=\"clip9d8626xy2plot\" class=\"plotclip\"><rect width=\"609.12\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip9d8626x\"><rect x=\"48\" y=\"0\" width=\"609.12\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip9d8626y\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip9d8626xy\"><rect x=\"48\" y=\"46\" width=\"609.12\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip9d8626y2\"><rect x=\"0\" y=\"46\" width=\"800\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip9d8626xy2\"><rect x=\"48\" y=\"46\" width=\"609.12\" height=\"261\"/></clipPath></g><g class=\"gradients\"><linearGradient x1=\"0\" x2=\"0\" y1=\"1\" y2=\"0\" id=\"g9d8626-cb96cf60e5-9347-4fa6-b91b-ea2c8d6539b4\"><stop offset=\"0%\" stop-color=\"rgb(13, 8, 135)\" stop-opacity=\"1\"/><stop offset=\"11.11111111111111%\" stop-color=\"rgb(70, 3, 159)\" stop-opacity=\"1\"/><stop offset=\"22.22222222222222%\" stop-color=\"rgb(114, 1, 168)\" stop-opacity=\"1\"/><stop offset=\"33.33333333333333%\" stop-color=\"rgb(156, 23, 158)\" stop-opacity=\"1\"/><stop offset=\"44.44444444444444%\" stop-color=\"rgb(189, 55, 134)\" stop-opacity=\"1\"/><stop offset=\"55.55555555555556%\" stop-color=\"rgb(216, 87, 107)\" stop-opacity=\"1\"/><stop offset=\"66.66666666666666%\" stop-color=\"rgb(237, 121, 83)\" stop-opacity=\"1\"/><stop offset=\"77.77777777777779%\" stop-color=\"rgb(251, 159, 58)\" stop-opacity=\"1\"/><stop offset=\"88.88888888888889%\" stop-color=\"rgb(253, 202, 38)\" stop-opacity=\"1\"/><stop offset=\"100%\" stop-color=\"rgb(240, 249, 33)\" stop-opacity=\"1\"/></linearGradient></g></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"48\" y=\"46\" width=\"609.12\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(98.75999999999999,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(200.28,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(301.8,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(403.32,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(504.84,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(606.36,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,290.2)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,233.35)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,176.5)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,119.65)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,62.8)\" d=\"M48,0h609.12\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"><path class=\"xy2-x\"/><path class=\"xy2-y\"/></g><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"><g class=\"xy2-x\"/><g class=\"xy2-y\"/></g><g class=\"plot\" transform=\"translate(48,46)\" clip-path=\"url('#clip9d8626xyplot')\"><g class=\"heatmaplayer mlayer\"><g class=\"hm\"><image xmlns=\"http://www.w3.org/2000/svg\" preserveAspectRatio=\"none\" height=\"261\" width=\"609\" x=\"0\" y=\"0\" xlink:href=\"data:image/png;base64,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\" style=\"opacity: 1;\"/></g></g></g><g class=\"overplot\"><g class=\"xy2\" transform=\"translate(48,46)\" clip-path=\"url('#clip9d8626xy2plot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace66e970d9-cf76-4c19-a67f-0b20ad38e7de\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M50.76,244.2L152.28,130.5L253.8,244.2L456.84,16.8L558.36,130.5\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(50.76,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(152.28,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(253.8,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(355.32,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(456.84,16.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(558.36,130.5)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"text\"/></g></g></g></g><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"><path class=\"xy2-x crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"xy2-y crisp\" d=\"M0,0\" style=\"fill: none;\"/></g><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(98.75999999999999,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2020</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(200.28,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(301.8,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(403.32,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(504.84,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(606.36,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 6</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"47\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">labels</text></g></g><g class=\"overaxes-above\"><g class=\"xy2-x\"/><g class=\"xy2-y\"><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,290.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,233.35)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,119.65)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2.5</text></g><g class=\"y2tick\"><text text-anchor=\"start\" x=\"658.12\" y=\"4.199999999999999\" transform=\"translate(0,62.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g></g></g></g><g class=\"subplot xy2\"/></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-9d8626\"><g class=\"clips\"/><clipPath id=\"legend9d8626\"><rect width=\"76\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(620,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"76\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend9d8626')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"72.734375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"cb96cf60e5-9347-4fa6-b91b-ea2c8d6539b4 colorbar\" transform=\"translate(48,46)\"><rect class=\"cbbg\" x=\"661\" y=\"0\" width=\"79.453125\" height=\"261\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0; stroke: rgb(68, 68, 68); stroke-opacity: 1; stroke-width: 0;\"/><g class=\"cbfills\" transform=\"translate(0,10)\"><rect class=\"cbfill\" x=\"671\" width=\"30\" y=\"0\" height=\"241\" style=\"fill: url('#g9d8626-cb96cf60e5-9347-4fa6-b91b-ea2c8d6539b4');\"/></g><g class=\"cblines\" transform=\"translate(0,10)\"/><g class=\"cbaxis crisp\" transform=\"translate(0,-46)\"><g class=\"ycb96cf60e5-9347-4fa6-b91b-ea2c8d6539b4tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,297)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">−1</text></g><g class=\"ycb96cf60e5-9347-4fa6-b91b-ea2c8d6539b4tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,236.75)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">−0.5</text></g><g class=\"ycb96cf60e5-9347-4fa6-b91b-ea2c8d6539b4tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ycb96cf60e5-9347-4fa6-b91b-ea2c8d6539b4tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,116.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g><g class=\"ycb96cf60e5-9347-4fa6-b91b-ea2c8d6539b4tick\"><text text-anchor=\"start\" x=\"703.9\" y=\"4.199999999999999\" transform=\"translate(0,56)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g></g><g class=\"cbtitleunshift\" transform=\"translate(-48,-46)\"><g class=\"cbtitle\"/></g><rect class=\"cboutline\" x=\"671\" y=\"10\" width=\"30\" height=\"241\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: none; stroke-width: 0;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vbt.BOLB.run(close['a'], window=2, pos_th=1, neg_th=0.5).plot().show_svg()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 4}