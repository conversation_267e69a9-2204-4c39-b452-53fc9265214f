plotting moduleBase plotting functions. Provides functions for visualizing data in an efficient and convenient way. Each creates a figure widget that is compatible with ipywidgets and enables interactive data visualization in Jupyter Notebook and JupyterLab environments. For more details on using Plotly, see Getting Started with <PERSON>lotly in Python . The module can be accessed directly via vbt.plotting . Warning In case of errors, it won't be visible in the notebook cell, but in the logs. clean_labels functionclean_labels ( labels ) Clean labels. Plotly doesn't support multi-indexes. Bar classBar ( data = None , trace_names = None , x_labels = None , trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Class with an initialization config. All subclasses of Configured are initialized using Config , which makes it easier to pickle. Settings are defined under configured in settings . Warning If any attribute has been overwritten that isn't listed in Configured.writeable_attrs , or if any Configured.__init__ argument depends upon global defaults, their values won't be copied over. Make sure to pass them explicitly to make the saved & loaded / copied instance resilient to changes in globals. Create a bar plot. Args data : array_like Data in any format that can be converted to NumPy. Must be of shape ( x_labels , trace_names ). trace_names : str or list of str Trace names, corresponding to columns in pandas. x_labels : array_like X-axis labels, corresponding to index in pandas. trace_kwargs : dict or list of dict Keyword arguments passed to plotly.graph_objects.Bar . Can be specified per trace as a sequence of dicts. add_trace_kwargs : dict Keyword arguments passed to add_trace . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage import vectorbt as vbt bar = vbt . plotting . Bar ( ... data = [[ 1 , 2 ], [ 3 , 4 ]], ... trace_names = [ 'a' , 'b' ], ... x_labels = [ 'x' , 'y' ] ... ) bar . fig Superclasses Configured Documented Pickleable TraceUpdater Inherited members Configured.config Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Configured.writeable_attrs Pickleable.load() Pickleable.save() TraceUpdater.fig TraceUpdater.traces update methodBar . update ( data ) Update the trace data. Usage bar . update ([[ 2 , 1 ], [ 4 , 3 ]]) bar . fig Box classBox ( data = None , trace_names = None , horizontal = False , remove_nan = True , from_quantile = None , to_quantile = None , trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Class with an initialization config. All subclasses of Configured are initialized using Config , which makes it easier to pickle. Settings are defined under configured in settings . Warning If any attribute has been overwritten that isn't listed in Configured.writeable_attrs , or if any Configured.__init__ argument depends upon global defaults, their values won't be copied over. Make sure to pass them explicitly to make the saved & loaded / copied instance resilient to changes in globals. Create a box plot. For keyword arguments, see Histogram . Usage import vectorbt as vbt box = vbt . plotting . Box ( ... data = [[ 1 , 2 ], [ 3 , 4 ], [ 2 , 1 ]], ... trace_names = [ 'a' , 'b' ] ... ) box . fig Superclasses Configured Documented Pickleable TraceUpdater Inherited members Configured.config Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Configured.writeable_attrs Pickleable.load() Pickleable.save() TraceUpdater.fig TraceUpdater.traces TraceUpdater.update() from_quantile propertyFilter out data points before this quantile. horizontal propertyWhether to plot horizontally. remove_nan propertyWhether to remove NaN values. to_quantile propertyFilter out data points after this quantile. Gauge classGauge ( value = None , label = None , value_range = None , cmap_name = 'Spectral' , trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Class with an initialization config. All subclasses of Configured are initialized using Config , which makes it easier to pickle. Settings are defined under configured in settings . Warning If any attribute has been overwritten that isn't listed in Configured.writeable_attrs , or if any Configured.__init__ argument depends upon global defaults, their values won't be copied over. Make sure to pass them explicitly to make the saved & loaded / copied instance resilient to changes in globals. Create a gauge plot. Args value : float The value to be displayed. label : str The label to be displayed. value_range : tuple of float The value range of the gauge. cmap_name : str A matplotlib-compatible colormap name. See the list of available colormaps . trace_kwargs : dict Keyword arguments passed to the plotly.graph_objects.Indicator . add_trace_kwargs : dict Keyword arguments passed to add_trace . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage import vectorbt as vbt gauge = vbt . plotting . Gauge ( ... value = 2 , ... value_range = ( 1 , 3 ), ... label = 'My Gauge' ... ) gauge . fig Superclasses Configured Documented Pickleable TraceUpdater Inherited members Configured.config Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Configured.writeable_attrs Pickleable.load() Pickleable.save() TraceUpdater.fig TraceUpdater.traces TraceUpdater.update() cmap_name propertyA matplotlib-compatible colormap name. value_range propertyThe value range of the gauge. Heatmap classHeatmap ( data = None , x_labels = None , y_labels = None , is_x_category = False , is_y_category = False , trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Class with an initialization config. All subclasses of Configured are initialized using Config , which makes it easier to pickle. Settings are defined under configured in settings . Warning If any attribute has been overwritten that isn't listed in Configured.writeable_attrs , or if any Configured.__init__ argument depends upon global defaults, their values won't be copied over. Make sure to pass them explicitly to make the saved & loaded / copied instance resilient to changes in globals. Create a heatmap plot. Args data : array_like Data in any format that can be converted to NumPy. Must be of shape ( y_labels , x_labels ). x_labels : array_like X-axis labels, corresponding to columns in pandas. y_labels : array_like Y-axis labels, corresponding to index in pandas. is_x_category : bool Whether X-axis is a categorical axis. is_y_category : bool Whether Y-axis is a categorical axis. trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Heatmap . add_trace_kwargs : dict Keyword arguments passed to add_trace . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage import vectorbt as vbt heatmap = vbt . plotting . Heatmap ( ... data = [[ 1 , 2 ], [ 3 , 4 ]], ... x_labels = [ 'a' , 'b' ], ... y_labels = [ 'x' , 'y' ] ... ) heatmap . fig Superclasses Configured Documented Pickleable TraceUpdater Inherited members Configured.config Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Configured.writeable_attrs Pickleable.load() Pickleable.save() TraceUpdater.fig TraceUpdater.traces TraceUpdater.update() Histogram classHistogram ( data = None , trace_names = None , horizontal = False , remove_nan = True , from_quantile = None , to_quantile = None , trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Class with an initialization config. All subclasses of Configured are initialized using Config , which makes it easier to pickle. Settings are defined under configured in settings . Warning If any attribute has been overwritten that isn't listed in Configured.writeable_attrs , or if any Configured.__init__ argument depends upon global defaults, their values won't be copied over. Make sure to pass them explicitly to make the saved & loaded / copied instance resilient to changes in globals. Create a histogram plot. Args data : array_like Data in any format that can be converted to NumPy. Must be of shape (any, trace_names ). trace_names : str or list of str Trace names, corresponding to columns in pandas. horizontal : bool Whether to plot horizontally. remove_nan : bool Whether to remove NaN values. from_quantile : float Filter out data points before this quantile. Should be in range [0, 1] . to_quantile : float Filter out data points after this quantile. Should be in range [0, 1] . trace_kwargs : dict or list of dict Keyword arguments passed to plotly.graph_objects.Histogram . Can be specified per trace as a sequence of dicts. add_trace_kwargs : dict Keyword arguments passed to add_trace . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage import vectorbt as vbt hist = vbt . plotting . Histogram ( ... data = [[ 1 , 2 ], [ 3 , 4 ], [ 2 , 1 ]], ... trace_names = [ 'a' , 'b' ] ... ) hist . fig Superclasses Configured Documented Pickleable TraceUpdater Inherited members Configured.config Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Configured.writeable_attrs Pickleable.load() Pickleable.save() TraceUpdater.fig TraceUpdater.traces TraceUpdater.update() from_quantile propertyFilter out data points before this quantile. horizontal propertyWhether to plot horizontally. remove_nan propertyWhether to remove NaN values. to_quantile propertyFilter out data points after this quantile. Scatter classScatter ( data = None , trace_names = None , x_labels = None , trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Class with an initialization config. All subclasses of Configured are initialized using Config , which makes it easier to pickle. Settings are defined under configured in settings . Warning If any attribute has been overwritten that isn't listed in Configured.writeable_attrs , or if any Configured.__init__ argument depends upon global defaults, their values won't be copied over. Make sure to pass them explicitly to make the saved & loaded / copied instance resilient to changes in globals. Create a scatter plot. Args data : array_like Data in any format that can be converted to NumPy. Must be of shape ( x_labels , trace_names ). trace_names : str or list of str Trace names, corresponding to columns in pandas. x_labels : array_like X-axis labels, corresponding to index in pandas. trace_kwargs : dict or list of dict Keyword arguments passed to plotly.graph_objects.Scatter . Can be specified per trace as a sequence of dicts. add_trace_kwargs : dict Keyword arguments passed to add_trace . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage import vectorbt as vbt scatter = vbt . plotting . Scatter ( ... data = [[ 1 , 2 ], [ 3 , 4 ]], ... trace_names = [ 'a' , 'b' ], ... x_labels = [ 'x' , 'y' ] ... ) scatter . fig Superclasses Configured Documented Pickleable TraceUpdater Inherited members Configured.config Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Configured.writeable_attrs Pickleable.load() Pickleable.save() TraceUpdater.fig TraceUpdater.traces TraceUpdater.update() TraceUpdater classTraceUpdater ( fig , traces ) Base trace updating class. Subclasses Bar Box Gauge Heatmap Histogram Scatter Volume fig propertyFigure. traces propertyTraces to update. update methodTraceUpdater . update ( * args , ** kwargs ) Update the trace data. Volume classVolume ( data = None , x_labels = None , y_labels = None , z_labels = None , trace_kwargs = None , add_trace_kwargs = None , scene_name = 'scene' , fig = None , ** layout_kwargs ) Class with an initialization config. All subclasses of Configured are initialized using Config , which makes it easier to pickle. Settings are defined under configured in settings . Warning If any attribute has been overwritten that isn't listed in Configured.writeable_attrs , or if any Configured.__init__ argument depends upon global defaults, their values won't be copied over. Make sure to pass them explicitly to make the saved & loaded / copied instance resilient to changes in globals. Create a volume plot. Args data : array_like Data in any format that can be converted to NumPy. Must be a 3-dim array. x_labels : array_like X-axis labels. y_labels : array_like Y-axis labels. z_labels : array_like Z-axis labels. trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Volume . add_trace_kwargs : dict Keyword arguments passed to add_trace . scene_name : str Reference to the 3D scene. fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Note Figure widgets have currently problems displaying NaNs. Use .show() method for rendering. Usage import vectorbt as vbt import numpy as np volume = vbt . plotting . Volume ( ... data = np . random . randint ( 1 , 10 , size = ( 3 , 3 , 3 )), ... x_labels = [ 'a' , 'b' , 'c' ], ... y_labels = [ 'd' , 'e' , 'f' ], ... z_labels = [ 'g' , 'h' , 'i' ] ... ) volume . fig Superclasses Configured Documented Pickleable TraceUpdater Inherited members Configured.config Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Configured.writeable_attrs Pickleable.load() Pickleable.save() TraceUpdater.fig TraceUpdater.traces TraceUpdater.update()