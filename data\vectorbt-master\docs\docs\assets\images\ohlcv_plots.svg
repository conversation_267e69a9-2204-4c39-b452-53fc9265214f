<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="750" height="380" style="" viewBox="0 0 750 380"><rect x="0" y="0" width="750" height="380" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-aac1e3"><g class="clips"><clipPath id="clipaac1e3xyplot" class="plotclip"><rect width="687" height="260"/></clipPath><clipPath class="axesclip" id="clipaac1e3x"><rect x="33" y="0" width="687" height="380"/></clipPath><clipPath class="axesclip" id="clipaac1e3y"><rect x="0" y="68" width="750" height="260"/></clipPath><clipPath class="axesclip" id="clipaac1e3xy"><rect x="33" y="68" width="687" height="260"/></clipPath></g><g class="gradients"/><g class="patterns"/></defs><g class="bglayer"><rect class="bg" x="33" y="68" width="687" height="260" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(239.1,0)" d="M0,68v260" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(376.5,0)" d="M0,68v260" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(513.9,0)" d="M0,68v260" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(651.3,0)" d="M0,68v260" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,315)" d="M33,0h687" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,276)" d="M33,0h687" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,237)" d="M33,0h687" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,198)" d="M33,0h687" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,159)" d="M33,0h687" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,120)" d="M33,0h687" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,81)" d="M33,0h687" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="xzl zl crisp" transform="translate(101.7,0)" d="M0,68v260" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(33,68)" clip-path="url(#clipaac1e3xyplot)"><g class="boxlayer mlayer"><g class="trace boxes" style="opacity: 1;"><path class="box" d="M35.04,170H102.36M35.04,208H102.36V169H35.04ZM68.7,208V247M68.7,169V130" style="vector-effect: non-scaling-stroke; stroke-width: 2px; stroke: rgb(27, 158, 118); stroke-opacity: 1; fill: rgb(27, 158, 118); fill-opacity: 0.5; opacity: 1;"/><path class="box" d="M172.44,92H239.76M172.44,130H239.76V91H172.44ZM206.1,130V169M206.1,91V52" style="vector-effect: non-scaling-stroke; stroke-width: 2px; stroke: rgb(27, 158, 118); stroke-opacity: 1; fill: rgb(27, 158, 118); fill-opacity: 0.5; opacity: 1;"/><path class="box" d="M309.84,52H377.16M309.84,52H377.16V52H309.84ZM343.5,52V91M343.5,52V13" style="vector-effect: non-scaling-stroke; stroke-width: 2px; stroke: rgb(27, 158, 118); stroke-opacity: 1; fill: rgb(27, 158, 118); fill-opacity: 0.5; opacity: 1;"/><path class="box" d="M447.24,129H514.56M447.24,130H514.56V91H447.24ZM480.9,130V169M480.9,91V52" style="vector-effect: non-scaling-stroke; stroke-width: 2px; stroke: rgb(217, 95, 2); stroke-opacity: 1; fill: rgb(217, 95, 2); fill-opacity: 0.5; opacity: 1;"/><path class="box" d="M584.64,207H651.96M584.64,208H651.96V169H584.64ZM618.3,208V247M618.3,169V130" style="vector-effect: non-scaling-stroke; stroke-width: 2px; stroke: rgb(217, 95, 2); stroke-opacity: 1; fill: rgb(217, 95, 2); fill-opacity: 0.5; opacity: 1;"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="341" transform="translate(101.7,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">0</text></g><g class="xtick"><text text-anchor="middle" x="0" y="341" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(239.1,0)">1</text></g><g class="xtick"><text text-anchor="middle" x="0" y="341" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(376.5,0)">2</text></g><g class="xtick"><text text-anchor="middle" x="0" y="341" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(513.9,0)">3</text></g><g class="xtick"><text text-anchor="middle" x="0" y="341" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(651.3,0)">4</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,315)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">1.5</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,276)">2</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,237)">2.5</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,198)">3</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,159)">3.5</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,120)">4</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,81)">4.5</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="iciclelayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-aac1e3"><g class="clips"/><clipPath id="legendaac1e3"><rect width="114" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(606,11.631578947368453)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="114" height="29" x="0" y="0"/><g class="scrollbox" transform="" clip-path="url(#legendaac1e3)"><g class="groups"><g class="traces" transform="translate(0,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Candlestick</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="legendcandle" d="M15,0H8M8,-6V6H-8Z" transform="translate(20,0)" style="stroke-miterlimit: 1; stroke-width: 2px; fill: rgb(217, 95, 2); fill-opacity: 0.5; stroke: rgb(217, 95, 2); stroke-opacity: 1;"/><path class="legendcandle" d="M-15,0H-8M-8,6V-6H8Z" transform="translate(20,0)" style="stroke-miterlimit: 1; stroke-width: 2px; fill: rgb(27, 158, 118); fill-opacity: 0.5; stroke: rgb(27, 158, 118); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="111.234375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"><text class="xtitle" x="376.5" y="368.3" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Index</text></g><g class="g-ytitle"/><g class="annotation" data-index="0" style="opacity: 1;"><g class="annotation-text-g" transform="rotate(0,376.5,56)"><g class="cursor-pointer" transform="translate(352,44)"><rect class="bg" x="0.5" y="0.5" width="48" height="23" style="stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;"/><text class="annotation-text" text-anchor="middle" x="24.359375" y="18" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">OHLC</text></g></g></g></g></svg>