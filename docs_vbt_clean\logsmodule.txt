logs moduleBase class for working with log records. Order records capture information on simulation logs. Logs are populated when simulating a portfolio and can be accessed as Portfolio.logs . import pandas as pd import numpy as np from datetime import datetime , timedelta import vectorbt as vbt np . random . seed ( 42 ) price = pd . DataFrame ({ ... 'a' : np . random . uniform ( 1 , 2 , size = 100 ), ... 'b' : np . random . uniform ( 1 , 2 , size = 100 ) ... }, index = [ datetime ( 2020 , 1 , 1 ) + timedelta ( days = i ) for i in range ( 100 )]) size = pd . DataFrame ({ ... 'a' : np . random . uniform ( - 100 , 100 , size = 100 ), ... 'b' : np . random . uniform ( - 100 , 100 , size = 100 ), ... }, index = [ datetime ( 2020 , 1 , 1 ) + timedelta ( days = i ) for i in range ( 100 )]) pf = vbt . Portfolio . from_orders ( price , size , fees = 0.01 , freq = 'd' , log = True ) logs = pf . logs logs . filled . count () a 88 b 99 Name: count, dtype: int64 logs . ignored . count () a 0 b 0 Name: count, dtype: int64 logs . rejected . count () a 12 b 1 Name: count, dtype: int64 StatsHint See StatsBuilderMixin.stats() and Logs.metrics . logs [ 'a' ] . stats () Start 2020-01-01 00:00:00 End 2020-04-09 00:00:00 Period 100 days 00:00:00 Total Records 100 Status Counts: None 0 Status Counts: Filled 88 Status Counts: Ignored 0 Status Counts: Rejected 12 Status Info Counts: None 88 Status Info Counts: NoCashLong 12 Name: a, dtype: object StatsBuilderMixin.stats() also supports (re-)grouping: logs . stats ( group_by = True ) Start 2020-01-01 00:00:00 End 2020-04-09 00:00:00 Period 100 days 00:00:00 Total Records 200 Status Counts: None 0 Status Counts: Filled 187 Status Counts: Ignored 0 Status Counts: Rejected 13 Status Info Counts: None 187 Status Info Counts: NoCashLong 13 Name: group, dtype: object PlotsHint See PlotsBuilderMixin.plots() and Logs.subplots . This class does not have any subplots. logs_attach_field_config ConfigConfig of fields to be attached to Logs . Co nf ig( { "res_side" : { "attach_filters" : true }, "res_status" : { "attach_filters" : true }, "res_status_info" : { "attach_filters" : true } } ) logs_field_config ConfigField config for Logs . Co nf ig( { "dtype" : { "id" : "int64" , "group" : "int64" , "col" : "int64" , "idx" : "int64" , "cash" : "float64" , "position" : "float64" , "debt" : "float64" , "free_cash" : "float64" , "val_price" : "float64" , "value" : "float64" , "req_size" : "float64" , "req_price" : "float64" , "req_size_type" : "int64" , "req_direction" : "int64" , "req_fees" : "float64" , "req_fixed_fees" : "float64" , "req_slippage" : "float64" , "req_min_size" : "float64" , "req_max_size" : "float64" , "req_size_granularity" : "float64" , "req_reject_prob" : "float64" , "req_lock_cash" : "bool" , "req_allow_partial" : "bool" , "req_raise_reject" : "bool" , "req_log" : "bool" , "new_cash" : "float64" , "new_position" : "float64" , "new_debt" : "float64" , "new_free_cash" : "float64" , "new_val_price" : "float64" , "new_value" : "float64" , "res_size" : "float64" , "res_price" : "float64" , "res_fees" : "float64" , "res_side" : "int64" , "res_status" : "int64" , "res_status_info" : "int64" , "order_id" : "int64" }, "settings" : { "id" : { "title" : "Log Id" }, "group" : { "title" : "Group" }, "cash" : { "title" : "Cash" }, "position" : { "title" : "Position" }, "debt" : { "title" : "Debt" }, "free_cash" : { "title" : "Free Cash" }, "val_price" : { "title" : "Val Price" }, "value" : { "title" : "Value" }, "req_size" : { "title" : "Request Size" }, "req_price" : { "title" : "Request Price" }, "req_size_type" : { "title" : "Request Size Type" , "mapping" : { "Amount" : 0 , "Value" : 1 , "Percent" : 2 , "TargetAmount" : 3 , "TargetValue" : 4 , "TargetPercent" : 5 } }, "req_direction" : { "title" : "Request Direction" , "mapping" : { "LongOnly" : 0 , "ShortOnly" : 1 , "Both" : 2 } }, "req_fees" : { "title" : "Request Fees" }, "req_fixed_fees" : { "title" : "Request Fixed Fees" }, "req_slippage" : { "title" : "Request Slippage" }, "req_min_size" : { "title" : "Request Min Size" }, "req_max_size" : { "title" : "Request Max Size" }, "req_size_granularity" : { "title" : "Request Size Granularity" }, "req_reject_prob" : { "title" : "Request Rejection Prob" }, "req_lock_cash" : { "title" : "Request Lock Cash" }, "req_allow_partial" : { "title" : "Request Allow Partial" }, "req_raise_reject" : { "title" : "Request Raise Rejection" }, "req_log" : { "title" : "Request Log" }, "new_cash" : { "title" : "New Cash" }, "new_position" : { "title" : "New Position" }, "new_debt" : { "title" : "New Debt" }, "new_free_cash" : { "title" : "New Free Cash" }, "new_val_price" : { "title" : "New Val Price" }, "new_value" : { "title" : "New Value" }, "res_size" : { "title" : "Result Size" }, "res_price" : { "title" : "Result Price" }, "res_fees" : { "title" : "Result Fees" }, "res_side" : { "title" : "Result Side" , "mapping" : { "Buy" : 0 , "Sell" : 1 } }, "res_status" : { "title" : "Result Status" , "mapping" : { "Filled" : 0 , "Ignored" : 1 , "Rejected" : 2 } }, "res_status_info" : { "title" : "Result Status Info" , "mapping" : { "SizeNaN" : 0 , "PriceNaN" : 1 , "ValPriceNaN" : 2 , "ValueNaN" : 3 , "ValueZeroNeg" : 4 , "SizeZero" : 5 , "NoCashShort" : 6 , "NoCashLong" : 7 , "NoOpenPosition" : 8 , "MaxSizeExceeded" : 9 , "RandomEvent" : 10 , "CantCoverFees" : 11 , "MinSizeNotReached" : 12 , "PartialFill" : 13 } }, "order_id" : { "title" : "Order Id" } } } ) Logs classLogs ( wrapper , records_arr , col_mapper = None , ** kwargs ) Extends Records for working with log records. Superclasses AttrResolver Configured Documented IndexingBase PandasIndexer Pickleable PlotsBuilderMixin Records RecordsWithFields StatsBuilderMixin Wrapping Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.to_doc() Configured.update_config() PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() Records.apply() Records.apply_mask() Records.build_field_config_doc() Records.col_arr Records.col_mapper Records.config Records.count() Records.get_apply_mapping_arr() Records.get_by_col_idxs() Records.get_field_arr() Records.get_field_mapping() Records.get_field_name() Records.get_field_setting() Records.get_field_title() Records.get_map_field() Records.get_map_field_to_index() Records.id_arr Records.idx_arr Records.iloc Records.indexing_func() Records.indexing_func_meta() Records.indexing_kwargs Records.is_sorted() Records.loc Records.map() Records.map_array() Records.map_field() Records.override_field_config_doc() Records.records Records.records_arr Records.records_readable Records.replace() Records.self_aliases Records.sort() Records.values Records.wrapper Records.writeable_attrs StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() buy methodRecords filtered by res_side == 0 . cant_cover_fees methodRecords filtered by res_status_info == 11 . cash methodMapped array of the field cash . col methodMapped array of the field col . debt methodMapped array of the field debt . field_config class variableField config of Logs . Co nf ig( { "dtype" : { "id" : "int64" , "group" : "int64" , "col" : "int64" , "idx" : "int64" , "cash" : "float64" , "position" : "float64" , "debt" : "float64" , "free_cash" : "float64" , "val_price" : "float64" , "value" : "float64" , "req_size" : "float64" , "req_price" : "float64" , "req_size_type" : "int64" , "req_direction" : "int64" , "req_fees" : "float64" , "req_fixed_fees" : "float64" , "req_slippage" : "float64" , "req_min_size" : "float64" , "req_max_size" : "float64" , "req_size_granularity" : "float64" , "req_reject_prob" : "float64" , "req_lock_cash" : "bool" , "req_allow_partial" : "bool" , "req_raise_reject" : "bool" , "req_log" : "bool" , "new_cash" : "float64" , "new_position" : "float64" , "new_debt" : "float64" , "new_free_cash" : "float64" , "new_val_price" : "float64" , "new_value" : "float64" , "res_size" : "float64" , "res_price" : "float64" , "res_fees" : "float64" , "res_side" : "int64" , "res_status" : "int64" , "res_status_info" : "int64" , "order_id" : "int64" }, "settings" : { "id" : { "name" : "id" , "title" : "Log Id" }, "col" : { "name" : "col" , "title" : "Column" , "mapping" : "columns" }, "idx" : { "name" : "idx" , "title" : "Timestamp" , "mapping" : "index" }, "group" : { "title" : "Group" }, "cash" : { "title" : "Cash" }, "position" : { "title" : "Position" }, "debt" : { "title" : "Debt" }, "free_cash" : { "title" : "Free Cash" }, "val_price" : { "title" : "Val Price" }, "value" : { "title" : "Value" }, "req_size" : { "title" : "Request Size" }, "req_price" : { "title" : "Request Price" }, "req_size_type" : { "title" : "Request Size Type" , "mapping" : { "Amount" : 0 , "Value" : 1 , "Percent" : 2 , "TargetAmount" : 3 , "TargetValue" : 4 , "TargetPercent" : 5 } }, "req_direction" : { "title" : "Request Direction" , "mapping" : { "LongOnly" : 0 , "ShortOnly" : 1 , "Both" : 2 } }, "req_fees" : { "title" : "Request Fees" }, "req_fixed_fees" : { "title" : "Request Fixed Fees" }, "req_slippage" : { "title" : "Request Slippage" }, "req_min_size" : { "title" : "Request Min Size" }, "req_max_size" : { "title" : "Request Max Size" }, "req_size_granularity" : { "title" : "Request Size Granularity" }, "req_reject_prob" : { "title" : "Request Rejection Prob" }, "req_lock_cash" : { "title" : "Request Lock Cash" }, "req_allow_partial" : { "title" : "Request Allow Partial" }, "req_raise_reject" : { "title" : "Request Raise Rejection" }, "req_log" : { "title" : "Request Log" }, "new_cash" : { "title" : "New Cash" }, "new_position" : { "title" : "New Position" }, "new_debt" : { "title" : "New Debt" }, "new_free_cash" : { "title" : "New Free Cash" }, "new_val_price" : { "title" : "New Val Price" }, "new_value" : { "title" : "New Value" }, "res_size" : { "title" : "Result Size" }, "res_price" : { "title" : "Result Price" }, "res_fees" : { "title" : "Result Fees" }, "res_side" : { "title" : "Result Side" , "mapping" : { "Buy" : 0 , "Sell" : 1 } }, "res_status" : { "title" : "Result Status" , "mapping" : { "Filled" : 0 , "Ignored" : 1 , "Rejected" : 2 } }, "res_status_info" : { "title" : "Result Status Info" , "mapping" : { "SizeNaN" : 0 , "PriceNaN" : 1 , "ValPriceNaN" : 2 , "ValueNaN" : 3 , "ValueZeroNeg" : 4 , "SizeZero" : 5 , "NoCashShort" : 6 , "NoCashLong" : 7 , "NoOpenPosition" : 8 , "MaxSizeExceeded" : 9 , "RandomEvent" : 10 , "CantCoverFees" : 11 , "MinSizeNotReached" : 12 , "PartialFill" : 13 } }, "order_id" : { "title" : "Order Id" } } } ) filled methodRecords filtered by res_status == 0 . free_cash methodMapped array of the field free_cash . group methodMapped array of the field group . id methodMapped array of the field id . idx methodMapped array of the field idx . ignored methodRecords filtered by res_status == 1 . max_size_exceeded methodRecords filtered by res_status_info == 9 . metrics class variableMetrics supported by Logs . Co nf ig( { "start" : { "title" : "Start" , "calc_func" : "<function Logs.<lambda> at 0x12d3ddd00>" , "agg_func" : null , "tags" : "wrapper" }, "end" : { "title" : "End" , "calc_func" : "<function Logs.<lambda> at 0x12d3ddda0>" , "agg_func" : null , "tags" : "wrapper" }, "period" : { "title" : "Period" , "calc_func" : "<function Logs.<lambda> at 0x12d3dde40>" , "apply_to_timedelta" : true , "agg_func" : null , "tags" : "wrapper" }, "total_records" : { "title" : "Total Records" , "calc_func" : "count" , "tags" : "records" }, "res_status_counts" : { "title" : "Status Counts" , "calc_func" : "res_status.value_counts" , "incl_all_keys" : true , "post_calc_func" : "<function Logs.<lambda> at 0x12d3ddee0>" , "tags" : [ "logs" , "res_status" , "value_counts" ] }, "res_status_info_counts" : { "title" : "Status Info Counts" , "calc_func" : "res_status_info.value_counts" , "post_calc_func" : "<function Logs.<lambda> at 0x12d3ddf80>" , "tags" : [ "logs" , "res_status_info" , "value_counts" ] } } ) Returns Logs._metrics , which gets (deep) copied upon creation of each instance. Thus, changing this config won't affect the class. To change metrics, you can either change the config in-place, override this property, or overwrite the instance variable Logs._metrics . min_size_not_reached methodRecords filtered by res_status_info == 12 . new_cash methodMapped array of the field new_cash . new_debt methodMapped array of the field new_debt . new_free_cash methodMapped array of the field new_free_cash . new_position methodMapped array of the field new_position . new_val_price methodMapped array of the field new_val_price . new_value methodMapped array of the field new_value . no_cash_long methodRecords filtered by res_status_info == 7 . no_cash_short methodRecords filtered by res_status_info == 6 . no_open_position methodRecords filtered by res_status_info == 8 . order_id methodMapped array of the field order_id . partial_fill methodRecords filtered by res_status_info == 13 . plots_defaults propertyDefaults for PlotsBuilderMixin.plots() . Merges Records.plots_defaults and logs.plots from settings . position methodMapped array of the field position . price_nan methodRecords filtered by res_status_info == 1 . random_event methodRecords filtered by res_status_info == 10 . rejected methodRecords filtered by res_status == 2 . req_allow_partial methodMapped array of the field req_allow_partial . req_direction methodMapped array of the field req_direction . req_fees methodMapped array of the field req_fees . req_fixed_fees methodMapped array of the field req_fixed_fees . req_lock_cash methodMapped array of the field req_lock_cash . req_log methodMapped array of the field req_log . req_max_size methodMapped array of the field req_max_size . req_min_size methodMapped array of the field req_min_size . req_price methodMapped array of the field req_price . req_raise_reject methodMapped array of the field req_raise_reject . req_reject_prob methodMapped array of the field req_reject_prob . req_size methodMapped array of the field req_size . req_size_granularity methodMapped array of the field req_size_granularity . req_size_type methodMapped array of the field req_size_type . req_slippage methodMapped array of the field req_slippage . res_fees methodMapped array of the field res_fees . res_price methodMapped array of the field res_price . res_side methodMapped array of the field res_side . res_size methodMapped array of the field res_size . res_status methodMapped array of the field res_status . res_status_info methodMapped array of the field res_status_info . sell methodRecords filtered by res_side == 1 . size_nan methodRecords filtered by res_status_info == 0 . size_zero methodRecords filtered by res_status_info == 5 . stats_defaults propertyDefaults for StatsBuilderMixin.stats() . Merges Records.stats_defaults and logs.stats from settings . subplots class variableSubplots supported by Logs . Co nf ig( {} ) Returns Logs._subplots , which gets (deep) copied upon creation of each instance. Thus, changing this config won't affect the class. To change subplots, you can either change the config in-place, override this property, or overwrite the instance variable Logs._subplots . val_price methodMapped array of the field val_price . val_price_nan methodRecords filtered by res_status_info == 2 . value methodMapped array of the field value . value_nan methodRecords filtered by res_status_info == 3 . value_zero_neg methodRecords filtered by res_status_info == 4 .