px_accessors modulePlotly Express pandas accessors. Note Accessors do not utilize caching. attach_px_methods functionattach_px_methods ( cls ) Class decorator to attach Plotly Express methods. PXAccessor classPXAccessor ( obj , ** kwargs ) Accessor for running Plotly Express functions. Accessible through pd.Series.vbt.px and pd.DataFrame.vbt.px . Usage import pandas as pd import vectorbt as vbt vbt . settings . set_theme ( 'seaborn' ) pd . Series ([ 1 , 2 , 3 ]) . vbt . px . bar () Superclasses AttrResolver BaseAccessor Configured Documented IndexingBase PandasIndexer Pickleable Wrapping Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() BaseAccessor.align_to() BaseAccessor.apply() BaseAccessor.apply_and_concat() BaseAccessor.apply_on_index() BaseAccessor.broadcast() BaseAccessor.broadcast_to() BaseAccessor.combine() BaseAccessor.concat() BaseAccessor.config BaseAccessor.df_accessor_cls BaseAccessor.drop_duplicate_levels() BaseAccessor.drop_levels() BaseAccessor.drop_redundant_levels() BaseAccessor.empty() BaseAccessor.empty_like() BaseAccessor.iloc BaseAccessor.indexing_func() BaseAccessor.indexing_kwargs BaseAccessor.loc BaseAccessor.make_symmetric() BaseAccessor.obj BaseAccessor.rename_levels() BaseAccessor.repeat() BaseAccessor.select_levels() BaseAccessor.self_aliases BaseAccessor.sr_accessor_cls BaseAccessor.stack_index() BaseAccessor.tile() BaseAccessor.to_1d_array() BaseAccessor.to_2d_array() BaseAccessor.to_dict() BaseAccessor.unstack_to_array() BaseAccessor.unstack_to_df() BaseAccessor.wrapper BaseAccessor.writeable_attrs Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() PandasIndexer.xs() Pickleable.load() Pickleable.save() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses PXDFAccessor PXSRAccessor area methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) bar methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) bar_polar methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) box methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) choropleth methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) choropleth_map methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) choropleth_mapbox methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) density_contour methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) density_heatmap methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) density_map methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) density_mapbox methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) ecdf methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) funnel methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) funnel_area methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) histogram methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) icicle methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) imshow methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) line methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) line_3d methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) line_geo methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) line_map methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) line_mapbox methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) line_polar methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) line_ternary methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) parallel_categories methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) parallel_coordinates methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) pie methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) scatter methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) scatter_3d methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) scatter_geo methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) scatter_map methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) scatter_mapbox methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) scatter_matrix methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) scatter_polar methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) scatter_ternary methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) strip methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) sunburst methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) timeline methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) treemap methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) violin methodattach_px_methods .< locals >. plot_func ( * args , ** kwargs ) PXDFAccessor classPXDFAccessor ( obj , ** kwargs ) Accessor for running Plotly Express functions. For DataFrames only. Accessible through pd.DataFrame.vbt.px . Superclasses AttrResolver BaseAccessor BaseDFAccessor Configured Documented IndexingBase PXAccessor PandasIndexer Pickleable Wrapping Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() BaseAccessor.align_to() BaseAccessor.apply() BaseAccessor.apply_and_concat() BaseAccessor.apply_on_index() BaseAccessor.broadcast() BaseAccessor.broadcast_to() BaseAccessor.combine() BaseAccessor.concat() BaseAccessor.drop_duplicate_levels() BaseAccessor.drop_levels() BaseAccessor.drop_redundant_levels() BaseAccessor.empty() BaseAccessor.empty_like() BaseAccessor.indexing_func() BaseAccessor.make_symmetric() BaseAccessor.rename_levels() BaseAccessor.repeat() BaseAccessor.select_levels() BaseAccessor.stack_index() BaseAccessor.tile() BaseAccessor.to_1d_array() BaseAccessor.to_2d_array() BaseAccessor.to_dict() BaseAccessor.unstack_to_array() BaseAccessor.unstack_to_df() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() PXAccessor.config PXAccessor.df_accessor_cls PXAccessor.iloc PXAccessor.indexing_kwargs PXAccessor.loc PXAccessor.obj PXAccessor.self_aliases PXAccessor.sr_accessor_cls PXAccessor.wrapper PXAccessor.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() PXSRAccessor classPXSRAccessor ( obj , ** kwargs ) Accessor for running Plotly Express functions. For Series only. Accessible through pd.Series.vbt.px . Superclasses AttrResolver BaseAccessor BaseSRAccessor Configured Documented IndexingBase PXAccessor PandasIndexer Pickleable Wrapping Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() BaseAccessor.align_to() BaseAccessor.apply() BaseAccessor.apply_and_concat() BaseAccessor.apply_on_index() BaseAccessor.broadcast() BaseAccessor.broadcast_to() BaseAccessor.combine() BaseAccessor.concat() BaseAccessor.drop_duplicate_levels() BaseAccessor.drop_levels() BaseAccessor.drop_redundant_levels() BaseAccessor.empty() BaseAccessor.empty_like() BaseAccessor.indexing_func() BaseAccessor.make_symmetric() BaseAccessor.rename_levels() BaseAccessor.repeat() BaseAccessor.select_levels() BaseAccessor.stack_index() BaseAccessor.tile() BaseAccessor.to_1d_array() BaseAccessor.to_2d_array() BaseAccessor.to_dict() BaseAccessor.unstack_to_array() BaseAccessor.unstack_to_df() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() PXAccessor.config PXAccessor.df_accessor_cls PXAccessor.iloc PXAccessor.indexing_kwargs PXAccessor.loc PXAccessor.obj PXAccessor.self_aliases PXAccessor.sr_accessor_cls PXAccessor.wrapper PXAccessor.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj()