mapping moduleMapping utilities. apply_mapping functionapply_mapping ( obj , mapping_like = None , reverse = False , ignore_case = True , ignore_underscores = True , ignore_type = None , ignore_missing = False , na_sentinel = None ) Apply mapping on object using a mapping-like object. Args obj : any Any object. Can take a scalar, tuple, list, set, frozenset, NumPy array, Index, Series, and DataFrame. mapping_like : mapping_like Any mapping-like object. See to_mapping() . reverse : bool See reverse in to_mapping() . ignore_case : bool Whether to ignore the case if the key is a string. ignore_underscores : bool Whether to ignore underscores if the key is a string. ignore_type : dtype_like or tuple One or multiple types or data types to ignore. ignore_missing : bool Whether to ignore missing values. na_sentinel : any Value to mark “not found”. reverse_mapping functionreverse_mapping ( mapping ) Reverse a mapping. Returns a dict. to_mapping functionto_mapping ( mapping_like , reverse = False ) Convert mapping-like object to a mapping. Enable reverse to apply reverse_mapping() on the result dict.