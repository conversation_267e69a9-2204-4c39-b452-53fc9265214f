enum_ moduleEnum utilities. In vectorbt, enums are represented by instances of named tuples to be easily used in Numba. Their values start with 0, while -1 means there is no value. map_enum_fields functionmap_enum_fields ( field , enum , ignore_type = builtins . int , ** kwargs ) Map fields to values. See apply_mapping() . map_enum_values functionmap_enum_values ( value , enum , ignore_type = builtins . str , ** kwargs ) Map values to fields. See apply_mapping() .