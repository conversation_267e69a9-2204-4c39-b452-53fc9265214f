plots_builder moduleMixin for building plots out of subplots. MetaPlotsBuilderMixin classMetaPlotsBuilderMixin ( * args , ** kwargs ) Meta class that exposes a read-only class property PlotsBuilderMixin.subplots . Superclasses builtins.type Subclasses MetaData MetaGenericAccessor MetaIndicatorBase MetaMappedArray MetaPortfolio MetaRecords subplots propertySubplots supported by PlotsBuilderMixin.plots() . PlotsBuilderMixin classPlotsBuilderMixin () Mixin that implements PlotsBuilderMixin.plots() . Required to be a subclass of Wrapping . Subclasses Data GenericAccessor IndicatorBase MappedArray Portfolio Records build_subplots_doc class methodPlotsBuilderMixin . build_subplots_doc ( source_cls = None ) Build subplots documentation. override_subplots_doc class methodPlotsBuilderMixin . override_subplots_doc ( __pdoc__ , source_cls = None ) Call this method on each subclass that overrides subplots . plots methodPlotsBuilderMixin . plots ( subplots = None , tags = None , column = None , group_by = None , silence_warnings = None , template_mapping = None , settings = None , filters = None , subplot_settings = None , show_titles = None , hide_id_labels = None , group_id_labels = None , make_subplots_kwargs = None , ** layout_kwargs ) Plot various parts of this object. Args subplots : str , tuple , iterable , or dict Subplots to plot. Each element can be either: a subplot name (see keys in PlotsBuilderMixin.subplots ) a tuple of a subplot name and a settings dict as in PlotsBuilderMixin.subplots . The settings dict can contain the following keys: title : Title of the subplot. Defaults to the name. plot_func (required): Plotting function for custom subplots. Should write the supplied figure fig in-place and can return anything (it won't be used). xaxis_kwargs : Layout keyword arguments for the x-axis. Defaults to dict(title='Index') . yaxis_kwargs : Layout keyword arguments for the y-axis. Defaults to empty dict. tags , check_{filter} , inv_check_{filter} , resolve_plot_func , pass_{arg} , resolve_path_{arg} , resolve_{arg} and template_mapping : The same as in StatsBuilderMixin for calc_func . Any other keyword argument that overrides the settings or is passed directly to plot_func . If resolve_plot_func is True, the plotting function may "request" any of the following arguments by accepting them or if pass_{arg} was found in the settings dict: Each of AttrResolver.self_aliases : original object (ungrouped, with no column selected) group_by : won't be passed if it was used in resolving the first attribute of plot_func specified as a path, use pass_group_by=True to pass anyway column subplot_name trace_names : list with the subplot name, can't be used in templates add_trace_kwargs : dict with subplot row and column index xref yref xaxis yaxis x_domain y_domain fig silence_warnings Any argument from settings Any attribute of this object if it meant to be resolved (see AttrResolver.resolve_attr() ) Note Layout-related resolution arguments such as add_trace_kwargs are unavailable before filtering and thus cannot be used in any templates but can still be overridden. Pass subplots='all' to plot all supported subplots. tags : str or iterable See tags in StatsBuilderMixin . column : str See column in StatsBuilderMixin . group_by : any See group_by in StatsBuilderMixin . silence_warnings : bool See silence_warnings in StatsBuilderMixin . template_mapping : mapping See template_mapping in StatsBuilderMixin . Applied on settings , make_subplots_kwargs , and layout_kwargs , and then on each subplot settings. filters : dict See filters in StatsBuilderMixin . settings : dict See settings in StatsBuilderMixin . subplot_settings : dict See metric_settings in StatsBuilderMixin . show_titles : bool Whether to show the title of each subplot. hide_id_labels : bool Whether to hide identical legend labels. Two labels are identical if their name, marker style and line style match. group_id_labels : bool Whether to group identical legend labels. make_subplots_kwargs : dict Keyword arguments passed to plotly.subplots.make_subplots . **layout_kwargs Keyword arguments used to update the layout of the figure. Note PlotsBuilderMixin and StatsBuilderMixin are very similar. Some artifacts follow the same concept, just named differently: plots_defaults vs stats_defaults subplots vs metrics subplot_settings vs metric_settings See further notes under StatsBuilderMixin . Usage See vectorbt.portfolio.base for examples. plots_defaults propertyDefaults for PlotsBuilderMixin.plots() . See plots_builder in settings . subplots class variableSubplots supported by PlotsBuilderMixin . Co nf ig( {} ) Returns PlotsBuilderMixin._subplots , which gets (deep) copied upon creation of each instance. Thus, changing this config won't affect the class. To change subplots, you can either change the config in-place, override this property, or overwrite the instance variable PlotsBuilderMixin._subplots . writeable_attrs propertySet of writeable attributes that will be saved/copied along with the config.