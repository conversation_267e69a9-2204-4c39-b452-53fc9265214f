index_fns moduleFunctions for working with index/columns. Index functions perform operations on index objects, such as stacking, combining, and cleansing MultiIndex levels. "Index" in pandas context is referred to both index and columns. align_index_to functionalign_index_to ( index1 , index2 ) Align index1 to have the same shape as index2 if they have any levels in common. Returns index slice for the aligning. align_indexes functionalign_indexes ( indexes ) Align multiple indexes to each other. combine_indexes functioncombine_indexes ( indexes , ignore_default = None , ** kwargs ) Combine each index in indexes using Cartesian product. Keyword arguments will be passed to stack_indexes() . drop_duplicate_levels functiondrop_duplicate_levels ( index , keep = None ) Drop levels in index with the same name and values. Set keep to 'last' to keep last levels, otherwise 'first'. Set keep to None to use the default. drop_levels functiondrop_levels ( index , levels , strict = True ) Drop levels in index by their name/position. drop_redundant_levels functiondrop_redundant_levels ( index ) Drop levels in index that either have a single unnamed value or a range from 0 to n. find_first_occurrence functionfind_first_occurrence ( index_value , index ) Return index of the first occurrence in index . get_index functionget_index ( arg , axis ) Get index of arg by axis . index_from_values functionindex_from_values ( values , name = None ) Create a new pd.Index with name by parsing an iterable values . Each in values will correspond to an element in the new index. pick_levels functionpick_levels ( index , required_levels = None , optional_levels = None ) Pick optional and required levels and return their indices. Raises an exception if index has less or more levels than expected. rename_levels functionrename_levels ( index , name_dict , strict = True ) Rename levels in index by name_dict . repeat_index functionrepeat_index ( index , n , ignore_default = None ) Repeat each element in index n times. Set ignore_default to None to use the default. select_levels functionselect_levels ( index , level_names ) Build a new index by selecting one or multiple level_names from index . stack_indexes functionstack_indexes ( indexes , drop_duplicates = None , keep = None , drop_redundant = None ) Stack each index in indexes on top of each other, from top to bottom. Set drop_duplicates , keep , or drop_redundant to None to use the default. tile_index functiontile_index ( index , n , ignore_default = None ) Tile the whole index n times. Set ignore_default to None to use the default. to_any_index functionto_any_index ( index_like ) Convert any index-like object to an index. Index objects are kept as-is.