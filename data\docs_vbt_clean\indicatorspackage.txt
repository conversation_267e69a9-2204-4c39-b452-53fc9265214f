indicators packageModules for building and running indicators. Technical indicators are used to see past trends and anticipate future moves. See Using Technical Indicators to Develop Trading Strategies . pandas_ta functionpandas_ta ( * args , ** kwargs ) Shortcut for IndicatorFactory.from_pandas_ta() . ta functionta ( * args , ** kwargs ) Shortcut for IndicatorFactory.from_ta() . talib functiontalib ( * args , ** kwargs ) Shortcut for IndicatorFactory.from_talib() . Sub-modulesvectorbt.indicators.basic vectorbt.indicators.configs vectorbt.indicators.factory vectorbt.indicators.nb