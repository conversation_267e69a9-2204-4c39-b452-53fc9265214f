checks moduleUtilities for validation during runtime. assert_array_equal functionassert_array_equal ( arg1 , arg2 ) Raise exception if the first argument and the second argument have different metadata or values. assert_dict_sequence_valid functionassert_dict_sequence_valid ( arg , lvl_keys ) Raise exception if a dict or any dict in a sequence of dicts has keys that are not in lvl_keys . assert_dict_valid functionassert_dict_valid ( arg , lvl_keys ) Raise exception if dict the argument has keys that are not in lvl_keys . lvl_keys should be a list of lists, each corresponding to a level in the dict. assert_dtype functionassert_dtype ( arg , dtype ) Raise exception if the argument is not of data type dtype . assert_dtype_equal functionassert_dtype_equal ( arg1 , arg2 ) Raise exception if the first argument and the second argument have different data types. assert_equal functionassert_equal ( arg1 , arg2 , deep = False ) Raise exception if the first argument and the second argument are different. assert_in functionassert_in ( arg1 , arg2 ) Raise exception if the first argument is not in the second argument. assert_index_equal functionassert_index_equal ( arg1 , arg2 , ** kwargs ) Raise exception if the first argument and the second argument have different index/columns. assert_instance_of functionassert_instance_of ( arg , types ) Raise exception if the argument is none of types types . assert_iterable functionassert_iterable ( arg ) Raise exception if the argument is not an iterable. assert_len_equal functionassert_len_equal ( arg1 , arg2 ) Raise exception if the first argument and the second argument have different length. Does not transform arguments to NumPy arrays. assert_level_not_exists functionassert_level_not_exists ( arg , level_name ) Raise exception if index the argument has level level_name . assert_meta_equal functionassert_meta_equal ( arg1 , arg2 ) Raise exception if the first argument and the second argument have different metadata. assert_ndim functionassert_ndim ( arg , ndims ) Raise exception if the argument has a different number of dimensions than ndims . assert_not_none functionassert_not_none ( arg ) Raise exception if the argument is None. assert_numba_func functionassert_numba_func ( func ) Raise exception if func is not Numba-compiled. assert_sequence functionassert_sequence ( arg ) Raise exception if the argument is not a sequence. assert_shape_equal functionassert_shape_equal ( arg1 , arg2 , axis = None ) Raise exception if the first argument and the second argument have different shapes along axis . assert_subclass_of functionassert_subclass_of ( arg , classes ) Raise exception if the argument is not a subclass of classes classes . assert_subdtype functionassert_subdtype ( arg , dtype ) Raise exception if the argument is not a sub data type of dtype . assert_type_equal functionassert_type_equal ( arg1 , arg2 ) Raise exception if the first argument and the second argument have different types. iskeyword functionfrozenset . __contains__ ( ... ) x. contains (y) <==> y in x. func_accepts_arg functionfunc_accepts_arg ( func , arg_name , arg_kind = None ) Check whether func accepts a positional or keyword argument with name arg_name . is_any_array functionis_any_array ( arg ) Check whether the argument is any of np.ndarray , pd.Series or pd.DataFrame . is_deep_equal functionis_deep_equal ( arg1 , arg2 , check_exact = False , ** kwargs ) Check whether two objects are equal (deep check). is_default_index functionis_default_index ( arg ) Check whether index is a basic range. is_equal functionis_equal ( arg1 , arg2 , equality_func =< function < lambda >> ) Check whether two objects are equal. is_frame functionis_frame ( arg ) Check whether the argument is pd.DataFrame . is_hashable functionis_hashable ( arg ) Check whether the argument can be hashed. is_index functionis_index ( arg ) Check whether the argument is pd.Index . is_index_equal functionis_index_equal ( arg1 , arg2 , strict = True ) Check whether indexes are equal. Introduces naming tests on top of pd.Index.equals , but still doesn't check for types. is_instance_of functionis_instance_of ( arg , types ) Check whether the argument is an instance of types . types can be one or multiple types or strings. is_iterable functionis_iterable ( arg ) Check whether the argument is iterable. is_mapping functionis_mapping ( arg ) Check whether the arguments is a mapping. is_mapping_like functionis_mapping_like ( arg ) Check whether the arguments is a mapping-like object. is_namedtuple functionis_namedtuple ( x ) Check whether object is an instance of namedtuple. is_np_array functionis_np_array ( arg ) Check whether the argument is np.ndarray . is_numba_func functionis_numba_func ( arg ) Check whether the argument is a Numba-compiled function. is_pandas functionis_pandas ( arg ) Check whether the argument is pd.Series or pd.DataFrame . is_sequence functionis_sequence ( arg ) Check whether the argument is a sequence. is_series functionis_series ( arg ) Check whether the argument is pd.Series . is_subclass_of functionis_subclass_of ( arg , types ) Check whether the argument is a subclass of types . types can be one or multiple types or strings. is_valid_variable_name functionis_valid_variable_name ( arg ) Check whether the argument is a valid variable name. safe_assert functionsafe_assert ( arg , msg = None )