{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# signals"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import vectorbt as vbt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"Collapsed": "false"}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "from numba import njit"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Disable caching for performance testing\n", "vbt.settings.caching['enabled'] = False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## accessors"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["index = pd.Index([\n", "    datetime(2018, 1, 1),\n", "    datetime(2018, 1, 2),\n", "    datetime(2018, 1, 3),\n", "    datetime(2018, 1, 4),\n", "    datetime(2018, 1, 5)\n", "])\n", "columns = ['a', 'b', 'c']\n", "big_index = [datetime(2018, 1, 1) + timedelta(days=i) for i in range(1000)]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(5, 3)\n", "(1000, 1000)\n"]}], "source": ["entries = pd.DataFrame({\n", "    'a': [True, <PERSON>als<PERSON>, <PERSON>alse, <PERSON>alse, <PERSON>als<PERSON>],\n", "    'b': [True, False, True, False, True],\n", "    'c': [True, True, True, False, False],\n", "}, index=index)\n", "print(entries.shape)\n", "\n", "big_entries = pd.DataFrame(np.full((1000, 1000), False), index=big_index)\n", "big_entries.iloc[::10] = True\n", "print(big_entries.shape)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(5,)\n", "(1000,)\n"]}], "source": ["ts = pd.Series([1., 2., 3., 2., 1.], index=index, name=columns[0])\n", "print(ts.shape)\n", "\n", "big_ts = pd.Series(np.random.uniform(10, 13, size=(1000,)), index=big_index)\n", "print(big_ts.shape)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["price = pd.DataFrame({\n", "    'open': [10, 11, 12, 11, 10],\n", "    'high': [11, 12, 13, 12, 11],\n", "    'low': [9, 10, 11, 10, 9],\n", "    'close': [10, 11, 12, 11, 10]\n", "})"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(5, 3)\n", "(1000, 1000)\n"]}], "source": ["a = np.random.randint(-1, 2, size=(5, 3))\n", "print(a.shape)\n", "\n", "big_a = np.random.randint(-1, 2, size=(1000, 1000))\n", "print(big_a.shape)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                a      b      c\n", "2018-01-01  False  False   True\n", "2018-01-02  False  False   True\n", "2018-01-03  False   True  False\n", "2018-01-04   True   True  False\n", "2018-01-05  False   True   True\n", "11 ms ± 33.2 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["print(entries.vbt.signals.shuffle(seed=42))\n", "\n", "%timeit big_entries.vbt.signals.shuffle(seed=42)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"Collapsed": "false"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02  False  False  False\n", "2018-01-03   <PERSON>   True   True\n", "2018-01-04  False  False   True\n", "2018-01-05  False   True   True\n", "1.39 ms ± 11.4 µs per loop (mean ± std. dev. of 7 runs, 1000 loops each)\n"]}], "source": ["print(entries.vbt.signals.fshift(2))\n", "\n", "%timeit big_entries.vbt.signals.fshift(2)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01    False\n", "2018-01-02    False\n", "2018-01-03    False\n", "2018-01-04    False\n", "2018-01-05    False\n", "dtype: bool\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02  False  False  False\n", "2018-01-03  False  False  False\n", "2018-01-04  False  False  False\n", "2018-01-05  False  False  False\n", "55.7 µs ± 641 ns per loop (mean ± std. dev. of 7 runs, 10000 loops each)\n"]}], "source": ["print(pd.Series.vbt.signals.empty(5, index=index))\n", "print(pd.DataFrame.vbt.signals.empty((5, 3), index=index, columns=columns))\n", "\n", "%timeit pd.DataFrame.vbt.signals.empty((1000, 1000))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01    False\n", "2018-01-02    False\n", "2018-01-03    False\n", "2018-01-04    False\n", "2018-01-05     True\n", "dtype: bool\n", "                a      b      c\n", "2018-01-01  False   True  False\n", "2018-01-02  False  False  False\n", "2018-01-03   True  False   True\n", "2018-01-04  False  False  False\n", "2018-01-05  False  False  False\n", "12.4 ms ± 25.6 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["@njit\n", "def choice_func_nb(from_i, to_i, col):\n", "    return np.random.choice(np.arange(from_i, to_i), size=1, replace=False)\n", "\n", "print(pd.Series.vbt.signals.generate(5, choice_func_nb, index=index))\n", "print(pd.DataFrame.vbt.signals.generate((5, 3), choice_func_nb, index=index, columns=columns))\n", "\n", "%timeit pd.DataFrame.vbt.signals.generate((1000, 1000), choice_func_nb)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01     True\n", "2018-01-02    False\n", "2018-01-03     True\n", "2018-01-04    False\n", "2018-01-05     True\n", "dtype: bool\n", "2018-01-01    False\n", "2018-01-02     True\n", "2018-01-03    False\n", "2018-01-04     True\n", "2018-01-05    False\n", "dtype: bool\n", "                a      b      c\n", "2018-01-01   <PERSON>   True   True\n", "2018-01-02  False  False  False\n", "2018-01-03   <PERSON>   True   True\n", "2018-01-04  False  False  False\n", "2018-01-05   <PERSON>   True   True\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02   <PERSON>   True   True\n", "2018-01-03  False  False  False\n", "2018-01-04   <PERSON>   True   True\n", "2018-01-05  False  False  False\n", "               a     b     c\n", "2018-01-01  <PERSON>  True  True\n", "2018-01-02  <PERSON>  True  True\n", "2018-01-03  <PERSON>  True  True\n", "2018-01-04  <PERSON>  True  True\n", "2018-01-05  <PERSON>  True  True\n", "               a     b     c\n", "2018-01-01  <PERSON>  True  True\n", "2018-01-02  <PERSON>  True  True\n", "2018-01-03  <PERSON>  True  True\n", "2018-01-04  <PERSON>  True  True\n", "2018-01-05  <PERSON>  True  True\n", "               a     b     c\n", "2018-01-01  <PERSON>  True  True\n", "2018-01-02  <PERSON>  True  True\n", "2018-01-03  <PERSON>  True  True\n", "2018-01-04  <PERSON>  True  True\n", "2018-01-05  <PERSON>  True  True\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02   <PERSON>   True   True\n", "2018-01-03   <PERSON>   True   True\n", "2018-01-04   <PERSON>   True   True\n", "2018-01-05   <PERSON>   True   True\n", "16.3 ms ± 53.7 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["@njit\n", "def entry_func_nb(from_i, to_i, col, temp_int):\n", "    temp_int[0] = from_i\n", "    return temp_int[:1]\n", "\n", "@njit\n", "def exit_func_nb(from_i, to_i, col, temp_int):\n", "    temp_int[0] = from_i\n", "    return temp_int[:1]\n", "\n", "temp_int = np.empty((1000,), dtype=np.int64)\n", "en, ex = pd.Series.vbt.signals.generate_both(\n", "    a.shape[0], entry_func_nb, (temp_int,), exit_func_nb, (temp_int,), \n", "    index=index)\n", "print(en)\n", "print(ex)\n", "en, ex = pd.DataFrame.vbt.signals.generate_both(\n", "    a.shape, entry_func_nb, (temp_int,), exit_func_nb, (temp_int,), \n", "    index=index, columns=columns)\n", "print(en)\n", "print(ex)\n", "en, ex = pd.DataFrame.vbt.signals.generate_both(\n", "    a.shape, entry_func_nb, (temp_int,), exit_func_nb, (temp_int,), \n", "    index=index, columns=columns, entry_wait=1, exit_wait=0)\n", "print(en)\n", "print(ex)\n", "en, ex = pd.DataFrame.vbt.signals.generate_both(\n", "    a.shape, entry_func_nb, (temp_int,), exit_func_nb, (temp_int,), \n", "    index=index, columns=columns, entry_wait=0, exit_wait=1)\n", "print(en)\n", "print(ex)\n", "\n", "%timeit pd.DataFrame.vbt.signals.generate_both(\\\n", "    big_a.shape, entry_func_nb, (temp_int,), exit_func_nb, (temp_int,))"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02   True   True  False\n", "2018-01-03  False  False  False\n", "2018-01-04  <PERSON><PERSON>e   True   True\n", "2018-01-05  False  False  False\n", "                a      b      c\n", "2018-01-01   <PERSON>   True   True\n", "2018-01-02  False  False   True\n", "2018-01-03  False   True   True\n", "2018-01-04  False  False  False\n", "2018-01-05  False   True  False\n", "22.1 ms ± 66.3 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["@njit\n", "def choice_func_nb(from_i, to_i, col, temp_int):\n", "    temp_int[0] = from_i\n", "    return temp_int[:1]\n", "\n", "print(entries.vbt.signals.generate_exits(choice_func_nb, temp_int))\n", "print(entries.vbt.signals.generate_exits(choice_func_nb, temp_int, wait=0))\n", "\n", "%timeit big_entries.vbt.signals.generate_exits(choice_func_nb, temp_int)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01    False\n", "2018-01-02     True\n", "2018-01-03    False\n", "2018-01-04    False\n", "2018-01-05     True\n", "dtype: bool\n", "                a      b      c\n", "2018-01-01  False  False   True\n", "2018-01-02   <PERSON>   True   True\n", "2018-01-03  False  False  False\n", "2018-01-04  False   True  False\n", "2018-01-05   True  False  False\n", "                a      b      c\n", "2018-01-01  False  False   True\n", "2018-01-02  False  False   True\n", "2018-01-03  False  False  False\n", "2018-01-04  False   True  False\n", "2018-01-05  False  False  False\n", "12.6 ms ± 91.7 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["print(pd.Series.vbt.signals.generate_random(5, n=2, seed=42, index=index))\n", "print(pd.DataFrame.vbt.signals.generate_random((5, 3), n=2, seed=42, index=index, columns=columns))\n", "print(pd.DataFrame.vbt.signals.generate_random((5, 3), n=[0, 1, 2], seed=42, index=index, columns=columns))\n", "\n", "%timeit pd.DataFrame.vbt.signals.generate_random((1000, 1000), n=100)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01     True\n", "2018-01-02    False\n", "2018-01-03    False\n", "2018-01-04    False\n", "2018-01-05     True\n", "dtype: bool\n", "                a      b      c\n", "2018-01-01   <PERSON>   True   True\n", "2018-01-02  False   True  False\n", "2018-01-03  False  False  False\n", "2018-01-04  False  False   True\n", "2018-01-05   True  False   True\n", "                a      b     c\n", "2018-01-01  False   True  True\n", "2018-01-02  False   True  True\n", "2018-01-03  False  False  True\n", "2018-01-04  False  False  True\n", "2018-01-05  False  False  True\n", "20 ms ± 48.7 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(pd.Series.vbt.signals.generate_random(5, prob=0.5, seed=42, index=index))\n", "print(pd.DataFrame.vbt.signals.generate_random((5, 3), prob=0.5, seed=42, index=index, columns=columns))\n", "print(pd.DataFrame.vbt.signals.generate_random((5, 3), prob=[0., 0.5, 1], seed=42, index=index, columns=columns))\n", "\n", "%timeit pd.DataFrame.vbt.signals.generate_random((1000, 1000), prob=0.5)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01    False\n", "2018-01-02    False\n", "2018-01-03     True\n", "2018-01-04    False\n", "2018-01-05    False\n", "Name: a, dtype: bool\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02  False   True  False\n", "2018-01-03   True  False  False\n", "2018-01-04  False   True  False\n", "2018-01-05  False  False   True\n", "                a      b      c\n", "2018-01-01  False   True   True\n", "2018-01-02   True  False   True\n", "2018-01-03  False  False  False\n", "2018-01-04  <PERSON><PERSON>e   True   True\n", "2018-01-05  False   True  False\n", "57.9 ms ± 1.05 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(entries['a'].vbt.signals.generate_random_exits(seed=42))\n", "print(entries.vbt.signals.generate_random_exits(seed=42))\n", "print(entries.vbt.signals.generate_random_exits(seed=42, wait=0))\n", "\n", "%timeit big_entries.vbt.signals.generate_random_exits(seed=42)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01    False\n", "2018-01-02     True\n", "2018-01-03    False\n", "2018-01-04    False\n", "2018-01-05    False\n", "Name: a, dtype: bool\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02   True   True  False\n", "2018-01-03  False  False  False\n", "2018-01-04  <PERSON><PERSON>e   True   True\n", "2018-01-05  False  False  False\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02  False   True  False\n", "2018-01-03  False  False  False\n", "2018-01-04  <PERSON><PERSON>e   True   True\n", "2018-01-05  False  False  False\n", "                a      b      c\n", "2018-01-01   <PERSON>   True   True\n", "2018-01-02  False  False   True\n", "2018-01-03  False   True   True\n", "2018-01-04  False  False  False\n", "2018-01-05  False   True  False\n", "7.6 ms ± 81.3 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["print(entries['a'].vbt.signals.generate_random_exits(prob=1., seed=42))\n", "print(entries.vbt.signals.generate_random_exits(prob=1., seed=42))\n", "print(entries.vbt.signals.generate_random_exits(prob=[0., 0.5, 1], seed=42))\n", "print(entries.vbt.signals.generate_random_exits(prob=1., seed=42, wait=0))\n", "\n", "%timeit big_entries.vbt.signals.generate_random_exits(prob=1., seed=42)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01     True\n", "2018-01-02    False\n", "2018-01-03     True\n", "2018-01-04    False\n", "2018-01-05    False\n", "dtype: bool\n", "2018-01-01    False\n", "2018-01-02     True\n", "2018-01-03    False\n", "2018-01-04    False\n", "2018-01-05     True\n", "dtype: bool\n", "                a      b      c\n", "2018-01-01   <PERSON>   True   True\n", "2018-01-02  False  False  False\n", "2018-01-03   True   True  False\n", "2018-01-04  False  False   True\n", "2018-01-05  False  False  False\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02   <PERSON>   True   True\n", "2018-01-03  False  False  False\n", "2018-01-04  False   True  False\n", "2018-01-05   True  False   True\n", "                a      b      c\n", "2018-01-01  False  False   True\n", "2018-01-02  False   True  False\n", "2018-01-03  False  False  False\n", "2018-01-04  False  False   True\n", "2018-01-05  False  False  False\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02  False  False   True\n", "2018-01-03  False  False  False\n", "2018-01-04  False   True  False\n", "2018-01-05  False  False   True\n", "      0     1     2\n", "0  True  True  True\n", "1  True  True  True\n", "      0     1     2\n", "0  True  True  True\n", "1  True  True  True\n", "       0      1      2\n", "0   True   True   True\n", "1   True   True   True\n", "2  False  False  False\n", "       0      1      2\n", "0  False  False  False\n", "1   True   True   True\n", "2   True   True   True\n", "       0      1      2\n", "0   True   True   True\n", "1  False  False  False\n", "2  False  False  False\n", "3  False  False  False\n", "4   True   True   True\n", "5  False  False  False\n", "6  False  False  False\n", "       0      1      2\n", "0  False  False  False\n", "1  False  False  False\n", "2   True   True   True\n", "3  False  False  False\n", "4  False  False  False\n", "5  False  False  False\n", "6   True   True   True\n", "18.1 ms ± 108 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "13.6 ms ± 66.2 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["en, ex = pd.Series.vbt.signals.generate_random_both(5, n=2, seed=42, index=index)\n", "print(en)\n", "print(ex)\n", "en, ex = pd.DataFrame.vbt.signals.generate_random_both((5, 3), n=2, seed=42, index=index, columns=columns)\n", "print(en)\n", "print(ex)\n", "en, ex = pd.DataFrame.vbt.signals.generate_random_both((5, 3), n=[0, 1, 2], seed=42, index=index, columns=columns)\n", "print(en)\n", "print(ex)\n", "en, ex = pd.DataFrame.vbt.signals.generate_random_both((2, 3), n=2, seed=42, entry_wait=1, exit_wait=0)\n", "print(en)\n", "print(ex)\n", "en, ex = pd.DataFrame.vbt.signals.generate_random_both((3, 3), n=2, seed=42, entry_wait=0, exit_wait=1)\n", "print(en)\n", "print(ex)\n", "en, ex = pd.DataFrame.vbt.signals.generate_random_both((7, 3), n=2, seed=42, entry_wait=2, exit_wait=2)\n", "print(en)\n", "print(ex)\n", "\n", "%timeit pd.DataFrame.vbt.signals.generate_random_both((1000, 1000), n=100)\n", "%timeit pd.DataFrame.vbt.signals.generate_random_both((1000, 1000), n=100, exit_wait=0)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 454915.  931362. 1423666. 1894465. 2368430. 2859191. 3338920. 3817177.\n", " 4288740. 4759056. 5236445. 5708131. 6183311. 6675387. 7160407. 7633895.\n", " 8117773. 8596158. 9073611. 9541106.]\n", "[ 467525.  948228. 1415395. 1894228. 2371659. 2850128. 3329587. 3800772.\n", " 4277064. 4754194. 5233391. 5708592. 6180180. 6652306. 7127212. 7610069.\n", " 8087227. 8567550. 9043232. 9518372.]\n"]}], "source": ["n = 10\n", "a = np.full(n * 2, 0.)\n", "for i in range(10000):\n", "    en, ex = pd.Series.vbt.signals.generate_random_both(1000, n, entry_wait=2, exit_wait=2)\n", "    _a = np.empty((n * 2,), dtype=np.int64)\n", "    _a[0::2] = np.flatnonzero(en)\n", "    _a[1::2] = np.flatnonzero(ex)\n", "    a += _a\n", "print(a)\n", "\n", "b = np.full(n * 2, 0.)\n", "for i in range(10000):\n", "    b += np.sort(np.random.choice(1000, size=n * 2, replace=False))\n", "print(b)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01     True\n", "2018-01-02    False\n", "2018-01-03    False\n", "2018-01-04    False\n", "2018-01-05     True\n", "dtype: bool\n", "2018-01-01    False\n", "2018-01-02     True\n", "2018-01-03    False\n", "2018-01-04    False\n", "2018-01-05    False\n", "dtype: bool\n", "                a      b      c\n", "2018-01-01   <PERSON>   True   True\n", "2018-01-02  False  False  False\n", "2018-01-03  False  False  False\n", "2018-01-04  False  False   True\n", "2018-01-05   True  False  False\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02   <PERSON>   True   True\n", "2018-01-03  False  False  False\n", "2018-01-04  False  False  False\n", "2018-01-05  False  False   True\n", "                a      b      c\n", "2018-01-01  False   True   True\n", "2018-01-02  False  False  False\n", "2018-01-03  False  False   True\n", "2018-01-04  False  False  False\n", "2018-01-05  False  False   True\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02  False   True   True\n", "2018-01-03  False  False  False\n", "2018-01-04  False  False   True\n", "2018-01-05  False  False  False\n", "               a     b     c\n", "2018-01-01  <PERSON>  True  True\n", "2018-01-02  <PERSON>  True  True\n", "2018-01-03  <PERSON>  True  True\n", "2018-01-04  <PERSON>  True  True\n", "2018-01-05  <PERSON>  True  True\n", "               a     b     c\n", "2018-01-01  <PERSON>  True  True\n", "2018-01-02  <PERSON>  True  True\n", "2018-01-03  <PERSON>  True  True\n", "2018-01-04  <PERSON>  True  True\n", "2018-01-05  <PERSON>  True  True\n", "44.6 ms ± 66.1 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["en, ex = pd.Series.vbt.signals.generate_random_both(\n", "    5, entry_prob=0.5, exit_prob=1., seed=42, index=index)\n", "print(en)\n", "print(ex)\n", "en, ex = pd.DataFrame.vbt.signals.generate_random_both(\n", "    (5, 3), entry_prob=0.5, exit_prob=1., seed=42, index=index, columns=columns)\n", "print(en)\n", "print(ex)\n", "en, ex = pd.DataFrame.vbt.signals.generate_random_both(\n", "    (5, 3), entry_prob=[0., 0.5, 1.], exit_prob=[0., 0.5, 1.], seed=42, index=index, columns=columns)\n", "print(en)\n", "print(ex)\n", "en, ex = pd.DataFrame.vbt.signals.generate_random_both(\n", "    (5, 3), entry_prob=1., exit_prob=1., seed=42, index=index, columns=columns, exit_wait=0)\n", "print(en)\n", "print(ex)\n", "\n", "%timeit pd.DataFrame.vbt.signals.generate_random_both(\\\n", "    (1000, 1000), entry_prob=1., exit_prob=1.)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0    False\n", "1    False\n", "2    False\n", "3    False\n", "4    False\n", "5     True\n", "dtype: bool\n", "0    False\n", "1    False\n", "2    False\n", "3     True\n", "4    False\n", "5    False\n", "dtype: bool\n", "0    False\n", "1    False\n", "2    False\n", "3     True\n", "4     True\n", "5     True\n", "dtype: bool\n", "       0      1      2\n", "0  False  False  False\n", "1  False  False  False\n", "2  False  False  False\n", "3  False  False  False\n", "4  False   True  False\n", "5  False   True  False\n", "0    False\n", "1    False\n", "2    False\n", "3    False\n", "4     True\n", "5    False\n", "dtype: bool\n", "0    False\n", "1    False\n", "2    False\n", "3    False\n", "4    False\n", "5     True\n", "dtype: bool\n", "0    False\n", "1    False\n", "2    False\n", "3     True\n", "4    False\n", "5    False\n", "dtype: bool\n", "0    False\n", "1    False\n", "2    False\n", "3     True\n", "4     True\n", "5     True\n", "dtype: bool\n", "       0      1      2\n", "0  False  False  False\n", "1  False  False  False\n", "2  False  False  False\n", "3  False   True   True\n", "4  False   True   True\n", "5  False   True   True\n", "0    False\n", "1    False\n", "2    False\n", "3    False\n", "4     True\n", "5    False\n", "dtype: bool\n", "11.1 ms ± 131 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n", "10.9 ms ± 71.1 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n", "32.8 ms ± 195 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["e = pd.Series([True, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse, <PERSON>alse])\n", "t = pd.Series([2, 3, 4, 3, 2, 1]).astype(np.float64)\n", "\n", "print(e.vbt.signals.generate_stop_exits(t, -0.1))\n", "print(e.vbt.signals.generate_stop_exits(t, -0.1, trailing=True))\n", "print(e.vbt.signals.generate_stop_exits(t, -0.1, trailing=True, pick_first=False))\n", "print(e.vbt.signals.generate_stop_exits(t.vbt.tile(3), [np.nan, -0.5, -1.], trailing=True, pick_first=False))\n", "print(e.vbt.signals.generate_stop_exits(t, -0.1, trailing=True, exit_wait=3))\n", "\n", "print(e.vbt.signals.generate_stop_exits(4 - t, 0.1))\n", "print(e.vbt.signals.generate_stop_exits(4 - t, 0.1, trailing=True))\n", "print(e.vbt.signals.generate_stop_exits(4 - t, 0.1, trailing=True, pick_first=False))\n", "print(e.vbt.signals.generate_stop_exits((4 - t).vbt.tile(3), [np.nan, 0.5, 1.], trailing=True, pick_first=False))\n", "print(e.vbt.signals.generate_stop_exits(4 - t, 0.1, trailing=True, exit_wait=3))\n", "\n", "%timeit big_entries.vbt.signals.generate_stop_exits(big_ts, -0.1)\n", "%timeit big_entries.vbt.signals.generate_stop_exits(big_ts, -0.1, trailing=True)\n", "%timeit big_entries.vbt.signals.generate_stop_exits(big_ts, -0.1, trailing=True, pick_first=False)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0     True\n", "1    False\n", "2    False\n", "3    False\n", "4     True\n", "5    False\n", "dtype: bool\n", "0    False\n", "1    False\n", "2    False\n", "3     True\n", "4    False\n", "5     True\n", "dtype: bool\n", "0     True\n", "1    False\n", "2    False\n", "3    False\n", "4    False\n", "5     True\n", "dtype: bool\n", "0    False\n", "1    False\n", "2    False\n", "3     True\n", "4    False\n", "5    False\n", "dtype: bool\n", "0     True\n", "1    False\n", "2    False\n", "3    False\n", "4     True\n", "5    False\n", "dtype: bool\n", "0    False\n", "1    False\n", "2    False\n", "3     True\n", "4    False\n", "5    False\n", "dtype: bool\n", "7.1 ms ± 137 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "22.5 ms ± 79.1 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "7.89 ms ± 56.6 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["e = pd.Series([True, True, True, True, True, True])\n", "t = pd.Series([2, 3, 4, 3, 2, 1]).astype(np.float64)\n", "\n", "en, ex = e.vbt.signals.generate_stop_exits(t, -0.1, trailing=True, chain=True)\n", "print(en)\n", "print(ex)\n", "en, ex = e.vbt.signals.generate_stop_exits(t, -0.1, trailing=True, entry_wait=2, chain=True)\n", "print(en)\n", "print(ex)\n", "en, ex = e.vbt.signals.generate_stop_exits(t, -0.1, trailing=True, exit_wait=2, chain=True)\n", "print(en)\n", "print(ex)\n", "\n", "%timeit big_entries.vbt.signals.generate_stop_exits(big_ts, -0.1, chain=True)\n", "%timeit big_entries.vbt.signals.generate_stop_exits(big_ts, -0.1, trailing=True, chain=True)\n", "%timeit big_entries.vbt.signals.generate_stop_exits(big_ts, -0.1, trailing=True, pick_first=False, chain=True)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02  False  False  False\n", "2018-01-03  False  False  False\n", "2018-01-04  False  False  False\n", "2018-01-05  False  False  False\n", "             a   b   c\n", "2018-01-01 NaN NaN NaN\n", "2018-01-02 NaN NaN NaN\n", "2018-01-03 NaN NaN NaN\n", "2018-01-04 NaN NaN NaN\n", "2018-01-05 NaN NaN NaN\n", "            a  b  c\n", "2018-01-01 -1 -1 -1\n", "2018-01-02 -1 -1 -1\n", "2018-01-03 -1 -1 -1\n", "2018-01-04 -1 -1 -1\n", "2018-01-05 -1 -1 -1\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02  False  False  False\n", "2018-01-03  False  False  False\n", "2018-01-04  <PERSON><PERSON>e   True   True\n", "2018-01-05   True  False  False\n", "              a     b     c\n", "2018-01-01  NaN   NaN   NaN\n", "2018-01-02  NaN   NaN   NaN\n", "2018-01-03  NaN   NaN   NaN\n", "2018-01-04  NaN  10.8  10.8\n", "2018-01-05  9.0   NaN   NaN\n", "            a  b  c\n", "2018-01-01 -1 -1 -1\n", "2018-01-02 -1 -1 -1\n", "2018-01-03 -1 -1 -1\n", "2018-01-04 -1  0  0\n", "2018-01-05  0 -1 -1\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02  False  False  False\n", "2018-01-03  False  False  False\n", "2018-01-04   <PERSON>   True   True\n", "2018-01-05  False  False  False\n", "               a     b     c\n", "2018-01-01   NaN   NaN   NaN\n", "2018-01-02   NaN   NaN   NaN\n", "2018-01-03   NaN   NaN   NaN\n", "2018-01-04  11.7  10.8  10.8\n", "2018-01-05   NaN   NaN   NaN\n", "            a  b  c\n", "2018-01-01 -1 -1 -1\n", "2018-01-02 -1 -1 -1\n", "2018-01-03 -1 -1 -1\n", "2018-01-04  1  1  1\n", "2018-01-05 -1 -1 -1\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02   True   True  False\n", "2018-01-03  False  False  False\n", "2018-01-04  False  False  False\n", "2018-01-05  False  False  False\n", "               a     b   c\n", "2018-01-01   NaN   NaN NaN\n", "2018-01-02  11.0  11.0 NaN\n", "2018-01-03   NaN   NaN NaN\n", "2018-01-04   NaN   NaN NaN\n", "2018-01-05   NaN   NaN NaN\n", "            a  b  c\n", "2018-01-01 -1 -1 -1\n", "2018-01-02  2  2 -1\n", "2018-01-03 -1 -1 -1\n", "2018-01-04 -1 -1 -1\n", "2018-01-05 -1 -1 -1\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02   True   True  False\n", "2018-01-03  False  False  False\n", "2018-01-04  <PERSON><PERSON>e   True   True\n", "2018-01-05  False  False  False\n", "               a     b     c\n", "2018-01-01   NaN   NaN   NaN\n", "2018-01-02  11.0  11.0   NaN\n", "2018-01-03   NaN   NaN   NaN\n", "2018-01-04   NaN  10.8  10.8\n", "2018-01-05   NaN   NaN   NaN\n", "            a  b  c\n", "2018-01-01 -1 -1 -1\n", "2018-01-02  2  2 -1\n", "2018-01-03 -1 -1 -1\n", "2018-01-04 -1  1  1\n", "2018-01-05 -1 -1 -1\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02  False  False  False\n", "2018-01-03  False  False  False\n", "2018-01-04  False  False  False\n", "2018-01-05  False  False  False\n", "             a   b   c\n", "2018-01-01 NaN NaN NaN\n", "2018-01-02 NaN NaN NaN\n", "2018-01-03 NaN NaN NaN\n", "2018-01-04 NaN NaN NaN\n", "2018-01-05 NaN NaN NaN\n", "            a  b  c\n", "2018-01-01 -1 -1 -1\n", "2018-01-02 -1 -1 -1\n", "2018-01-03 -1 -1 -1\n", "2018-01-04 -1 -1 -1\n", "2018-01-05 -1 -1 -1\n", "                a      b      c\n", "2018-01-01   <PERSON>   True   True\n", "2018-01-02  False  False  False\n", "2018-01-03  False  False  False\n", "2018-01-04  <PERSON><PERSON>e   True   True\n", "2018-01-05  False   True  False\n", "              a     b     c\n", "2018-01-01  9.0   9.0   9.0\n", "2018-01-02  NaN   NaN   NaN\n", "2018-01-03  NaN   NaN   NaN\n", "2018-01-04  NaN  11.7  11.7\n", "2018-01-05  NaN   9.0   NaN\n", "            a  b  c\n", "2018-01-01  1  1  1\n", "2018-01-02 -1 -1 -1\n", "2018-01-03 -1 -1 -1\n", "2018-01-04 -1  1  1\n", "2018-01-05 -1  1 -1\n", "20.3 ms ± 853 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["def test_ohlc_stop_exits(**kwargs):\n", "    out_dict = {}\n", "    result = entries.vbt.signals.generate_ohlc_stop_exits(\n", "        price['open'], price['high'], price['low'], price['close'],\n", "        out_dict=out_dict, **kwargs\n", "    )\n", "    if isinstance(result, tuple):\n", "        _, ex = result\n", "    else:\n", "        ex = result\n", "    out_dict['stop_price'][~ex] = np.nan\n", "    out_dict['stop_type'][~ex] = -1\n", "    return result, out_dict['stop_price'], out_dict['stop_type']\n", "\n", "ex, stop_price, stop_type = test_ohlc_stop_exits()\n", "print(ex)\n", "print(stop_price)\n", "print(stop_type)\n", "\n", "ex, stop_price, stop_type = test_ohlc_stop_exits(sl_stop=0.1)\n", "print(ex)\n", "print(stop_price)\n", "print(stop_type)\n", "\n", "ex, stop_price, stop_type = test_ohlc_stop_exits(sl_stop=0.1, sl_trail=True)\n", "print(ex)\n", "print(stop_price)\n", "print(stop_type)\n", "\n", "ex, stop_price, stop_type = test_ohlc_stop_exits(tp_stop=0.1)\n", "print(ex)\n", "print(stop_price)\n", "print(stop_type)\n", "\n", "ex, stop_price, stop_type = test_ohlc_stop_exits(sl_stop=0.1, sl_trail=True, tp_stop=0.1)\n", "print(ex)\n", "print(stop_price)\n", "print(stop_type)\n", "\n", "ex, stop_price, stop_type = test_ohlc_stop_exits(\n", "    sl_stop=[np.nan, 0.5, 1.], sl_trail=True, tp_stop=[np.nan, 0.5, 1.])\n", "print(ex)\n", "print(stop_price)\n", "print(stop_type)\n", "\n", "ex, stop_price, stop_type = test_ohlc_stop_exits(sl_stop=0.1, sl_trail=True, tp_stop=0.1, exit_wait=0)\n", "print(ex)\n", "print(stop_price)\n", "print(stop_type)\n", "\n", "%timeit big_entries.vbt.signals.generate_ohlc_stop_exits(\\\n", "    big_ts, big_ts + 1, big_ts - 1, big_ts, sl_stop=0.1, sl_trail=True, tp_stop=0.1)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                a      b      c\n", "2018-01-01   <PERSON>   True   True\n", "2018-01-02  False  False  False\n", "2018-01-03  False   True   True\n", "2018-01-04  False  False  False\n", "2018-01-05  False   True  False\n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02   <PERSON>   True   True\n", "2018-01-03  False  False  False\n", "2018-01-04  <PERSON><PERSON>e   True   True\n", "2018-01-05  False  False  False\n", "               a     b     c\n", "2018-01-01   NaN   NaN   NaN\n", "2018-01-02  11.0  11.0  11.0\n", "2018-01-03   NaN   NaN   NaN\n", "2018-01-04   NaN  10.8  10.8\n", "2018-01-05   NaN   NaN   NaN\n", "            a  b  c\n", "2018-01-01 -1 -1 -1\n", "2018-01-02  2  2  2\n", "2018-01-03 -1 -1 -1\n", "2018-01-04 -1  1  1\n", "2018-01-05 -1 -1 -1\n", "33.5 ms ± 800 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["(en, ex), stop_price, stop_type = test_ohlc_stop_exits(sl_stop=0.1, sl_trail=True, tp_stop=0.1, chain=True)\n", "print(en)\n", "print(ex)\n", "print(stop_price)\n", "print(stop_type)\n", "\n", "%timeit big_entries.vbt.signals.generate_ohlc_stop_exits(\\\n", "    big_ts, big_ts + 1, big_ts - 1, big_ts, sl_stop=0.1, sl_trail=True, tp_stop=0.1, chain=True)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a    NaN\n", "b    2.0\n", "c    1.0\n", "Name: map_reduce_between, dtype: float64\n", "1.79 ms ± 14.7 µs per loop (mean ± std. dev. of 7 runs, 1000 loops each)\n"]}], "source": ["distance_map_nb = njit(lambda prev_i, next_i, col: next_i - prev_i)\n", "avg_reduce_nb = njit(lambda col, a: np.nanmean(a))\n", "\n", "print(entries.vbt.signals.map_reduce_between(\n", "    range_map_func_nb=distance_map_nb, reduce_func_nb=avg_reduce_nb))\n", "\n", "%timeit big_entries.vbt.signals.map_reduce_between(\\\n", "    range_map_func_nb=distance_map_nb, reduce_func_nb=avg_reduce_nb)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a    1.000000\n", "b    1.000000\n", "c    0.333333\n", "Name: map_reduce_between_two, dtype: float64\n", "43.9 ms ± 421 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(entries.vbt.signals.map_reduce_between(\n", "    other=entries.vbt.signals.fshift(1), \n", "    range_map_func_nb=distance_map_nb, \n", "    reduce_func_nb=avg_reduce_nb))\n", "\n", "%timeit big_entries.vbt.signals.map_reduce_between(\\\n", "    other=big_entries.vbt.signals.fshift(1),\\\n", "    range_map_func_nb=distance_map_nb,\\\n", "    reduce_func_nb=avg_reduce_nb)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a    1.0\n", "b    1.0\n", "c    3.0\n", "Name: map_reduce_partitions, dtype: float64\n", "1.31 ms ± 14.1 µs per loop (mean ± std. dev. of 7 runs, 1000 loops each)\n"]}], "source": ["print(entries.vbt.signals.map_reduce_partitions(\n", "    range_map_func_nb=distance_map_nb, reduce_func_nb=avg_reduce_nb))\n", "\n", "%timeit big_entries.vbt.signals.map_reduce_partitions(\\\n", "    range_map_func_nb=distance_map_nb, reduce_func_nb=avg_reduce_nb)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "a    1\n", "b    3\n", "c    3\n", "Name: total, dtype: int64\n", "693 µs ± 527 ns per loop (mean ± std. dev. of 7 runs, 1000 loops each)\n"]}], "source": ["print(entries['a'].vbt.signals.total())\n", "print(entries.vbt.signals.total())\n", "\n", "%timeit big_entries.vbt.signals.total()"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["nan\n", "a    NaN\n", "b    2.0\n", "c    1.0\n", "Name: avg_distance, dtype: float64\n", "1.8 ms ± 22.3 µs per loop (mean ± std. dev. of 7 runs, 1000 loops each)\n"]}], "source": ["print(entries['a'].vbt.signals.avg_distance())\n", "print(entries.vbt.signals.avg_distance())\n", "\n", "%timeit big_entries.vbt.signals.avg_distance()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0\n", "a    1.000000\n", "b    1.000000\n", "c    0.333333\n", "Name: avg_distance, dtype: float64\n", "42.8 ms ± 72.2 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(entries['a'].vbt.signals.avg_distance(to=entries['a'].vbt.signals.fshift(1)))\n", "print(entries.vbt.signals.avg_distance(to=entries.vbt.signals.fshift(1)))\n", "\n", "%timeit big_entries.vbt.signals.avg_distance(to=big_entries.vbt.signals.fshift(1))"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01    0\n", "2018-01-02   -1\n", "2018-01-03   -1\n", "2018-01-04   -1\n", "2018-01-05   -1\n", "Name: a, dtype: int64\n", "            a  b  c\n", "2018-01-01  0  0  0\n", "2018-01-02 -1 -1  1\n", "2018-01-03 -1  0  2\n", "2018-01-04 -1 -1 -1\n", "2018-01-05 -1  0 -1\n", "2018-01-01   -1\n", "2018-01-02   -1\n", "2018-01-03   -1\n", "2018-01-04   -1\n", "2018-01-05   -1\n", "Name: a, dtype: int64\n", "            a  b  c\n", "2018-01-01 -1 -1 -1\n", "2018-01-02 -1 -1 -1\n", "2018-01-03 -1  0 -1\n", "2018-01-04 -1 -1 -1\n", "2018-01-05 -1  0 -1\n", "2018-01-01    0\n", "2018-01-02   -1\n", "2018-01-03   -1\n", "2018-01-04   -1\n", "2018-01-05   -1\n", "Name: a, dtype: int64\n", "            a  b  c\n", "2018-01-01  0  0  0\n", "2018-01-02 -1 -1  1\n", "2018-01-03 -1  1  2\n", "2018-01-04 -1 -1 -1\n", "2018-01-05 -1  2 -1\n", "2018-01-01    0\n", "2018-01-02   -1\n", "2018-01-03   -1\n", "2018-01-04   -1\n", "2018-01-05   -1\n", "Name: a, dtype: int64\n", "            a  b  c\n", "2018-01-01  0  0  0\n", "2018-01-02 -1 -1  1\n", "2018-01-03 -1  0  2\n", "2018-01-04 -1 -1 -1\n", "2018-01-05 -1  0 -1\n", "2.9 ms ± 56.3 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["print(entries['a'].vbt.signals.pos_rank())\n", "print(entries.vbt.signals.pos_rank())\n", "\n", "print(entries['a'].vbt.signals.pos_rank(after_false=True))\n", "print(entries.vbt.signals.pos_rank(after_false=True))\n", "\n", "print(entries['a'].vbt.signals.pos_rank(allow_gaps=True))\n", "print(entries.vbt.signals.pos_rank(allow_gaps=True))\n", "\n", "print(entries['a'].vbt.signals.pos_rank(allow_gaps=True, reset_by=~entries['a']))\n", "print(entries.vbt.signals.pos_rank(allow_gaps=True, reset_by=~entries))\n", "\n", "%timeit big_entries.vbt.signals.pos_rank()"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01    0\n", "2018-01-02   -1\n", "2018-01-03   -1\n", "2018-01-04   -1\n", "2018-01-05   -1\n", "Name: a, dtype: int64\n", "            a  b  c\n", "2018-01-01  0  0  0\n", "2018-01-02 -1 -1  0\n", "2018-01-03 -1  1  0\n", "2018-01-04 -1 -1 -1\n", "2018-01-05 -1  2 -1\n", "2018-01-01   -1\n", "2018-01-02   -1\n", "2018-01-03   -1\n", "2018-01-04   -1\n", "2018-01-05   -1\n", "Name: a, dtype: int64\n", "            a  b  c\n", "2018-01-01 -1 -1 -1\n", "2018-01-02 -1 -1 -1\n", "2018-01-03 -1  0 -1\n", "2018-01-04 -1 -1 -1\n", "2018-01-05 -1  1 -1\n", "2018-01-01    0\n", "2018-01-02   -1\n", "2018-01-03   -1\n", "2018-01-04   -1\n", "2018-01-05   -1\n", "Name: a, dtype: int64\n", "            a  b  c\n", "2018-01-01  0  0  0\n", "2018-01-02 -1 -1  0\n", "2018-01-03 -1  0  0\n", "2018-01-04 -1 -1 -1\n", "2018-01-05 -1  0 -1\n", "2.83 ms ± 37.2 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["print(entries['a'].vbt.signals.partition_pos_rank())\n", "print(entries.vbt.signals.partition_pos_rank())\n", "\n", "print(entries['a'].vbt.signals.partition_pos_rank(after_false=True))\n", "print(entries.vbt.signals.partition_pos_rank(after_false=True))\n", "\n", "print(entries['a'].vbt.signals.partition_pos_rank(reset_by=~entries['a']))\n", "print(entries.vbt.signals.partition_pos_rank(reset_by=~entries))\n", "\n", "%timeit big_entries.vbt.signals.partition_pos_rank()"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                a      b      c\n", "2018-01-01   <PERSON>   True   True\n", "2018-01-02  False  False  False\n", "2018-01-03  False   True  False\n", "2018-01-04  False  False  False\n", "2018-01-05  False   True  False\n", "3.3 ms ± 81.6 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["print(entries.vbt.signals.first())\n", "\n", "%timeit big_entries.vbt.signals.first()"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                a      b      c\n", "2018-01-01   <PERSON>   True   True\n", "2018-01-02  False  False   True\n", "2018-01-03  False   True   True\n", "2018-01-04  False  False  False\n", "2018-01-05  False   True  False\n", "160 µs ± 3.78 µs per loop (mean ± std. dev. of 7 runs, 10000 loops each)\n", "                a      b      c\n", "2018-01-01   <PERSON>   True   True\n", "2018-01-02  False  False   True\n", "2018-01-03  False   True   True\n", "2018-01-04  False  False  False\n", "2018-01-05  False   True  False\n", "1.33 ms ± 182 µs per loop (mean ± std. dev. of 7 runs, 1000 loops each)\n"]}], "source": ["print(entries & entries)\n", "%timeit big_entries & big_entries\n", "\n", "print(entries.vbt.signals.AND(entries))\n", "%timeit big_entries.vbt.signals.AND(big_entries) # a bit slower but does smart broadcasting"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                a     b      c\n", "2018-01-01   <PERSON>  True   True\n", "2018-01-02   <PERSON>  True   True\n", "2018-01-03   <PERSON>  True   True\n", "2018-01-04   <PERSON>  True   True\n", "2018-01-05  False  True  False\n", "               >1                  >2                   >3              \n", "                a     b      c      a      b      c      a      b      c\n", "2018-01-01   True  True   True   True   True   True   True   True   True\n", "2018-01-02   True  True   True  False  False   True  False  False   True\n", "2018-01-03   True  True   True   True   True   True  False   True   True\n", "2018-01-04   True  True   True  False  False  False  False  False  False\n", "2018-01-05  False  True  False  False   True  False  False   True  False\n"]}], "source": ["print(entries.vbt.signals.OR([ts > 1, ts > 2, ts > 3])) # you can pass multiple arguments\n", "print(entries.vbt.signals.OR([ts > 1, ts > 2, ts > 3], concat=True, keys=['>1', '>2', '>3']))"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Start                       2018-01-01 00:00:00\n", "End                         2018-01-05 00:00:00\n", "Period                          5 days 00:00:00\n", "Total                                         1\n", "Rate [%]                                   20.0\n", "First Index                 2018-01-01 00:00:00\n", "Last Index                  2018-01-01 00:00:00\n", "Norm Avg Index [-1, 1]                     -1.0\n", "Distance: Min                               NaT\n", "Distance: Max                               NaT\n", "Distance: Mean                              NaT\n", "Distance: Std                               NaT\n", "Total Partitions                              1\n", "Partition Rate [%]                        100.0\n", "Partition Length: Min           1 days 00:00:00\n", "Partition Length: Max           1 days 00:00:00\n", "Partition Length: Mean          1 days 00:00:00\n", "Partition Length: Std                       NaT\n", "Partition Distance: Min                     NaT\n", "Partition Distance: Max                     NaT\n", "Partition Distance: Mean                    NaT\n", "Partition Distance: Std                     NaT\n", "Name: a, dtype: object\n", "9.75 ms ± 142 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n", "Start                       2018-01-01 00:00:00\n", "End                         2018-01-05 00:00:00\n", "Period                          5 days 00:00:00\n", "Total                                         1\n", "Rate [%]                                   20.0\n", "First Index                 2018-01-01 00:00:00\n", "Last Index                  2018-01-01 00:00:00\n", "Norm Avg Index [-1, 1]                     -1.0\n", "Distance: Min                               NaT\n", "Distance: Max                               NaT\n", "Distance: Mean                              NaT\n", "Distance: Std                               NaT\n", "Total Partitions                              1\n", "Partition Rate [%]                        100.0\n", "Partition Length: Min           1 days 00:00:00\n", "Partition Length: Max           1 days 00:00:00\n", "Partition Length: Mean          1 days 00:00:00\n", "Partition Length: Std                       NaT\n", "Partition Distance: Min                     NaT\n", "Partition Distance: Max                     NaT\n", "Partition Distance: Mean                    NaT\n", "Partition Distance: Std                     NaT\n", "Name: a, dtype: object\n", "30.5 ms ± 982 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "Start                       2018-01-01 00:00:00\n", "End                         2018-01-05 00:00:00\n", "Period                          5 days 00:00:00\n", "Total                                  2.333333\n", "Rate [%]                              46.666667\n", "First Index                 2018-01-01 00:00:00\n", "Last Index                  2018-01-03 00:00:00\n", "Norm Avg Index [-1, 1]                     -0.5\n", "Distance: Min                   1 days 12:00:00\n", "Distance: Max                   1 days 12:00:00\n", "Distance: Mean                  1 days 12:00:00\n", "Distance: Std                   0 days 00:00:00\n", "Total Partitions                       1.666667\n", "Partition Rate [%]                    77.777778\n", "Partition Length: Min           1 days 16:00:00\n", "Partition Length: Max           1 days 16:00:00\n", "Partition Length: Mean          1 days 16:00:00\n", "Partition Length: Std           0 days 00:00:00\n", "Partition Distance: Min         2 days 00:00:00\n", "Partition Distance: Max         2 days 00:00:00\n", "Partition Distance: Mean        2 days 00:00:00\n", "Partition Distance: Std         0 days 00:00:00\n", "Name: agg_func_mean, dtype: object\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.7/site-packages/ipykernel_launcher.py:7: UserWarning: Object has multiple columns. Aggregating using <function mean at 0x7fe8580767b8>. Pass column to select a single column/group.\n", "  import sys\n"]}, {"name": "stdout", "output_type": "stream", "text": ["30.4 ms ± 120 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "Start                       2018-01-01 00:00:00\n", "End                         2018-01-05 00:00:00\n", "Period                          5 days 00:00:00\n", "Total                                  2.333333\n", "Rate [%]                              46.666667\n", "Total Overlapping                           0.0\n", "Overlapping Rate [%]                        0.0\n", "First Index                 2018-01-01 00:00:00\n", "Last Index                  2018-01-03 00:00:00\n", "Norm Avg Index [-1, 1]                     -0.5\n", "Distance -> Other: Min          1 days 00:00:00\n", "Distance -> Other: Max          1 days 16:00:00\n", "Distance -> Other: Mean         1 days 08:00:00\n", "Distance -> Other: Std          0 days 12:00:00\n", "Total Partitions                       1.666667\n", "Partition Rate [%]                    77.777778\n", "Partition Length: Min           1 days 16:00:00\n", "Partition Length: Max           1 days 16:00:00\n", "Partition Length: Mean          1 days 16:00:00\n", "Partition Length: Std           0 days 00:00:00\n", "Partition Distance: Min         2 days 00:00:00\n", "Partition Distance: Max         2 days 00:00:00\n", "Partition Distance: Mean        2 days 00:00:00\n", "Partition Distance: Std         0 days 00:00:00\n", "Name: agg_func_mean, dtype: object\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.7/site-packages/ipykernel_launcher.py:10: UserWarning: Object has multiple columns. Aggregating using <function mean at 0x7fe8580767b8>. Pass column to select a single column/group.\n", "  # Remove the CWD from sys.path while we load stuff.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["160 ms ± 1.69 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(entries['a'].vbt.signals.stats())\n", "%timeit big_entries[0].vbt.signals.stats(silence_warnings=True)\n", "\n", "print(entries.vbt.signals.stats(column='a'))\n", "%timeit big_entries.vbt.signals.stats(column=0, silence_warnings=True)\n", "\n", "print(entries.vbt.signals.stats())\n", "%timeit big_entries.vbt.signals.stats(silence_warnings=True)\n", "                                      \n", "print(entries.vbt.signals.stats(settings=dict(other=~entries)))\n", "%timeit big_entries.vbt.signals.stats(settings=dict(other=~big_entries), silence_warnings=True)"]}, {"cell_type": "code", "execution_count": 57, "metadata": {"Collapsed": "false"}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-8b294a\"><g class=\"clips\"><clipPath id=\"clip8b294axyplot\" class=\"plotclip\"><rect width=\"604\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip8b294ax\"><rect x=\"48\" y=\"0\" width=\"604\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip8b294ay\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip8b294axy\"><rect x=\"48\" y=\"46\" width=\"604\" height=\"261\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"48\" y=\"46\" width=\"604\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(123.5,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(199,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(274.5,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(350,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(425.5,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(501,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(576.5,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,293.95)\" d=\"M48,0h604\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,59.05)\" d=\"M48,0h604\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(48,46)\" clip-path=\"url('#clip8b294axyplot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace855de4e9-581b-475f-8ee7-05a6a253b342\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,247.95L151,13.05L302,13.05L604,13.05\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter tracec7eeb192-9b91-40d6-8cf1-f2f2c8cbbe82\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,247.95L151,13.05L302,247.95L453,13.05L604,247.95\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter traced8e07810-4e71-4fe2-9e75-68c8a074ab1a\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,247.95L302,247.95L453,13.05L604,13.05\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(48,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Jan 1, 2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(123.5,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">12:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(199,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Jan 2, 2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(274.5,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">12:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(350,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Jan 3, 2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(425.5,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">12:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(501,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Jan 4, 2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(576.5,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">12:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(652,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Jan 5, 2018</tspan></text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"47\" y=\"4.199999999999999\" transform=\"translate(0,293.95)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">false</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"47\" y=\"4.199999999999999\" transform=\"translate(0,59.05)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">true</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-8b294a\"><g class=\"clips\"/><clipPath id=\"legend8b294a\"><rect width=\"156\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(496,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"156\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend8b294a')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">a</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"49.71875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(52.21875,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">b</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"49.984375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(104.703125,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">c</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"48.765625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["entries.vbt.signals.plot().show_svg()"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-49021f\"><g class=\"clips\"><clipPath id=\"clip49021fxyplot\" class=\"plotclip\"><rect width=\"604\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip49021fx\"><rect x=\"48\" y=\"0\" width=\"604\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip49021fy\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip49021fxy\"><rect x=\"48\" y=\"46\" width=\"604\" height=\"261\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"48\" y=\"46\" width=\"604\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(123.5,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(199,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(274.5,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(350,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(425.5,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(501,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(576.5,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,293.95)\" d=\"M48,0h604\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,59.05)\" d=\"M48,0h604\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(48,46)\" clip-path=\"url('#clip49021fxyplot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter traceaa4388c2-1887-4b0e-b303-5320da852488\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,247.95L151,13.05L302,13.05L604,13.05\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(48,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Jan 1, 2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(123.5,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">12:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(199,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Jan 2, 2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(274.5,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">12:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(350,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Jan 3, 2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(425.5,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">12:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(501,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Jan 4, 2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(576.5,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">12:00</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(652,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Jan 5, 2018</tspan></text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"47\" y=\"4.199999999999999\" transform=\"translate(0,293.95)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">false</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"47\" y=\"4.199999999999999\" transform=\"translate(0,59.05)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">true</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-49021f\"><g class=\"clips\"/><clipPath id=\"legend49021f\"><rect width=\"53\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(599,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"53\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend49021f')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">a</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"49.71875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["entries['a'].vbt.signals.plot().show_svg()"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-f5661b\"><g class=\"clips\"><clipPath id=\"clipf5661bxyplot\" class=\"plotclip\"><rect width=\"592\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipf5661bx\"><rect x=\"54\" y=\"0\" width=\"592\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clipf5661by\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipf5661bxy\"><rect x=\"54\" y=\"46\" width=\"592\" height=\"261\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"54\" y=\"46\" width=\"592\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(202,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(350,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(498,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,241.75)\" d=\"M54,0h592\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,176.5)\" d=\"M54,0h592\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,111.25)\" d=\"M54,0h592\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,307)\" d=\"M54,0h592\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(54,46)\" clip-path=\"url('#clipf5661bxyplot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace86f34e72-58c5-40c1-aa34-0b98033895ee\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(296,130.5)\" d=\"M3.5,0A3.5,3.5 0 1,1 0,-3.5A3.5,3.5 0 0,1 3.5,0Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;\"/></g><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(54,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">23:59:59.999</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Dec 31, 2017</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(202,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">23:59:59.9995</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(350,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Jan 1, 2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(498,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">00:00:00.0005</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(646,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">00:00:00.001</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,307)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,241.75)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,111.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,46)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-f5661b\"><g class=\"clips\"/><clipPath id=\"legendf5661b\"><rect width=\"53\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(593,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"53\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legendf5661b')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">a</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3.5,0A3.5,3.5 0 1,1 0,-3.5A3.5,3.5 0 0,1 3.5,0Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"49.71875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["entries['a'].vbt.signals.plot_as_markers(ts).show_svg()"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-b02e8c\"><g class=\"clips\"><clipPath id=\"clipb02e8cxyplot\" class=\"plotclip\"><rect width=\"592\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb02e8cx\"><rect x=\"54\" y=\"0\" width=\"592\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb02e8cy\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb02e8cxy\"><rect x=\"54\" y=\"46\" width=\"592\" height=\"261\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"54\" y=\"46\" width=\"592\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(202,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(350,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(498,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,241.75)\" d=\"M54,0h592\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,176.5)\" d=\"M54,0h592\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,111.25)\" d=\"M54,0h592\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,307)\" d=\"M54,0h592\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(54,46)\" clip-path=\"url('#clipb02e8cxyplot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace66448e9b-65b8-4010-841e-03b0d8dc48a1\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(296,130.5)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(54,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">23:59:59.999</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Dec 31, 2017</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(202,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">23:59:59.9995</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(350,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Jan 1, 2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(498,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">00:00:00.0005</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(646,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">00:00:00.001</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,307)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,241.75)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,111.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,46)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-b02e8c\"><g class=\"clips\"/><clipPath id=\"legendb02e8c\"><rect width=\"78\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(568,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"78\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legendb02e8c')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Entry</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"74.640625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["entries['a'].vbt.signals.plot_as_entry_markers(ts).show_svg()"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-795397\"><g class=\"clips\"><clipPath id=\"clip795397xyplot\" class=\"plotclip\"><rect width=\"592\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip795397x\"><rect x=\"54\" y=\"0\" width=\"592\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip795397y\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip795397xy\"><rect x=\"54\" y=\"46\" width=\"592\" height=\"261\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"54\" y=\"46\" width=\"592\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(202,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(350,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(498,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,241.75)\" d=\"M54,0h592\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,176.5)\" d=\"M54,0h592\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,111.25)\" d=\"M54,0h592\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,307)\" d=\"M54,0h592\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(54,46)\" clip-path=\"url('#clip795397xyplot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace3cd81aa3-4a72-4107-9174-68fb4c821fab\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(296,130.5)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(54,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">23:59:59.999</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Dec 31, 2017</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(202,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">23:59:59.9995</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(350,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">00:00:00</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">Jan 1, 2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(498,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">00:00:00.0005</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(646,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">00:00:00.001</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,307)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,241.75)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,111.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"53\" y=\"4.199999999999999\" transform=\"translate(0,46)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-795397\"><g class=\"clips\"/><clipPath id=\"legend795397\"><rect width=\"68\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(578,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"68\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend795397')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Exit</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"65.21875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["entries['a'].vbt.signals.plot_as_exit_markers(ts).show_svg()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## factory"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["custom_n1      0      1\n", "custom_n2      1      0\n", "0           True   True\n", "1          False  False\n", "2           True   True\n", "3          False  False\n", "4           True   True\n", "custom_n1      0      1\n", "custom_n2      1      0\n", "0          False  False\n", "1           True   True\n", "2          False  False\n", "3           True   True\n", "4          False  False\n", "custom_n1       0       1\n", "custom_n2       1       0\n", "0          1100.0  1100.0\n", "1             NaN     NaN\n", "2          1100.0  1102.0\n", "3             NaN     NaN\n", "4          1100.0  1104.0\n", "custom_n1       0       1\n", "custom_n2       1       0\n", "0             NaN     NaN\n", "1          1101.0  1100.0\n", "2             NaN     NaN\n", "3          1103.0  1100.0\n", "4             NaN     NaN\n"]}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-6268d8\"><g class=\"clips\"><clipPath id=\"clip6268d8xyplot\" class=\"plotclip\"><rect width=\"637\" height=\"274\"/></clipPath><clipPath class=\"axesclip\" id=\"clip6268d8x\"><rect x=\"33\" y=\"0\" width=\"637\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip6268d8y\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"274\"/></clipPath><clipPath class=\"axesclip\" id=\"clip6268d8xy\"><rect x=\"33\" y=\"46\" width=\"637\" height=\"274\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"33\" y=\"46\" width=\"637\" height=\"274\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(210.67,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(351.5,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(492.32,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(633.15,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,251.5)\" d=\"M33,0h637\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,183)\" d=\"M33,0h637\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,114.5)\" d=\"M33,0h637\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(69.85,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/><path class=\"yzl zl crisp\" transform=\"translate(0,320)\" d=\"M33,0h637\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(33,46)\" clip-path=\"url('#clip6268d8xyplot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter tracefb29b710-84cf-40c8-b609-6cfbf17ac5ed\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(36.85,137)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(318.5,137)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(600.15,137)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g><g class=\"text\"/></g><g class=\"trace scatter trace8085ed11-c912-4b66-ac49-7ec2023ece6e\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(177.67,137)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(459.32,137)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(69.85,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(210.67,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(351.5,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(492.32,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(633.15,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">4</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,320)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,251.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,183)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,114.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,46)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-6268d8\"><g class=\"clips\"/><clipPath id=\"legend6268d8\"><rect width=\"145\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(525,11.519999999999996)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"145\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend6268d8')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Entry</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"74.640625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(77.140625,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Exit</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"65.21875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["@njit\n", "def choice_nb(from_i, to_i, col, ts, in_out, n, arg, temp_idx_arr, kw):\n", "    in_out[from_i, col] = ts[from_i, col] * n + arg + kw\n", "    temp_idx_arr[0] = from_i\n", "    return temp_idx_arr[:1]\n", "\n", "MySignals = vbt.SignalFactory(\n", "    input_names=['ts1', 'ts2'],\n", "    in_output_names=['in_out1', 'in_out2'],\n", "    param_names=['n1', 'n2']\n", ").from_choice_func(\n", "    entry_choice_func=choice_nb,\n", "    entry_settings=dict(\n", "        pass_inputs=['ts1'],\n", "        pass_in_outputs=['in_out1'],\n", "        pass_params=['n1'],\n", "        pass_kwargs=['temp_idx_arr1', ('kw1', 1000)]\n", "    ),\n", "    exit_choice_func=choice_nb,\n", "    exit_settings=dict(\n", "        pass_inputs=['ts2'],\n", "        pass_in_outputs=['in_out2'],\n", "        pass_params=['n2'],\n", "        pass_kwargs=['temp_idx_arr2', ('kw2', 1000)]\n", "    ),\n", "    in_output_settings=dict(\n", "        in_out1=dict(\n", "            dtype=np.float64\n", "        ),\n", "        in_out2=dict(\n", "            dtype=np.float64\n", "        )\n", "    ),\n", "    in_out1=np.nan,\n", "    in_out2=np.nan,\n", "    var_args=True,\n", "    require_input_shape=False\n", ")\n", "my_sig = MySignals.run(np.arange(5), np.arange(5), [0, 1], [1, 0], entry_args=(100,), exit_args=(100,))\n", "print(my_sig.entries)\n", "print(my_sig.exits)\n", "print(my_sig.in_out1)\n", "print(my_sig.in_out2)\n", "my_sig[(0, 1)].plot().show_svg()"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["custom_n1      0      1\n", "custom_n2      1      0\n", "0           True   True\n", "1          False  False\n", "2          False  False\n", "3          False  False\n", "4           True   True\n", "5          False  False\n", "6          False  False\n", "custom_n1      0      1\n", "custom_n2      1      0\n", "0          False  False\n", "1          False  False\n", "2           True   True\n", "3          False  False\n", "4          False  False\n", "5          False  False\n", "6           True   True\n", "custom_n1       0       1\n", "custom_n2       1       0\n", "0          1100.0  1100.0\n", "1             NaN     NaN\n", "2             NaN     NaN\n", "3             NaN     NaN\n", "4          1100.0  1104.0\n", "5             NaN     NaN\n", "6             NaN     NaN\n", "custom_n1       0       1\n", "custom_n2       1       0\n", "0             NaN     NaN\n", "1             NaN     NaN\n", "2          1102.0  1100.0\n", "3             NaN     NaN\n", "4             NaN     NaN\n", "5             NaN     NaN\n", "6          1106.0  1100.0\n"]}], "source": ["my_sig = MySignals.run(\n", "    np.arange(7), np.arange(7), [0, 1], [1, 0], \n", "    entry_args=(100,), exit_args=(100,), \n", "    entry_kwargs=dict(wait=2), exit_kwargs=dict(wait=2)\n", ")\n", "print(my_sig.entries)\n", "print(my_sig.exits)\n", "print(my_sig.in_out1)\n", "print(my_sig.in_out2)"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["custom_n2      1      0\n", "0           True   True\n", "1          False  False\n", "2           True   True\n", "3          False  False\n", "4           True   True\n", "custom_n2      1      0\n", "0          False  False\n", "1           True   True\n", "2          False  False\n", "3           True   True\n", "4          False  False\n", "custom_n2       1       0\n", "0             NaN     NaN\n", "1          1101.0  1100.0\n", "2             NaN     NaN\n", "3          1103.0  1100.0\n", "4             NaN     NaN\n"]}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-df3bd2\"><g class=\"clips\"><clipPath id=\"clipdf3bd2xyplot\" class=\"plotclip\"><rect width=\"637\" height=\"274\"/></clipPath><clipPath class=\"axesclip\" id=\"clipdf3bd2x\"><rect x=\"33\" y=\"0\" width=\"637\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clipdf3bd2y\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"274\"/></clipPath><clipPath class=\"axesclip\" id=\"clipdf3bd2xy\"><rect x=\"33\" y=\"46\" width=\"637\" height=\"274\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"33\" y=\"46\" width=\"637\" height=\"274\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(210.67,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(351.5,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(492.32,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(633.15,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,251.5)\" d=\"M33,0h637\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,183)\" d=\"M33,0h637\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,114.5)\" d=\"M33,0h637\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(69.85,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/><path class=\"yzl zl crisp\" transform=\"translate(0,320)\" d=\"M33,0h637\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(33,46)\" clip-path=\"url('#clipdf3bd2xyplot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter traced9156be5-dfc4-4c56-89ac-3c5275f3b84e\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(36.85,137)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(318.5,137)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(600.15,137)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g><g class=\"text\"/></g><g class=\"trace scatter trace25499b8e-8d34-4b65-b643-cdca046a9834\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(177.67,137)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(459.32,137)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(69.85,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(210.67,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(351.5,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(492.32,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(633.15,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">4</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,320)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,251.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,183)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,114.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,46)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-df3bd2\"><g class=\"clips\"/><clipPath id=\"legenddf3bd2\"><rect width=\"145\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(525,11.519999999999996)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"145\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legenddf3bd2')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Entry</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"74.640625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(77.140625,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Exit</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"65.21875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["MySignals = vbt.SignalFactory(\n", "    input_names=['ts2'],\n", "    in_output_names=['in_out2'],\n", "    param_names=['n2'],\n", "    mode='exits'\n", ").from_choice_func(\n", "    exit_choice_func=choice_nb,\n", "    exit_settings=dict(\n", "        pass_inputs=['ts2'],\n", "        pass_in_outputs=['in_out2'],\n", "        pass_params=['n2'],\n", "        pass_kwargs=['temp_idx_arr2', ('kw2', 1000)]\n", "    ),\n", "    in_output_settings=dict(\n", "        in_out2=dict(\n", "            dtype=np.float64\n", "        )\n", "    ),\n", "    in_out2=np.nan,\n", "    var_args=True\n", ")\n", "e = np.array([True, False, True, False, True])\n", "my_sig = MySignals.run(e, np.arange(5), [1, 0], 100)\n", "print(my_sig.entries)\n", "print(my_sig.exits)\n", "print(my_sig.in_out2)\n", "my_sig[0].plot().show_svg()"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["custom_n2      1      0\n", "0           True   True\n", "1          False  False\n", "2          False  False\n", "3           True   True\n", "4          False  False\n", "5          False  False\n", "custom_n2      1      0\n", "0          False  False\n", "1          False  False\n", "2           True   True\n", "3          False  False\n", "4          False  False\n", "5           True   True\n", "custom_n2       1       0\n", "0             NaN     NaN\n", "1             NaN     NaN\n", "2          1102.0  1100.0\n", "3             NaN     NaN\n", "4             NaN     NaN\n", "5          1105.0  1100.0\n"]}], "source": ["e = np.array([True, False, False, True, False, False])\n", "my_sig = MySignals.run(e, np.arange(6), [1, 0], 100, wait=2)\n", "print(my_sig.entries)\n", "print(my_sig.exits)\n", "print(my_sig.in_out2)"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["custom_n2     1     0\n", "0          True  True\n", "1          True  True\n", "2          True  True\n", "3          True  True\n", "4          True  True\n", "custom_n2      1      0\n", "0           True   True\n", "1          False  False\n", "2           True   True\n", "3          False  False\n", "4           True   True\n", "custom_n2      1      0\n", "0          False  False\n", "1           True   True\n", "2          False  False\n", "3           True   True\n", "4          False  False\n", "custom_n2       1       0\n", "0             NaN     NaN\n", "1          1101.0  1100.0\n", "2             NaN     NaN\n", "3          1103.0  1100.0\n", "4             NaN     NaN\n"]}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-6c75cf\"><g class=\"clips\"><clipPath id=\"clip6c75cfxyplot\" class=\"plotclip\"><rect width=\"637\" height=\"274\"/></clipPath><clipPath class=\"axesclip\" id=\"clip6c75cfx\"><rect x=\"33\" y=\"0\" width=\"637\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip6c75cfy\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"274\"/></clipPath><clipPath class=\"axesclip\" id=\"clip6c75cfxy\"><rect x=\"33\" y=\"46\" width=\"637\" height=\"274\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"33\" y=\"46\" width=\"637\" height=\"274\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(210.67,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(351.5,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(492.32,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(633.15,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,251.5)\" d=\"M33,0h637\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,183)\" d=\"M33,0h637\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,114.5)\" d=\"M33,0h637\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(69.85,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/><path class=\"yzl zl crisp\" transform=\"translate(0,320)\" d=\"M33,0h637\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(33,46)\" clip-path=\"url('#clip6c75cfxyplot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter tracebdcaeea7-fb3f-420e-a700-673a81ca3045\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(36.85,137)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(318.5,137)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(600.15,137)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g><g class=\"text\"/></g><g class=\"trace scatter trace6563a128-f554-4979-86a9-e1c8fa812ddc\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(177.67,137)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(459.32,137)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(69.85,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(210.67,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(351.5,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(492.32,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(633.15,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">4</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,320)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,251.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,183)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,114.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"32\" y=\"4.199999999999999\" transform=\"translate(0,46)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-6c75cf\"><g class=\"clips\"/><clipPath id=\"legend6c75cf\"><rect width=\"176\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(494,11.519999999999996)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"176\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend6c75cf')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">New Entry</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"104.796875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(107.296875,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Exit</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"65.21875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["MySignals = vbt.SignalFactory(\n", "    input_names=['ts2'],\n", "    in_output_names=['in_out2'],\n", "    param_names=['n2'],\n", "    mode='chain'\n", ").from_choice_func(\n", "    exit_choice_func=choice_nb,\n", "    exit_settings=dict(\n", "        pass_inputs=['ts2'],\n", "        pass_in_outputs=['in_out2'],\n", "        pass_params=['n2'],\n", "        pass_kwargs=['temp_idx_arr2', ('kw2', 1000)]\n", "    ),\n", "    in_output_settings=dict(\n", "        in_out2=dict(\n", "            dtype=np.float64\n", "        )\n", "    ),\n", "    in_out2=np.nan, \n", "    var_args=True\n", ")\n", "e = np.array([True, True, True, True, True])\n", "my_sig = MySignals.run(e, np.arange(5), [1, 0], 100)\n", "print(my_sig.entries)\n", "print(my_sig.new_entries)\n", "print(my_sig.exits)\n", "print(my_sig.in_out2)\n", "my_sig[0].plot().show_svg()"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["custom_n2     1     0\n", "0          True  True\n", "1          True  True\n", "2          True  True\n", "3          True  True\n", "4          True  True\n", "5          True  True\n", "custom_n2      1      0\n", "0           True   True\n", "1          False  False\n", "2          False  False\n", "3           True   True\n", "4          False  False\n", "5          False  False\n", "custom_n2      1      0\n", "0          False  False\n", "1          False  False\n", "2           True   True\n", "3          False  False\n", "4          False  False\n", "5           True   True\n", "custom_n2       1       0\n", "0             NaN     NaN\n", "1             NaN     NaN\n", "2          1102.0  1100.0\n", "3             NaN     NaN\n", "4             NaN     NaN\n", "5          1105.0  1100.0\n"]}], "source": ["e = np.array([True, True, True, True, True, True])\n", "my_sig = MySignals.run(e, np.arange(6), [1, 0], 100, wait=2)\n", "print(my_sig.entries)\n", "print(my_sig.new_entries)\n", "print(my_sig.exits)\n", "print(my_sig.in_out2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## basic"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RANDNX"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0     True\n", "1    False\n", "2    False\n", "3    False\n", "4    False\n", "5    False\n", "Name: 1, dtype: bool\n", "0    False\n", "1     True\n", "2    False\n", "3    False\n", "4    False\n", "5    False\n", "Name: 1, dtype: bool\n"]}], "source": ["randnx = vbt.RANDNX.run(n=1, input_shape=(6,), seed=42)\n", "\n", "print(randnx.entries)\n", "print(randnx.exits)"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["randnx_n      1      2      3\n", "0          True   True   True\n", "1         False  False  False\n", "2         False   True   True\n", "3         False  False  False\n", "4         False  False   True\n", "5         False  False  False\n", "randnx_n      1      2      3\n", "0         False  False  False\n", "1          True   True   True\n", "2         False  False  False\n", "3         False   True   True\n", "4         False  False  False\n", "5         False  False   True\n"]}], "source": ["randnx = vbt.RANDNX.run(n=[1, 2, 3], input_shape=(6,), seed=42)\n", "\n", "print(randnx.entries)\n", "print(randnx.exits)"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["randnx_n      1      2      3      4\n", "              0      1      0      1\n", "0         False   True   True   True\n", "1          True  False  False  False\n", "2         False  False  False   True\n", "3         False  False   True  False\n", "4         False   True  False   True\n", "5         False  False   True  False\n", "6         False  False  False   True\n", "7         False  False  False  False\n", "randnx_n      1      2      3      4\n", "              0      1      0      1\n", "0         False  False  False  False\n", "1         False  False   True   True\n", "2         False  False  False  False\n", "3         False   True  False   True\n", "4         False  False   True  False\n", "5          True  False  False   True\n", "6         False  False   True  False\n", "7         False   True  False   True\n"]}], "source": ["randnx = vbt.RANDNX.run(n=[np.array([1, 2]), np.array([3, 4])], input_shape=(8, 2), seed=42)\n", "\n", "print(randnx.entries)\n", "print(randnx.exits)"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20.5 ms ± 485 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "198 ms ± 1.35 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["%timeit vbt.RANDNX.run(n=100, input_shape=(1000, 1000), seed=42)\n", "%timeit vbt.RANDNX.run(n=np.full(10, 100).tolist(), input_shape=(1000, 1000), seed=42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RPROBNX"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0     True\n", "1    False\n", "2     True\n", "3    False\n", "4     True\n", "Name: (1.0, 1.0), dtype: bool\n", "0    False\n", "1     True\n", "2    False\n", "3     True\n", "4    False\n", "Name: (1.0, 1.0), dtype: bool\n"]}], "source": ["rprobnx = vbt.RPROBNX.run(entry_prob=1., exit_prob=1., input_shape=(5,), seed=42)\n", "\n", "print(rprobnx.entries)\n", "print(rprobnx.exits)"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0     True\n", "1    False\n", "2     True\n", "3    False\n", "4     True\n", "Name: (array_0, array_0), dtype: bool\n", "0    False\n", "1     True\n", "2    False\n", "3     True\n", "4    False\n", "Name: (array_0, array_0), dtype: bool\n"]}], "source": ["rprobnx = vbt.RPROBNX.run(\n", "    entry_prob=np.asarray([1., 0., 1., 0., 1.]), \n", "    exit_prob=np.asarray([0., 1., 0., 1., 0.]), \n", "    input_shape=(5,), seed=42)\n", "\n", "print(rprobnx.entries)\n", "print(rprobnx.exits)"]}, {"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["rprobnx_entry_prob    0.5    1.0\n", "rprobnx_exit_prob     1.0    0.5\n", "0                    True   True\n", "1                   False  False\n", "2                   False   True\n", "3                   False  False\n", "4                    True  False\n", "rprobnx_entry_prob    0.5    1.0\n", "rprobnx_exit_prob     1.0    0.5\n", "0                   False  False\n", "1                    True   True\n", "2                   False  False\n", "3                   False  False\n", "4                   False  False\n"]}], "source": ["rprobnx = vbt.RPROBNX.run(entry_prob=[0.5, 1.], exit_prob=[1., 0.5], input_shape=(5,), seed=42)\n", "\n", "print(rprobnx.entries)\n", "print(rprobnx.exits)"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["51 ms ± 390 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "503 ms ± 3.47 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%timeit vbt.RPROBNX.run(entry_prob=1., exit_prob=1., input_shape=(1000, 1000), seed=42)\n", "%timeit vbt.RPROBNX.run(\\\n", "    entry_prob=np.full(10, 1.).tolist(), exit_prob=np.full(10, 1.).tolist(), \\\n", "    input_shape=(1000, 1000), seed=42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RPROBX"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["rprobx_prob    0.0                  0.5                  1.0              \n", "                 a      b      c      a      b      c      a      b      c\n", "2018-01-01   False  False  False  False  False  False  False  False  False\n", "2018-01-02   False  False  False  False  False  False   True   True  False\n", "2018-01-03   False  False  False  False  False  False  False  False  False\n", "2018-01-04   False  False  False   True  False   True  False   True   True\n", "2018-01-05   False  False  False  False  False  False  False  False  False\n"]}], "source": ["rprobx = vbt.RPROBX.run(entries, prob=[0., 0.5, 1.], seed=42)\n", "\n", "print(rprobx.exits)"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["8.19 ms ± 211 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "71.8 ms ± 1.01 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["%timeit vbt.RPROBX.run(big_entries, prob=1., seed=42)\n", "%timeit vbt.RPROBX.run(big_entries, prob=np.full(10, 1.).tolist(), seed=42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RPROBCX"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["rprobcx_prob    0.0                  0.5                  1.0              \n", "                  a      b      c      a      b      c      a      b      c\n", "2018-01-01     True   True   True   True   True   True   True   True   True\n", "2018-01-02    False  False  False  False  False  False  False  False  False\n", "2018-01-03    False  False  False  False   True   True  False   True   True\n", "2018-01-04    False  False  False  False  False  False  False  False  False\n", "2018-01-05    False  False  False  False   True  False  False   True  False\n", "rprobcx_prob    0.0                  0.5                  1.0              \n", "                  a      b      c      a      b      c      a      b      c\n", "2018-01-01    False  False  False  False  False  False  False  False  False\n", "2018-01-02    False  False  False  False   True   True   True   True   True\n", "2018-01-03    False  False  False   True  False  False  False  False  False\n", "2018-01-04    False  False  False  False   True  False  False   True   True\n", "2018-01-05    False  False  False  False  False   True  False  False  False\n"]}], "source": ["rprobcx = vbt.RPROBCX.run(entries, prob=[0., 0.5, 1.], seed=42)\n", "\n", "print(rprobcx.new_entries)\n", "print(rprobcx.exits)"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20.8 ms ± 167 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "202 ms ± 2.47 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%timeit vbt.RPROBCX.run(big_entries, prob=1., seed=42)\n", "%timeit vbt.RPROBCX.run(big_entries, prob=np.full(10, 1.).tolist(), seed=42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### STX"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["stx_stop      0.1              \n", "                a      b      c\n", "2018-01-01  False  False  False\n", "2018-01-02   True   True  False\n", "2018-01-03  False  False  False\n", "2018-01-04  False  False  False\n", "2018-01-05  False  False  False\n"]}], "source": ["stx = vbt.STX.run(entries, ts, 0.1)\n", "\n", "print(stx.exits)"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["stx_stop   array_0              \n", "                 a      b      c\n", "2018-01-01   False  False  False\n", "2018-01-02    True   True  False\n", "2018-01-03   False  False  False\n", "2018-01-04   <PERSON><PERSON>e   True   True\n", "2018-01-05   False  False  False\n"]}], "source": ["stx = vbt.STX.run(entries, ts, np.asarray([0.1, 0.1, -0.1, -0.1, -0.1])[:, None])\n", "\n", "print(stx.exits)"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["stx_stop        0.1                                      -0.1                \\\n", "stx_trailing  False                 True                False                 \n", "                  a      b      c      a      b      c      a      b      c   \n", "2018-01-01    False  False  False  False  False  False  False  False  False   \n", "2018-01-02     True   True  False   True   True  False  False  False  False   \n", "2018-01-03    False  False  False  False  False  False  False  False  False   \n", "2018-01-04    False  False  False  False  False  False  False   True   True   \n", "2018-01-05    False  False  False  False  False  False  False  False  False   \n", "\n", "stx_stop                           \n", "stx_trailing   True                \n", "                  a      b      c  \n", "2018-01-01    False  False  False  \n", "2018-01-02    False  False  False  \n", "2018-01-03    False  False  False  \n", "2018-01-04     <PERSON>   True   True  \n", "2018-01-05    False  False  False  \n"]}], "source": ["stx = vbt.STX.run(entries, ts, [0.1, 0.1, -0.1, -0.1], trailing=[False, True, False, True])\n", "\n", "print(stx.exits)"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["14 ms ± 177 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "119 ms ± 1.75 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["%timeit vbt.STX.run(big_entries, big_ts, 0.1)\n", "%timeit vbt.STX.run(big_entries, big_ts, np.full(10, 0.1).tolist())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### STCX"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["stcx_stop        0.1                                      -0.1                \\\n", "stcx_trailing  False                 True                False                 \n", "                   a      b      c      a      b      c      a      b      c   \n", "2018-01-01      True   True   True   True   True   True   True   True   True   \n", "2018-01-02     False  False  False  False  False  False  False  False  False   \n", "2018-01-03     False   True   True  False   True   True  False  False  False   \n", "2018-01-04     False  False  False  False  False  False  False  False  False   \n", "2018-01-05     False  False  False  False  False  False  False  False  False   \n", "\n", "stcx_stop                           \n", "stcx_trailing   True                \n", "                   a      b      c  \n", "2018-01-01      <PERSON>   True   True  \n", "2018-01-02     False  False  False  \n", "2018-01-03     False  False  False  \n", "2018-01-04     False  False  False  \n", "2018-01-05     False   True  False  \n", "stcx_stop        0.1                                      -0.1                \\\n", "stcx_trailing  False                 True                False                 \n", "                   a      b      c      a      b      c      a      b      c   \n", "2018-01-01     False  False  False  False  False  False  False  False  False   \n", "2018-01-02      True   True   True   True   True   True  False  False  False   \n", "2018-01-03     False  False  False  False  False  False  False  False  False   \n", "2018-01-04     False  False  False  False  False  False  False  False  False   \n", "2018-01-05     False  False  False  False  False  False  False  False  False   \n", "\n", "stcx_stop                           \n", "stcx_trailing   True                \n", "                   a      b      c  \n", "2018-01-01     False  False  False  \n", "2018-01-02     False  False  False  \n", "2018-01-03     False  False  False  \n", "2018-01-04      <PERSON>   True   True  \n", "2018-01-05     False  False  False  \n"]}], "source": ["stcx = vbt.STCX.run(entries, ts, [0.1, 0.1, -0.1, -0.1], trailing=[False, True, False, True])\n", "\n", "print(stcx.new_entries)\n", "print(stcx.exits)"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["9.44 ms ± 92.1 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "77 ms ± 1.08 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["%timeit vbt.STCX.run(big_entries, big_ts, 0.1)\n", "%timeit vbt.STCX.run(big_entries, big_ts, np.full(10, 0.1).tolist())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### OHLCSTX"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ohlcstx_sl_stop    0.1              \n", "                     a      b      c\n", "2018-01-01       False  False  False\n", "2018-01-02       False  False  False\n", "2018-01-03       False  False  False\n", "2018-01-04       <PERSON><PERSON>e   True   True\n", "2018-01-05        True  False  False\n", "ohlcstx_sl_stop  0.1            \n", "                   a     b     c\n", "2018-01-01       NaN   NaN   NaN\n", "2018-01-02       NaN   NaN   NaN\n", "2018-01-03       NaN   NaN   NaN\n", "2018-01-04       NaN  10.8  10.8\n", "2018-01-05       9.0   NaN   NaN\n", "ohlcstx_sl_stop       0.1                    \n", "                        a         b         c\n", "2018-01-01           None      None      None\n", "2018-01-02           None      None      None\n", "2018-01-03           None      None      None\n", "2018-01-04           None  StopLoss  StopLoss\n", "2018-01-05       StopLoss      None      None\n"]}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-9196ff\"><g class=\"clips\"><clipPath id=\"clip9196ffxyplot\" class=\"plotclip\"><rect width=\"640\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip9196ffx\"><rect x=\"30\" y=\"0\" width=\"640\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip9196ffy\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip9196ffxy\"><rect x=\"30\" y=\"46\" width=\"640\" height=\"261\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"30\" y=\"46\" width=\"640\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(94,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(222,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(350,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(478,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(606,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,293.95)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,235.23)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,176.5)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,117.77)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,59.05)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(30,46)\" clip-path=\"url('#clip9196ffxyplot')\"><g class=\"ohlclayer mlayer\"><g class=\"trace ohlc\" style=\"opacity: 1;\"><path d=\"M25.6,189.23H64M64,130.5V247.95M102.4,189.23H64\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M153.6,130.5H192M192,71.77V189.23M230.4,130.5H192\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M281.6,71.77H320M320,13.05V130.5M358.4,71.77H320\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M409.6,130.5H448M448,71.77V189.23M486.4,130.5H448\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M537.6,189.23H576M576,130.5V247.95M614.4,189.23H576\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g></g><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace92ca4342-a82f-491e-b2b5-154f7504caf0\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(64,189.23)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(320,71.77)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(576,189.23)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g><g class=\"text\"/></g><g class=\"trace scatter tracee23afbf0-d874-4741-bb36-918b3d6578bf\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(448,142.25)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(94,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(222,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(350,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(478,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(606,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 5</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,293.95)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">9</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,235.23)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,176.5)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">11</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,117.77)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">12</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,59.05)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">13</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-9196ff\"><g class=\"clips\"/><clipPath id=\"legend9196ff\"><rect width=\"224\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(446,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"224\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend9196ff')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">OHLC</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendohlc\" d=\"M15,0H0M8,6V0\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; fill: none; stroke-width: 2px; stroke: rgb(217, 95, 2); stroke-opacity: 1;\"/><path class=\"legendohlc\" d=\"M-15,0H0M-8,-6V0\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; fill: none; stroke-width: 2px; stroke: rgb(27, 158, 118); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"76.03125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(78.53125,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Entry</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"74.640625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(155.671875,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Exit</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"65.21875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ohlcstx = vbt.OHLCSTX.run(\n", "    entries, price['open'], price['high'], price['low'], price['close'], \n", "    sl_stop=0.1\n", ")\n", "\n", "print(ohlcstx.exits)\n", "print(ohlcstx.stop_price)\n", "print(ohlcstx.stop_type_readable)\n", "ohlcstx[(0.1, 'b')].plot().show_svg()"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ohlcstx_sl_stop    0.1                  0.0                                   \n", "ohlcstx_tp_stop    0.0                  0.0                  0.1              \n", "                     a      b      c      a      b      c      a      b      c\n", "2018-01-01       False  False  False  False  False  False  False  False  False\n", "2018-01-02        True   True  False   True   True  False   True   True  False\n", "2018-01-03       False  False  False  False  False  False  False  False  False\n", "2018-01-04       False   True   True  False   True   True  False   True   True\n", "2018-01-05       False  False  False  False  False  False  False  False  False\n", "ohlcstx_sl_stop   0.1               0.0                              \n", "ohlcstx_tp_stop   0.0               0.0               0.1            \n", "                    a     b     c     a     b     c     a     b     c\n", "2018-01-01        NaN   NaN   NaN   NaN   NaN   NaN   NaN   NaN   NaN\n", "2018-01-02       10.0  10.0   NaN  10.0  10.0   NaN  10.0  10.0   NaN\n", "2018-01-03        NaN   NaN   NaN   NaN   NaN   NaN   NaN   NaN   NaN\n", "2018-01-04        NaN  10.8  10.8   NaN  12.0  12.0   NaN  12.0  12.0\n", "2018-01-05        NaN   NaN   NaN   NaN   NaN   NaN   NaN   NaN   NaN\n", "ohlcstx_sl_stop         0.1                             0.0            \\\n", "ohlcstx_tp_stop         0.0                             0.0             \n", "                          a           b         c         a         b   \n", "2018-01-01             None        None      None      None      None   \n", "2018-01-02       TakeProfit  TakeProfit      None  StopLoss  StopLoss   \n", "2018-01-03             None        None      None      None      None   \n", "2018-01-04             None    StopLoss  StopLoss      None  StopLoss   \n", "2018-01-05             None        None      None      None      None   \n", "\n", "ohlcstx_sl_stop                                          \n", "ohlcstx_tp_stop                 0.1                      \n", "                        c         a         b         c  \n", "2018-01-01           None      None      None      None  \n", "2018-01-02           None  StopLoss  StopLoss      None  \n", "2018-01-03           None      None      None      None  \n", "2018-01-04       StopLoss      None  StopLoss  StopLoss  \n", "2018-01-05           None      None      None      None  \n"]}], "source": ["ohlcstx = vbt.OHLCSTX.run(\n", "    entries, price['open'], price['high'], price['low'], price['close'], \n", "    sl_stop=[0.1, 0., 0.], ts_stop=[0., 0.1, 0.], tp_stop=[0., 0., 0.1]\n", ")\n", "\n", "print(ohlcstx.exits)\n", "print(ohlcstx.stop_price)\n", "print(ohlcstx.stop_type_readable)"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["28.1 ms ± 605 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "226 ms ± 7.07 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%timeit vbt.OHLCSTX.run(\\\n", "    big_entries, big_ts, big_ts + 1, big_ts - 1, big_ts,\\\n", "    sl_stop=0.1, ts_stop=0.1, tp_stop=0.1)\n", "%timeit vbt.OHLCSTX.run(\\\n", "    big_entries, big_ts, big_ts + 1, big_ts - 1, big_ts,\\\n", "    sl_stop=np.full(10, 0.1).tolist(), ts_stop=np.full(10, 0.1).tolist(), tp_stop=np.full(10, 0.1).tolist())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### OHLCSTCX"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ohlcstcx_sl_stop    0.1              \n", "                      a      b      c\n", "2018-01-01         <PERSON>   True   True\n", "2018-01-02        False  False  False\n", "2018-01-03        False  False  False\n", "2018-01-04        False  False  False\n", "2018-01-05        False  False  False\n", "ohlcstcx_sl_stop    0.1              \n", "                      a      b      c\n", "2018-01-01        False  False  False\n", "2018-01-02        False  False  False\n", "2018-01-03        False  False  False\n", "2018-01-04        False  False  False\n", "2018-01-05         <PERSON>   True   True\n", "ohlcstcx_sl_stop  0.1          \n", "                    a    b    c\n", "2018-01-01        NaN  NaN  NaN\n", "2018-01-02        NaN  NaN  NaN\n", "2018-01-03        NaN  NaN  NaN\n", "2018-01-04        NaN  NaN  NaN\n", "2018-01-05        9.0  9.0  9.0\n", "ohlcstcx_sl_stop       0.1                    \n", "                         a         b         c\n", "2018-01-01            None      None      None\n", "2018-01-02            None      None      None\n", "2018-01-03            None      None      None\n", "2018-01-04            None      None      None\n", "2018-01-05        StopLoss  StopLoss  StopLoss\n"]}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-249257\"><g class=\"clips\"><clipPath id=\"clip249257xyplot\" class=\"plotclip\"><rect width=\"640\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip249257x\"><rect x=\"30\" y=\"0\" width=\"640\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip249257y\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip249257xy\"><rect x=\"30\" y=\"46\" width=\"640\" height=\"261\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"30\" y=\"46\" width=\"640\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(94,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(222,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(350,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(478,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(606,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,288.95)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,231.48)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,174)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,116.53)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,59.05)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(30,46)\" clip-path=\"url('#clip249257xyplot')\"><g class=\"ohlclayer mlayer\"><g class=\"trace ohlc\" style=\"opacity: 1;\"><path d=\"M25.6,185.48H64M64,128V242.95M102.4,185.48H64\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M153.6,128H192M192,70.53V185.48M230.4,128H192\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M281.6,70.53H320M320,13.05V128M358.4,70.53H320\" style=\"fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M409.6,128H448M448,70.53V185.48M486.4,128H448\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/><path d=\"M537.6,185.48H576M576,128V242.95M614.4,185.48H576\" style=\"fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g></g><g class=\"scatterlayer mlayer\"><g class=\"trace scatter traced7f78b59-b1fd-41f9-8b6c-c01762157a97\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(64,185.48)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g><g class=\"text\"/></g><g class=\"trace scatter trace68e74506-ce85-4ebc-afda-a4889baf943b\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(576,242.95)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(94,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(222,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(350,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(478,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(606,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 5</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,288.95)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">9</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,231.48)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,174)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">11</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,116.53)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">12</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,59.05)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">13</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-249257\"><g class=\"clips\"/><clipPath id=\"legend249257\"><rect width=\"254\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(416,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"254\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend249257')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">OHLC</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendohlc\" d=\"M15,0H0M8,6V0\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; fill: none; stroke-width: 2px; stroke: rgb(217, 95, 2); stroke-opacity: 1;\"/><path class=\"legendohlc\" d=\"M-15,0H0M-8,-6V0\" transform=\"translate(20,0)\" style=\"stroke-miterlimit: 1; fill: none; stroke-width: 2px; stroke: rgb(27, 158, 118); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"76.03125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(78.53125,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">New Entry</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"104.796875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(185.828125,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Exit</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"65.21875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ohlcstcx = vbt.OHLCSTCX.run(\n", "    entries, price['open'], price['high'], price['low'], price['close'], \n", "    sl_stop=0.1\n", ")\n", "\n", "print(ohlcstcx.new_entries)\n", "print(ohlcstcx.exits)\n", "print(ohlcstcx.stop_price)\n", "print(ohlcstcx.stop_type_readable)\n", "ohlcstcx[(0.1, 'b')].plot().show_svg()"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["38.8 ms ± 1.07 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n", "329 ms ± 728 µs per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["%timeit vbt.OHLCSTCX.run(\\\n", "    big_entries, big_ts, big_ts + 1, big_ts - 1, big_ts,\\\n", "    sl_stop=0.1, ts_stop=0.1, tp_stop=0.1)\n", "%timeit vbt.OHLCSTCX.run(\\\n", "    big_entries, big_ts, big_ts + 1, big_ts - 1, big_ts,\\\n", "    sl_stop=np.full(10, 0.1).tolist(), ts_stop=np.full(10, 0.1).tolist(), tp_stop=np.full(10, 0.1).tolist())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {"098f62fea95b404aa6313fd55e26f84d": {"buffers": [{"data": "AAAAAAAA8D8AAAAAAAD4fwAAAAAAAPA/AAAAAAAA+H8AAAAAAADwPw==", "encoding": "base64", "path": ["_data", 0, "y", "value"]}, {"data": "AAAAAAAA+H8AAAAAAADwPwAAAAAAAPh/AAAAAAAA8D8AAAAAAAD4fw==", "encoding": "base64", "path": ["_data", 1, "y", "value"]}], "model_module": "plotly<PERSON>dget", "model_module_version": "^4.12.0", "model_name": "FigureModel", "state": {"_config": {"plotlyServerURL": "https://plot.ly"}, "_data": [{"marker": {"color": "#37B13F", "line": {"color": "rgb(38,123,44)", "width": 1}, "size": 8, "symbol": "triangle-up"}, "mode": "markers", "name": "Entry", "showlegend": true, "type": "scatter", "uid": "34e1fdef-e576-4896-b883-a60f192224fd", "x": [0, 1, 2, 3, 4], "y": {"dtype": "float64", "shape": [5], "value": {}}}, {"marker": {"color": "#EA4335", "line": {"color": "rgb(181,31,18)", "width": 1}, "size": 8, "symbol": "triangle-down"}, "mode": "markers", "name": "Exit", "showlegend": true, "type": "scatter", "uid": "d026718b-7795-4af3-9926-76f02973a9bc", "x": [0, 1, 2, 3, 4], "y": {"dtype": "float64", "shape": [5], "value": {}}}], "_js2py_layoutDelta": {}, "_js2py_pointsCallback": {}, "_js2py_relayout": {}, "_js2py_restyle": {}, "_js2py_traceDeltas": {}, "_js2py_update": {}, "_last_layout_edit_id": 4, "_last_trace_edit_id": 4, "_layout": {"autosize": false, "colorway": ["#1f77b4", "#ff7f0e", "#2ca02c", "#dc3912", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"], "height": 350, "hovermode": "closest", "legend": {"orientation": "h", "traceorder": "normal", "x": 1, "xanchor": "right", "y": 1.02, "yanchor": "bottom"}, "margin": {"b": 30, "l": 30, "r": 30, "t": 30}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 700}, "_py2js_animate": {}, "_py2js_deleteTraces": {}, "_py2js_moveTraces": {}, "_py2js_removeLayoutProps": {}, "_py2js_removeTraceProps": {}, "_py2js_restyle": {}, "_view_count": 0}}, "53a3b68b2d9040289dda3ac2f353f20c": {"buffers": [{"data": "AAAAAAAA8D8AAAAAAAD4fwAAAAAAAPA/AAAAAAAA+H8AAAAAAADwPw==", "encoding": "base64", "path": ["_data", 0, "y", "value"]}, {"data": "AAAAAAAA+H8AAAAAAADwPwAAAAAAAPh/AAAAAAAA8D8AAAAAAAD4fw==", "encoding": "base64", "path": ["_data", 1, "y", "value"]}], "model_module": "plotly<PERSON>dget", "model_module_version": "^4.12.0", "model_name": "FigureModel", "state": {"_config": {"plotlyServerURL": "https://plot.ly"}, "_data": [{"marker": {"color": "#37B13F", "line": {"color": "rgb(38,123,44)", "width": 1}, "size": 8, "symbol": "triangle-up"}, "mode": "markers", "name": "Entry", "showlegend": true, "type": "scatter", "uid": "01ccccdc-c5a0-4314-a26a-28f5647c62dc", "x": [0, 1, 2, 3, 4], "y": {"dtype": "float64", "shape": [5], "value": {}}}, {"marker": {"color": "#EA4335", "line": {"color": "rgb(181,31,18)", "width": 1}, "size": 8, "symbol": "triangle-down"}, "mode": "markers", "name": "Exit", "showlegend": true, "type": "scatter", "uid": "db6ef3a7-f0e6-4b42-8195-f002b62d2c8b", "x": [0, 1, 2, 3, 4], "y": {"dtype": "float64", "shape": [5], "value": {}}}], "_js2py_layoutDelta": {}, "_js2py_pointsCallback": {}, "_js2py_relayout": {}, "_js2py_restyle": {}, "_js2py_traceDeltas": {}, "_js2py_update": {}, "_last_layout_edit_id": 4, "_last_trace_edit_id": 4, "_layout": {"autosize": false, "colorway": ["#1f77b4", "#ff7f0e", "#2ca02c", "#dc3912", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"], "height": 350, "hovermode": "closest", "legend": {"orientation": "h", "traceorder": "normal", "x": 1, "xanchor": "right", "y": 1.02, "yanchor": "bottom"}, "margin": {"b": 30, "l": 30, "r": 30, "t": 30}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 700}, "_py2js_animate": {}, "_py2js_deleteTraces": {}, "_py2js_moveTraces": {}, "_py2js_removeLayoutProps": {}, "_py2js_removeTraceProps": {}, "_py2js_restyle": {}, "_view_count": 0}}, "7910588777404ccc8420d90babe91d94": {"buffers": [{"data": "AAAAAAAAJEAAAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAD4fw==", "encoding": "base64", "path": ["_data", 1, "y", "value"]}, {"data": "AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/AAAAAAAA+H8AAAAAAAAiQA==", "encoding": "base64", "path": ["_data", 2, "y", "value"]}], "model_module": "plotly<PERSON>dget", "model_module_version": "^4.12.0", "model_name": "FigureModel", "state": {"_config": {"plotlyServerURL": "https://plot.ly"}, "_data": [{"close": [10, 11, 12, 11, 10], "decreasing": {"line": {"color": "#d95f02"}}, "high": [11, 12, 13, 12, 11], "increasing": {"line": {"color": "#1b9e76"}}, "low": [9, 10, 11, 10, 9], "name": "OHLC", "opacity": 0.7, "open": [10, 11, 12, 11, 10], "type": "ohlc", "uid": "6eef3ca9-dcad-44ac-9a4e-4bdb30924d3a", "x": ["2018-01-01T00:00:00.000000", "2018-01-02T00:00:00.000000", "2018-01-03T00:00:00.000000", "2018-01-04T00:00:00.000000", "2018-01-05T00:00:00.000000"]}, {"marker": {"color": "#37B13F", "line": {"color": "rgb(38,123,44)", "width": 1}, "size": 8, "symbol": "triangle-up"}, "mode": "markers", "name": "Entry", "showlegend": true, "type": "scatter", "uid": "d767998c-600c-4079-a368-7b5a88cef13d", "x": ["2018-01-01T00:00:00.000000", "2018-01-02T00:00:00.000000", "2018-01-03T00:00:00.000000", "2018-01-04T00:00:00.000000", "2018-01-05T00:00:00.000000"], "y": {"dtype": "float64", "shape": [5], "value": {}}}, {"customdata": ["", "", "", "", "StopLoss"], "hovertemplate": "(%{x}, %{y})<br>Type: %{customdata}", "marker": {"color": "#EA4335", "line": {"color": "rgb(181,31,18)", "width": 1}, "size": 8, "symbol": "triangle-down"}, "mode": "markers", "name": "Exit", "showlegend": true, "type": "scatter", "uid": "575adf87-e540-4654-8ccc-16dd4b64fb12", "x": ["2018-01-01T00:00:00.000000", "2018-01-02T00:00:00.000000", "2018-01-03T00:00:00.000000", "2018-01-04T00:00:00.000000", "2018-01-05T00:00:00.000000"], "y": {"dtype": "float64", "shape": [5], "value": {}}}], "_js2py_layoutDelta": {}, "_js2py_pointsCallback": {}, "_js2py_relayout": {}, "_js2py_restyle": {}, "_js2py_traceDeltas": {}, "_js2py_update": {}, "_last_layout_edit_id": 6, "_last_trace_edit_id": 6, "_layout": {"autosize": false, "colorway": ["#1f77b4", "#ff7f0e", "#2ca02c", "#dc3912", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"], "height": 350, "hovermode": "closest", "legend": {"orientation": "h", "traceorder": "normal", "x": 1, "xanchor": "right", "y": 1.02, "yanchor": "bottom"}, "margin": {"b": 30, "l": 30, "r": 30, "t": 30}, "showlegend": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 700, "xaxis": {"rangeslider": {"visible": false}, "showgrid": true}, "yaxis": {"showgrid": true}}, "_py2js_animate": {}, "_py2js_deleteTraces": {}, "_py2js_moveTraces": {}, "_py2js_removeLayoutProps": {}, "_py2js_removeTraceProps": {}, "_py2js_restyle": {}, "_view_count": 0}}, "e5e1e7401489428e97f3286d07c14aa4": {"buffers": [{"data": "AAAAAAAA8D8AAAAAAAD4fwAAAAAAAPA/AAAAAAAA+H8AAAAAAADwPw==", "encoding": "base64", "path": ["_data", 0, "y", "value"]}, {"data": "AAAAAAAA+H8AAAAAAADwPwAAAAAAAPh/AAAAAAAA8D8AAAAAAAD4fw==", "encoding": "base64", "path": ["_data", 1, "y", "value"]}], "model_module": "plotly<PERSON>dget", "model_module_version": "^4.12.0", "model_name": "FigureModel", "state": {"_config": {"plotlyServerURL": "https://plot.ly"}, "_data": [{"marker": {"color": "#37B13F", "line": {"color": "rgb(38,123,44)", "width": 1}, "size": 8, "symbol": "triangle-up"}, "mode": "markers", "name": "Entry", "showlegend": true, "type": "scatter", "uid": "9457f5a1-a6d3-4379-a23f-4967cf24d721", "x": [0, 1, 2, 3, 4], "y": {"dtype": "float64", "shape": [5], "value": {}}}, {"marker": {"color": "#EA4335", "line": {"color": "rgb(181,31,18)", "width": 1}, "size": 8, "symbol": "triangle-down"}, "mode": "markers", "name": "Exit", "showlegend": true, "type": "scatter", "uid": "86c347e7-4e0a-458c-af76-b304b808cc64", "x": [0, 1, 2, 3, 4], "y": {"dtype": "float64", "shape": [5], "value": {}}}], "_js2py_layoutDelta": {}, "_js2py_pointsCallback": {}, "_js2py_relayout": {}, "_js2py_restyle": {}, "_js2py_traceDeltas": {}, "_js2py_update": {}, "_last_layout_edit_id": 4, "_last_trace_edit_id": 4, "_layout": {"autosize": false, "colorway": ["#1f77b4", "#ff7f0e", "#2ca02c", "#dc3912", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"], "height": 350, "hovermode": "closest", "legend": {"orientation": "h", "traceorder": "normal", "x": 1, "xanchor": "right", "y": 1.02, "yanchor": "bottom"}, "margin": {"b": 30, "l": 30, "r": 30, "t": 30}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 700}, "_py2js_animate": {}, "_py2js_deleteTraces": {}, "_py2js_moveTraces": {}, "_py2js_removeLayoutProps": {}, "_py2js_removeTraceProps": {}, "_py2js_restyle": {}, "_view_count": 0}}, "fa44b07999ad4c8c99cdbd9bf5e2faeb": {"buffers": [{"data": "AAAAAAAAJEAAAAAAAAD4fwAAAAAAAChAAAAAAAAA+H8AAAAAAAAkQA==", "encoding": "base64", "path": ["_data", 1, "y", "value"]}, {"data": "AAAAAAAA+H8AAAAAAAD4fwAAAAAAAPh/mpmZmZmZJUAAAAAAAAD4fw==", "encoding": "base64", "path": ["_data", 2, "y", "value"]}], "model_module": "plotly<PERSON>dget", "model_module_version": "^4.12.0", "model_name": "FigureModel", "state": {"_config": {"plotlyServerURL": "https://plot.ly"}, "_data": [{"close": [10, 11, 12, 11, 10], "decreasing": {"line": {"color": "#d95f02"}}, "high": [11, 12, 13, 12, 11], "increasing": {"line": {"color": "#1b9e76"}}, "low": [9, 10, 11, 10, 9], "name": "OHLC", "opacity": 0.7, "open": [10, 11, 12, 11, 10], "type": "ohlc", "uid": "d1bafa2f-3298-4af2-8ea1-3b3fe6cc323d", "x": ["2018-01-01T00:00:00.000000", "2018-01-02T00:00:00.000000", "2018-01-03T00:00:00.000000", "2018-01-04T00:00:00.000000", "2018-01-05T00:00:00.000000"]}, {"marker": {"color": "#37B13F", "line": {"color": "rgb(38,123,44)", "width": 1}, "size": 8, "symbol": "triangle-up"}, "mode": "markers", "name": "Entry", "showlegend": true, "type": "scatter", "uid": "8e3b063f-49ad-406b-b2e6-418f732c78c9", "x": ["2018-01-01T00:00:00.000000", "2018-01-02T00:00:00.000000", "2018-01-03T00:00:00.000000", "2018-01-04T00:00:00.000000", "2018-01-05T00:00:00.000000"], "y": {"dtype": "float64", "shape": [5], "value": {}}}, {"customdata": ["", "", "", "StopLoss", ""], "hovertemplate": "(%{x}, %{y})<br>Type: %{customdata}", "marker": {"color": "#EA4335", "line": {"color": "rgb(181,31,18)", "width": 1}, "size": 8, "symbol": "triangle-down"}, "mode": "markers", "name": "Exit", "showlegend": true, "type": "scatter", "uid": "9eedc724-9a83-4170-95bb-17b1e4d0a947", "x": ["2018-01-01T00:00:00.000000", "2018-01-02T00:00:00.000000", "2018-01-03T00:00:00.000000", "2018-01-04T00:00:00.000000", "2018-01-05T00:00:00.000000"], "y": {"dtype": "float64", "shape": [5], "value": {}}}], "_js2py_layoutDelta": {}, "_js2py_pointsCallback": {}, "_js2py_relayout": {}, "_js2py_restyle": {}, "_js2py_traceDeltas": {}, "_js2py_update": {}, "_last_layout_edit_id": 6, "_last_trace_edit_id": 6, "_layout": {"autosize": false, "colorway": ["#1f77b4", "#ff7f0e", "#2ca02c", "#dc3912", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"], "height": 350, "hovermode": "closest", "legend": {"orientation": "h", "traceorder": "normal", "x": 1, "xanchor": "right", "y": 1.02, "yanchor": "bottom"}, "margin": {"b": 30, "l": 30, "r": 30, "t": 30}, "showlegend": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 700, "xaxis": {"rangeslider": {"visible": false}, "showgrid": true}, "yaxis": {"showgrid": true}}, "_py2js_animate": {}, "_py2js_deleteTraces": {}, "_py2js_moveTraces": {}, "_py2js_removeLayoutProps": {}, "_py2js_removeTraceProps": {}, "_py2js_restyle": {}, "_view_count": 0}}, "ffb81874391c45b797e029d13ad98923": {"buffers": [{"data": "AAAAAAAA8D8AAAAAAAD4fwAAAAAAAPA/AAAAAAAA+H8AAAAAAADwPw==", "encoding": "base64", "path": ["_data", 0, "y", "value"]}, {"data": "AAAAAAAA+H8AAAAAAADwPwAAAAAAAPh/AAAAAAAA8D8AAAAAAAD4fw==", "encoding": "base64", "path": ["_data", 1, "y", "value"]}], "model_module": "plotly<PERSON>dget", "model_module_version": "^4.12.0", "model_name": "FigureModel", "state": {"_config": {"plotlyServerURL": "https://plot.ly"}, "_data": [{"marker": {"color": "#37B13F", "line": {"color": "rgb(38,123,44)", "width": 1}, "size": 8, "symbol": "triangle-up"}, "mode": "markers", "name": "Entry", "showlegend": true, "type": "scatter", "uid": "b3f1bed4-8c42-461b-add2-bbc929185ff0", "x": [0, 1, 2, 3, 4], "y": {"dtype": "float64", "shape": [5], "value": {}}}, {"marker": {"color": "#EA4335", "line": {"color": "rgb(181,31,18)", "width": 1}, "size": 8, "symbol": "triangle-down"}, "mode": "markers", "name": "Exit", "showlegend": true, "type": "scatter", "uid": "40bbe151-6890-4437-95db-78b8d27b4aed", "x": [0, 1, 2, 3, 4], "y": {"dtype": "float64", "shape": [5], "value": {}}}], "_js2py_layoutDelta": {}, "_js2py_pointsCallback": {}, "_js2py_relayout": {}, "_js2py_restyle": {}, "_js2py_traceDeltas": {}, "_js2py_update": {}, "_last_layout_edit_id": 4, "_last_trace_edit_id": 4, "_layout": {"autosize": false, "colorway": ["#1f77b4", "#ff7f0e", "#2ca02c", "#dc3912", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"], "height": 350, "hovermode": "closest", "legend": {"orientation": "h", "traceorder": "normal", "x": 1, "xanchor": "right", "y": 1.02, "yanchor": "bottom"}, "margin": {"b": 30, "l": 30, "r": 30, "t": 30}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "width": 700}, "_py2js_animate": {}, "_py2js_deleteTraces": {}, "_py2js_moveTraces": {}, "_py2js_removeLayoutProps": {}, "_py2js_removeTraceProps": {}, "_py2js_restyle": {}, "_view_count": 0}}}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}