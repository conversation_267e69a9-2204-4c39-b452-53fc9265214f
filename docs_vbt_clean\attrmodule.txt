attr_ moduleUtilities for working with class/instance attributes. deep_getattr functiondeep_getattr ( obj , attr_chain , getattr_func =< function default_getattr_func > , call_last_attr = True ) Retrieve attribute consecutively. The attribute chain attr_chain can be: string -> get variable/property or method without arguments tuple of string -> call method without arguments tuple of string and tuple -> call method and pass positional arguments (unpacked) tuple of string, tuple, and dict -> call method and pass positional and keyword arguments (unpacked) iterable of any of the above Use getattr_func to overwrite the default behavior of accessing an attribute (see default_getattr_func() ). Hint If your chain includes only attributes and functions without arguments, you can represent this chain as a single (but probably long) string. default_getattr_func functiondefault_getattr_func ( obj , attr , args = None , kwargs = None , call_attr = True ) Default getattr_func . get_dict_attr functionget_dict_attr ( obj , attr ) Get attribute without invoking the attribute lookup machinery. AttrResolver classAttrResolver () Class that implements resolution of self and its attributes. Resolution is getattr that works for self, properties, and methods. It also utilizes built-in caching. Subclasses Wrapping deep_getattr methodAttrResolver . deep_getattr ( * args , ** kwargs ) See deep_getattr() . post_resolve_attr methodAttrResolver . post_resolve_attr ( attr , out , final_kwargs = None ) Post-process an object after resolution. Should return an object. pre_resolve_attr methodAttrResolver . pre_resolve_attr ( attr , final_kwargs = None ) Pre-process an attribute before resolution. Should return an attribute. resolve_attr methodAttrResolver . resolve_attr ( attr , args = None , cond_kwargs = None , kwargs = None , custom_arg_names = None , cache_dct = None , use_caching = True , passed_kwargs_out = None ) Resolve an attribute using keyword arguments and built-in caching. If attr is a property, returns its value. If attr is a method, passes *args , **kwargs , and **cond_kwargs with keys found in the signature. If attr is a property and there is a get_{arg} method, calls the get_{arg} method. Won't cache if use_caching is False or any passed argument is in custom_arg_names . Use passed_kwargs_out to get keyword arguments that were passed. resolve_self methodAttrResolver . resolve_self ( cond_kwargs = None , custom_arg_names = None , impacts_caching = True , silence_warnings = False ) Resolve self. Note cond_kwargs can be modified in-place. self_aliases propertyNames to associate with this object.