config moduleUtilities for configuration. convert_to_dict functionconvert_to_dict ( dct , nested = True ) Convert any dict (apart from atomic_dict ) to dict . Set nested to True to convert all child dicts in recursive manner. copy_dict functioncopy_dict ( dct , copy_mode = 'shallow' , nested = True ) Copy dict based on a copy mode. The following modes are supported: 'shallow': Copies keys only. 'hybrid': Copies keys and values using copy.copy . 'deep': Copies the whole thing using copy.deepcopy . Set nested to True to copy all child dicts in recursive manner. get_func_arg_names functionget_func_arg_names ( func , arg_kind = None ) Get argument names of a function. get_func_kwargs functionget_func_kwargs ( func ) Get keyword arguments with defaults of a function. merge_dicts functionmerge_dicts ( * dicts , to_dict = True , copy_mode = 'shallow' , nested = True , same_keys = False ) Merge dicts. Args *dicts : dict Dicts. to_dict : bool Whether to call convert_to_dict() on each dict prior to copying. copy_mode : str Mode for copy_dict() to copy each dict prior to merging. Pass None to not copy. nested : bool Whether to merge all child dicts in recursive manner. same_keys : bool Whether to merge on the overlapping keys only. resolve_dict functionresolve_dict ( dct , i = None ) Select keyword arguments. set_dict_item functionset_dict_item ( dct , k , v , force = False ) Set dict item. If the dict is of the type Config , also passes force keyword to override blocking flags. update_dict functionupdate_dict ( x , y , nested = True , force = False , same_keys = False ) Update dict with keys and values from other dict. Set nested to True to update all child dicts in recursive manner. For force , see set_dict_item() . If you want to treat any dict as a single value, wrap it with atomic_dict . Note If the child dict is not atomic, it will copy only its values, not its meta. AtomicConfig classAtomicConfig ( dct = None , copy_kwargs = None , reset_dct = None , reset_dct_copy_kwargs = None , frozen_keys = None , readonly = None , nested = None , convert_dicts = None , as_attrs = None ) Config that behaves like a single value when merging. Superclasses Config Documented Pickleable PickleableDict atomic_dict builtins.dict Inherited members Config.as_attrs_ Config.clear() Config.convert_dicts_ Config.copy() Config.copy_kwargs_ Config.dumps() Config.frozen_keys_ Config.load_update() Config.loads() Config.make_checkpoint() Config.merge_with() Config.nested_ Config.pop() Config.popitem() Config.readonly_ Config.reset() Config.reset_dct_ Config.reset_dct_copy_kwargs_ Config.to_dict() Config.to_doc() Config.update() Pickleable.load() Pickleable.save() Config classConfig ( dct = None , copy_kwargs = None , reset_dct = None , reset_dct_copy_kwargs = None , frozen_keys = None , readonly = None , nested = None , convert_dicts = None , as_attrs = None ) Extends dict with config features such as nested updates, frozen keys/values, and pickling. Args dct : dict Dict to construct this config from. copy_kwargs : dict Keyword arguments passed to copy_dict() for copying dct and reset_dct . Copy mode defaults to 'shallow' if readonly , otherwise to 'hybrid'. reset_dct : dict Dict to fall back to in case of resetting. If None, copies dct using reset_dct_copy_kwargs . reset_dct_copy_kwargs : dict Keyword arguments that override copy_kwargs for reset_dct . frozen_keys : bool Whether to deny updates to the keys of the config. Defaults to False. readonly : bool Whether to deny updates to the keys and values of the config. Defaults to False. nested : bool Whether to do operations recursively on each child dict. Such operations include copy, update, and merge. Disable to treat each child dict as a single value. Defaults to True. convert_dicts : bool or type Whether to convert child dicts to configs with the same configuration. This will trigger a waterfall reaction across all child dicts. Won't convert dicts that are already configs. Apart from boolean, you can set it to any subclass of Config to use it for construction. Requires nested to be True. Defaults to False. as_attrs : bool Whether to enable accessing dict keys via the dot notation. Enables autocompletion (but only during runtime!). Raises error in case of naming conflicts. Defaults to True if frozen or readonly , otherwise False. Defaults can be overridden with settings under config in settings . If another config is passed, its properties are copied over, but they can still be overridden with the arguments passed to the initializer. Note All arguments are applied only once during initialization. Superclasses Documented Pickleable PickleableDict builtins.dict Inherited members Documented.to_doc() Pickleable.load() Pickleable.save() PickleableDict.dumps() PickleableDict.loads() Subclasses AtomicConfig SettingsConfig as_attrs_ propertyWhether to enable accessing dict keys via dot notation. clear methodConfig . clear ( force = False ) Remove all items. convert_dicts_ propertyWhether to convert child dicts to configs with the same configuration. copy methodConfig . copy ( reset_dct_copy_kwargs = None , ** copy_kwargs ) Copy the instance in the same way it's done during initialization. copy_kwargs override Config.copy_kwargs_ and Config.reset_dct_copy_kwargs_ via merging. reset_dct_copy_kwargs override merged Config.reset_dct_copy_kwargs_ . copy_kwargs_ propertyParameters for copying dct . frozen_keys_ propertyWhether to deny updates to the keys and values of the config. load_update methodConfig . load_update ( fname , ** kwargs ) Load dumps from a file and update this instance. Note Updates both the config properties and dictionary. make_checkpoint methodConfig . make_checkpoint ( force = False , ** reset_dct_copy_kwargs ) Replace reset_dct by the current state. reset_dct_copy_kwargs override Config.reset_dct_copy_kwargs_ . merge_with methodConfig . merge_with ( other , nested = None , ** kwargs ) Merge with another dict into one single dict. See merge_dicts() . nested_ propertyWhether to do operations recursively on each child dict. pop methodConfig . pop ( k , v =< object object > , force = False ) Remove and return the pair by the key. popitem methodConfig . popitem ( force = False ) Remove and return some pair. readonly_ propertyWhether to deny any updates to the config. reset methodConfig . reset ( force = False , ** reset_dct_copy_kwargs ) Clears the config and updates it with the initial config. reset_dct_copy_kwargs override Config.reset_dct_copy_kwargs_ . reset_dct_ propertyDict to fall back to in case of resetting. reset_dct_copy_kwargs_ propertyParameters for copying reset_dct . to_dict methodConfig . to_dict ( nested = None ) Convert to dict. update methodConfig . update ( * args , nested = None , force = False , ** kwargs ) Update the config. See update_dict() . Configured classConfigured ( ** config ) Class with an initialization config. All subclasses of Configured are initialized using Config , which makes it easier to pickle. Settings are defined under configured in settings . Warning If any attribute has been overwritten that isn't listed in Configured.writeable_attrs , or if any Configured argument depends upon global defaults, their values won't be copied over. Make sure to pass them explicitly to make the saved & loaded / copied instance resilient to changes in globals. Superclasses Documented Pickleable Inherited members Documented.to_doc() Pickleable.dumps() Pickleable.load() Pickleable.loads() Pickleable.save() Subclasses ArrayWrapper Bar Box ColumnGrouper DataUpdater Gauge Heatmap Histogram QSAdapter Scatter TelegramBot Volume Wrapping config propertyInitialization config. copy methodConfigured . copy ( copy_mode = 'shallow' , nested = None , cls = None ) Create a new instance by copying the config. See Configured.replace() . replace methodConfigured . replace ( copy_mode_ = 'shallow' , nested_ = None , cls_ = None , ** new_config ) Create a new instance by copying and (optionally) changing the config. Warning This operation won't return a copy of the instance but a new instance initialized with the same config and writeable attributes (or their copy, depending on copy_mode ). update_config methodConfigured . update_config ( * args , ** kwargs ) Force-update the config. writeable_attrs propertySet of writeable attributes that will be saved/copied along with the config. Default classDefault ( value ) Class for wrapping default values. DumpTuple classDumpTuple ( cls , dumps ) DumpTuple(cls, dumps) Superclasses builtins.tuple cls method-wrapperAlias for field number 0 dumps method-wrapperAlias for field number 1 Pickleable classPickleable () Superclass that defines abstract properties and methods for pickle-able classes. Subclasses Configured PickleableDict dumps methodPickleable . dumps ( ** kwargs ) Pickle to bytes. load class methodPickleable . load ( fname , ** kwargs ) Load dumps from a file and create new instance. loads class methodPickleable . loads ( dumps , ** kwargs ) Unpickle from bytes. save methodPickleable . save ( fname , ** kwargs ) Save dumps to a file. PickleableDict classPickleableDict ( * args , ** kwargs ) Dict that may contain values of type Pickleable . Superclasses Pickleable builtins.dict Inherited members Pickleable.dumps() Pickleable.load() Pickleable.loads() Pickleable.save() Subclasses Config load_update methodPickleableDict . load_update ( fname , ** kwargs ) Load dumps from a file and update this instance. atomic_dict classatomic_dict ( * args , ** kwargs ) Dict that behaves like a single value when merging. Superclasses builtins.dict Subclasses AtomicConfig