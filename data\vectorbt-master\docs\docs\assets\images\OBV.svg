<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="350" style="" viewBox="0 0 700 350"><rect x="0" y="0" width="700" height="350" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-af37dc"><g class="clips"><clipPath id="clipaf37dcxyplot" class="plotclip"><rect width="626" height="274"/></clipPath><clipPath class="axesclip" id="clipaf37dcx"><rect x="44" y="0" width="626" height="350"/></clipPath><clipPath class="axesclip" id="clipaf37dcy"><rect x="0" y="46" width="700" height="274"/></clipPath><clipPath class="axesclip" id="clipaf37dcxy"><rect x="44" y="46" width="626" height="274"/></clipPath></g><g class="gradients"/></defs><g class="bglayer"><rect class="bg" x="44" y="46" width="626" height="274" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(47.4,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(152.87,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(254.93,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(360.4,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(462.47,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(567.93,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,270.43)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,231.71)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,192.99)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,154.26999999999998)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,115.55)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,76.84)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="yzl zl crisp" transform="translate(0,309.15)" d="M44,0h626" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(44, 46)" clip-path="url('#clipaf37dcxyplot')"><g class="scatterlayer mlayer"><g class="trace scatter trace33351c2e-e14c-48ff-9fe9-ece69b835386" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,259.9L6.8,254L10.21,256.8L13.61,260.3L17.01,256.36L23.82,249.1L27.22,253.22L30.62,249.04L34.02,252.8L37.42,256.72L40.83,252.92L44.23,256.59L47.63,252.53L54.43,245.07L57.84,248.26L68.04,236.96L71.45,241.16L74.85,244.74L78.25,241.03L85.05,248.58L88.46,244.44L91.86,240.22L95.26,243.84L102.07,235.84L105.47,239.35L108.87,235.41L112.27,227.16L115.67,218.29L119.08,225.36L132.68,199.2L136.09,204.9L139.49,198.89L142.89,205.3L149.7,195.82L153.1,191.79L156.5,196.55L159.9,192.05L173.51,171.67L176.91,176.99L183.72,165.19L187.12,171.79L190.52,177.73L193.92,171.22L197.33,176.29L200.73,171.33L204.13,176.65L207.53,171.27L214.34,160.31L217.74,153.06L221.14,146.26L224.54,151.99L227.95,158.08L231.35,151.1L238.15,138.67L241.55,131.15L244.96,119.98L248.36,130.73L258.57,96.25L261.97,109.09L265.37,120.73L268.77,129L272.17,118.97L275.58,128.21L282.38,147.51L285.78,138.04L299.39,98.23L302.79,107.61L306.2,116.7L309.6,128.02L313,118.2L316.4,126.91L319.8,119.06L323.21,127.58L326.61,137.11L330.01,128.69L333.41,136.23L336.82,128.82L343.62,141.64L347.02,134.41L350.42,141.03L353.83,133.66L364.03,111.64L367.43,102.6L370.84,96.58L374.24,102.71L381.04,89.78L384.45,81.8L387.85,70.18L391.25,62.05L394.65,54.59L398.05,44.96L401.46,27.49L404.86,42.97L408.26,29.39L411.66,40.97L418.47,62.9L421.87,50.89L425.27,38.97L428.67,49L432.08,58.23L435.48,50.07L442.28,33.48L445.68,22.57L452.49,46.66L455.89,37.55L462.7,54.4L466.1,44.58L469.5,53.93L472.9,44.41L476.3,34.66L479.71,42.69L483.11,34.86L486.51,41.5L496.72,61.47L500.12,55.34L506.92,67.47L510.33,62.15L513.73,67.49L517.13,62.13L534.14,29.93L537.54,20.69L540.95,29.84L544.35,21.24L547.75,13.7L551.15,20.8L554.55,27.82L557.96,21.71L564.76,33.45L568.16,41.19L574.97,24.5L578.37,29.83L585.17,18.59L588.58,24.42L595.38,38.58L598.78,32.52L605.59,43.99L608.99,36.85L615.79,49.38L619.2,55.98L626,46.28" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(47.4,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Mar 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(152.87,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Apr 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(254.93,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">May 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(360.4,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jun 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(462.47,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jul 2019</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(567.93,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Aug 2019</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,309.15)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0</text></g><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,270.43)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">100B</text></g><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,231.71)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">200B</text></g><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,192.99)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">300B</text></g><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,154.26999999999998)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">400B</text></g><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,115.55)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">500B</text></g><g class="ytick"><text text-anchor="end" x="43" y="4.199999999999999" transform="translate(0,76.84)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">600B</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-af37dc"><g class="clips"/><clipPath id="legendaf37dc"><rect width="71" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(599, 11.519999999999996)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="71" height="29" x="0" y="0"/><g class="scrollbox" transform="translate(0, 0)" clip-path="url('#legendaf37dc')"><g class="groups"><g class="traces" transform="translate(0, 14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">OBV</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="68.375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-ytitle"/></g></svg>