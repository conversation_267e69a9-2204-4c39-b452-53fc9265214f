<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="350" style="" viewBox="0 0 700 350"><rect x="0" y="0" width="700" height="350" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-086cc4"><g class="clips"><clipPath id="clip086cc4xyplot" class="plotclip"><rect width="640" height="274"/></clipPath><clipPath class="axesclip" id="clip086cc4x"><rect x="30" y="0" width="640" height="350"/></clipPath><clipPath class="axesclip" id="clip086cc4y"><rect x="0" y="46" width="700" height="274"/></clipPath><clipPath class="axesclip" id="clip086cc4xy"><rect x="30" y="46" width="640" height="274"/></clipPath></g><g class="gradients"/></defs><g class="bglayer"><rect class="bg" x="30" y="46" width="640" height="274" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(190,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(296.67,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(403.33,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(510,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(616.67,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,306.3)" d="M30,0h640" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,256.98)" d="M30,0h640" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,207.66)" d="M30,0h640" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,158.34)" d="M30,0h640" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,109.02000000000001)" d="M30,0h640" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,59.7)" d="M30,0h640" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="xzl zl crisp" transform="translate(83.33,0)" d="M0,46v274" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(30,46)" clip-path="url('#clip086cc4xyplot')"><g class="ohlclayer mlayer"><g class="trace ohlc" style="opacity: 1;"><path d="M21.33,161.66H53.33M53.33,112.34V210.98M85.33,161.66H53.33" style="fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/><path d="M128,112.34H160M160,63.02V161.66M192,112.34H160" style="fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/><path d="M234.67,63.02H266.67M266.67,13.7V112.34M298.67,63.02H266.67" style="fill: none; stroke: rgb(27, 158, 118); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/><path d="M341.33,112.34H373.33M373.33,63.02V161.66M405.33,112.34H373.33" style="fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/><path d="M448,161.66H480M480,112.34V210.98M512,161.66H480" style="fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/><path d="M554.67,210.98H586.67M586.67,161.66V260.3M618.67,210.98H586.67" style="fill: none; stroke: rgb(217, 95, 2); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g></g><g class="scatterlayer mlayer"><g class="trace scatter tracef678370a-335a-4763-9672-6614ac4c46b3" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point" transform="translate(53.33,161.66)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace68906b03-3c41-4995-88b0-12fbfcd90545" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(480,210.98)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(83.33,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(190,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">1</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(296.67,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">2</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(403.33,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">3</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(510,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">4</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(616.67,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">5</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="29" y="4.199999999999999" transform="translate(0,306.3)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">8</text></g><g class="ytick"><text text-anchor="end" x="29" y="4.199999999999999" transform="translate(0,256.98)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">9</text></g><g class="ytick"><text text-anchor="end" x="29" y="4.199999999999999" transform="translate(0,207.66)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">10</text></g><g class="ytick"><text text-anchor="end" x="29" y="4.199999999999999" transform="translate(0,158.34)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">11</text></g><g class="ytick"><text text-anchor="end" x="29" y="4.199999999999999" transform="translate(0,109.02000000000001)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">12</text></g><g class="ytick"><text text-anchor="end" x="29" y="4.199999999999999" transform="translate(0,59.7)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">13</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-086cc4"><g class="clips"/><clipPath id="legend086cc4"><rect width="224" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(446,11.519999999999996)"><rect class="bg" shape-rendering="crispEdges" width="224" height="29" x="0" y="0" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;"/><g class="scrollbox" transform="" clip-path="url('#legend086cc4')"><g class="groups"><g class="traces" transform="translate(0,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">OHLC</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="legendohlc" d="M15,0H0M8,6V0" transform="translate(20,0)" style="stroke-miterlimit: 1; fill: none; stroke-width: 2px; stroke: rgb(217, 95, 2); stroke-opacity: 1;"/><path class="legendohlc" d="M-15,0H0M-8,-6V0" transform="translate(20,0)" style="stroke-miterlimit: 1; fill: none; stroke-width: 2px; stroke: rgb(27, 158, 118); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="76.03125" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(78.53125,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Entry</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="74.640625" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(155.671875,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Exit</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="65.21875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" x="0" y="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-ytitle"/></g></svg>