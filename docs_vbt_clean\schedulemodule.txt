schedule_ moduleUtilities for scheduling jobs. AsyncJob classAsyncJob ( interval , scheduler = None ) A periodic job as used by :class: Scheduler . :param interval: A quantity of a certain time unit :param scheduler: The :class: Scheduler <Scheduler> instance that this job will register itself with once it has been fully configured in :meth: Job.do() . Every job runs at a given fixed time interval that is defined by: a :meth: time unit <Job.second> a quantity of time units defined by interval A job is usually created and returned by :meth: Scheduler.every method, which also defines its interval . Superclasses schedule.Job async_run methodAsyncJob . async_run () Async Job.run . AsyncScheduler classAsyncScheduler () Objects instantiated by the :class: Scheduler <Scheduler> are factories to create jobs, keep record of scheduled jobs and handle their execution. Superclasses schedule.Scheduler async_run_all methodAsyncScheduler . async_run_all ( delay_seconds = 0 ) Async Scheduler.run_all . async_run_pending methodAsyncScheduler . async_run_pending () Async Scheduler.run_pending . every methodAsyncScheduler . every ( interval = 1 ) Schedule a new periodic job of type AsyncJob . CancelledError classCancelledError ( * args , ** kwargs ) Thrown for the operation to be cancelled. Superclasses asyncio.exceptions.CancelledError builtins.BaseException ScheduleManager classScheduleManager ( scheduler = None ) Class that manages schedule.Scheduler . async_start methodScheduleManager . async_start ( sleep = 1 ) Async run pending jobs in a loop. async_task propertyCurrent async task. async_task_running propertyWhether the async task is running. done_callback methodScheduleManager . done_callback ( async_task ) Callback run when the async task is finished. every methodScheduleManager . every ( * args , to = None , tags = None ) Create a new job that runs every interval units of time. *args can include at most four different arguments: interval , unit , start_day , and at , in the strict order: interval : integer or datetime.timedelta unit : ScheduleManager.units start_day : ScheduleManager.weekdays at : string or datetime.time . See the package schedule for more details. Usage import datetime import pytz import vectorbt as vbt def job_func ( message = "I'm working..." ): ... print ( message ) my_manager = vbt . ScheduleManager () # add jobs my_manager . every () . do ( job_func , message = "Hello" ) Every 1 second do job_func(message='Hello') (last run: [never], next run: 2021-03-18 19:06:47) my_manager . every ( 10 , 'minutes' ) . do ( job_func ) Every 10 minutes do job_func() (last run: [never], next run: 2021-03-18 19:16:46) my_manager . every ( 'hour' ) . do ( job_func ) Every 1 hour do job_func() (last run: [never], next run: 2021-03-18 20:06:46) my_manager . every ( '10:30' ) . do ( job_func ) Every 1 day at 10:30:00 do job_func() (last run: [never], next run: 2021-03-19 10:30:00) my_manager . every ( 'day' , '10:30' ) . do ( job_func ) Every 1 day at 10:30:00 do job_func() (last run: [never], next run: 2021-03-19 10:30:00) my_manager . every ( 'day' , datetime . time ( 9 , 30 , tzinfo = pytz . utc )) . do ( job_func ) Every 1 day at 10:30:00 do job_func() (last run: [never], next run: 2021-03-19 10:30:00) my_manager . every ( 'monday' ) . do ( job_func ) Every 1 week do job_func() (last run: [never], next run: 2021-03-22 19:06:46) my_manager . every ( 'wednesday' , '13:15' ) . do ( job_func ) Every 1 week at 13:15:00 do job_func() (last run: [never], next run: 2021-03-24 13:15:00) my_manager . every ( 'minute' , ':17' ) . do ( job_func ) Every 1 minute at 00:00:17 do job_func() (last run: [never], next run: 2021-03-18 19:07:17) my_manager . start () You can still use the chained approach as done by schedule : my_manager . every () . minute . at ( ':17' ) . do ( job_func ) Every 1 minute at 00:00:17 do job_func() (last run: [never], next run: 2021-03-18 19:07:17) scheduler propertyScheduler. start methodScheduleManager . start ( sleep = 1 ) Run pending jobs in a loop. start_in_background methodScheduleManager . start_in_background ( ** kwargs ) Run ScheduleManager.async_start() in the background. stop methodScheduleManager . stop () Stop the async task. units class variableweekdays class variable