docs moduleUtilities for documentation. prepare_for_doc functionprepare_for_doc ( obj , replace = None , path = None ) Prepare object for use in documentation. to_doc functionto_doc ( obj , replace = None , path = None , ** kwargs ) Convert object to a JSON string. Documented classDocumented () Abstract class for documenting self. Note Won't get converted into a string in prepare_for_doc() . Subclasses Config Configured to_doc methodDocumented . to_doc ( ** kwargs ) Convert to a doc. SafeToStr classSafeToStr () Class that can be safely converted into a string in prepare_for_doc() . Subclasses Rep RepEval RepFunc Sub