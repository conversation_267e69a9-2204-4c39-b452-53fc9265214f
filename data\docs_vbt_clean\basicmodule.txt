basic moduleIndicators built with IndicatorFactory . You can access all the indicators either by vbt.* or vbt.indicators.* . import pandas as pd import vectorbt as vbt # vectorbt.indicators.basic.MA vbt . MA . run ( pd . Series ([ 1 , 2 , 3 ]), [ 2 , 3 ]) . ma ma_window 2 3 ma_ewm False False 0 NaN NaN 1 1.5 NaN 2 2.5 2.0 The advantage of these indicators over TA-Lib's is that they work primarily on 2-dimensional arrays and utilize caching, which makes them faster for matrices with huge number of columns. They also have plotting methods. Run for the examples below: import vectorbt as vbt from datetime import datetime start = '2019-03-01 UTC' # crypto is in UTC end = '2019-09-01 UTC' cols = [ 'Open' , 'High' , 'Low' , 'Close' , 'Volume' ] ohlcv = vbt . YFData . download ( "BTC-USD" , start = start , end = end ) . get ( cols ) ohlcv Open High Low \ Date 2019-03-01 00:00:00+00:00 3853.757080 3907.795410 3851.692383 2019-03-02 00:00:00+00:00 3855.318115 3874.607422 3832.127930 2019-03-03 00:00:00+00:00 3862.266113 3875.483643 3836.905762 ... ... ... ... 2019-08-30 00:00:00+00:00 9514.844727 9656.124023 9428.302734 2019-08-31 00:00:00+00:00 9597.539062 9673.220703 9531.799805 2019-09-01 00:00:00+00:00 9630.592773 9796.755859 9582.944336 Close Volume Date 2019-03-01 00:00:00+00:00 3859.583740 7661247975 2019-03-02 00:00:00+00:00 3864.415039 7578786076 2019-03-03 00:00:00+00:00 3847.175781 7253558152 ... ... ... 2019-08-30 00:00:00+00:00 9598.173828 13595263986 2019-08-31 00:00:00+00:00 9630.664062 11454806419 2019-09-01 00:00:00+00:00 9757.970703 11445355859 [185 rows x 5 columns] ohlcv . vbt . ohlcv . plot () ATR classATR ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Average True Range (ATR). The indicator provide an indication of the degree of price volatility. Strong moves, in either direction, are often accompanied by large ranges, or large True Ranges. See Average True Range - ATR . Note Uses Simple MA and Exponential MA as compared to Wilder. Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.indicators.basic.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.indicators.basic._ATR apply_func methodATR . apply_func ( high , low , close , window , ewm , adjust , tr , cache_dict ) Apply function for ATR . atr propertyOutput array. atr_above methodATR . atr_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where atr is above other . See combine_objs() . atr_below methodATR . atr_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where atr is below other . See combine_objs() . atr_crossed_above methodATR . atr_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where atr is crossed_above other . See combine_objs() . atr_crossed_below methodATR . atr_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where atr is crossed_below other . See combine_objs() . atr_equal methodATR . atr_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where atr is equal other . See combine_objs() . atr_stats methodATR . atr_stats ( * args , ** kwargs ) Stats of atr as generic. close methodInput array. close_above methodATR . close_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is above other . See combine_objs() . close_below methodATR . close_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is below other . See combine_objs() . close_crossed_above methodATR . close_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_above other . See combine_objs() . close_crossed_below methodATR . close_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_below other . See combine_objs() . close_equal methodATR . close_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is equal other . See combine_objs() . close_stats methodATR . close_stats ( * args , ** kwargs ) Stats of close as generic. custom_func methodIndicatorFactory . from_apply_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , col = None , flex_2d = None , return_cache = False , use_cache = None , use_ray = False , ** _kwargs ) Custom function that forwards inputs and parameters to apply_func . ewm_list propertyList of ewm values. high methodInput array. high_above methodATR . high_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is above other . See combine_objs() . high_below methodATR . high_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is below other . See combine_objs() . high_crossed_above methodATR . high_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is crossed_above other . See combine_objs() . high_crossed_below methodATR . high_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is crossed_below other . See combine_objs() . high_equal methodATR . high_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is equal other . See combine_objs() . high_stats methodATR . high_stats ( * args , ** kwargs ) Stats of high as generic. low methodInput array. low_above methodATR . low_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is above other . See combine_objs() . low_below methodATR . low_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is below other . See combine_objs() . low_crossed_above methodATR . low_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is crossed_above other . See combine_objs() . low_crossed_below methodATR . low_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is crossed_below other . See combine_objs() . low_equal methodATR . low_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is equal other . See combine_objs() . low_stats methodATR . low_stats ( * args , ** kwargs ) Stats of low as generic. plot method_ATR . plot ( column = None , tr_trace_kwargs = None , atr_trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Plot ATR.tr and ATR.atr . Args column : str Name of the column to plot. tr_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for ATR.tr . atr_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for ATR.atr . add_trace_kwargs : dict Keyword arguments passed to add_trace . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage vbt . ATR . run ( ohlcv [ 'High' ], ohlcv [ 'Low' ], ohlcv [ 'Close' ], 10 ) . plot () run class methodATR . run ( high , low , close , window = Default ( 14 ), ewm = Default ( True ), short_name = 'atr' , hide_params = None , hide_default = True , ** kwargs ) Run ATR indicator. Inputs: high , low , close Parameters: window , ewm Outputs: tr , atr Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodATR . run_combs ( high , low , close , window = Default ( 14 ), ewm = Default ( True ), r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple ATR indicators using function comb_func . Inputs: high , low , close Parameters: window , ewm Outputs: tr , atr comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to ATR.run() . tr propertyOutput array. tr_above methodATR . tr_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where tr is above other . See combine_objs() . tr_below methodATR . tr_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where tr is below other . See combine_objs() . tr_crossed_above methodATR . tr_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where tr is crossed_above other . See combine_objs() . tr_crossed_below methodATR . tr_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where tr is crossed_below other . See combine_objs() . tr_equal methodATR . tr_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where tr is equal other . See combine_objs() . tr_stats methodATR . tr_stats ( * args , ** kwargs ) Stats of tr as generic. window_list propertyList of window values. BBANDS classBBANDS ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Bollinger Bands (BBANDS). A Bollinger Band® is a technical analysis tool defined by a set of lines plotted two standard deviations (positively and negatively) away from a simple moving average (SMA) of the security's price, but can be adjusted to user preferences. See Bollinger Band® . Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.indicators.basic.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.indicators.basic._BBANDS alpha_list propertyList of alpha values. apply_func methodBBANDS . apply_func ( close , window , ewm , alpha , adjust , ddof , ma_cache_dict , mstd_cache_dict ) Apply function for BBANDS . bandwidth methodCustom property. bandwidth_above methodBBANDS . bandwidth_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where bandwidth is above other . See combine_objs() . bandwidth_below methodBBANDS . bandwidth_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where bandwidth is below other . See combine_objs() . bandwidth_crossed_above methodBBANDS . bandwidth_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where bandwidth is crossed_above other . See combine_objs() . bandwidth_crossed_below methodBBANDS . bandwidth_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where bandwidth is crossed_below other . See combine_objs() . bandwidth_equal methodBBANDS . bandwidth_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where bandwidth is equal other . See combine_objs() . bandwidth_stats methodBBANDS . bandwidth_stats ( * args , ** kwargs ) Stats of bandwidth as generic. close methodInput array. close_above methodBBANDS . close_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is above other . See combine_objs() . close_below methodBBANDS . close_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is below other . See combine_objs() . close_crossed_above methodBBANDS . close_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_above other . See combine_objs() . close_crossed_below methodBBANDS . close_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_below other . See combine_objs() . close_equal methodBBANDS . close_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is equal other . See combine_objs() . close_stats methodBBANDS . close_stats ( * args , ** kwargs ) Stats of close as generic. custom_func methodIndicatorFactory . from_apply_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , col = None , flex_2d = None , return_cache = False , use_cache = None , use_ray = False , ** _kwargs ) Custom function that forwards inputs and parameters to apply_func . ewm_list propertyList of ewm values. lower propertyOutput array. lower_above methodBBANDS . lower_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where lower is above other . See combine_objs() . lower_below methodBBANDS . lower_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where lower is below other . See combine_objs() . lower_crossed_above methodBBANDS . lower_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where lower is crossed_above other . See combine_objs() . lower_crossed_below methodBBANDS . lower_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where lower is crossed_below other . See combine_objs() . lower_equal methodBBANDS . lower_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where lower is equal other . See combine_objs() . lower_stats methodBBANDS . lower_stats ( * args , ** kwargs ) Stats of lower as generic. middle propertyOutput array. middle_above methodBBANDS . middle_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where middle is above other . See combine_objs() . middle_below methodBBANDS . middle_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where middle is below other . See combine_objs() . middle_crossed_above methodBBANDS . middle_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where middle is crossed_above other . See combine_objs() . middle_crossed_below methodBBANDS . middle_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where middle is crossed_below other . See combine_objs() . middle_equal methodBBANDS . middle_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where middle is equal other . See combine_objs() . middle_stats methodBBANDS . middle_stats ( * args , ** kwargs ) Stats of middle as generic. percent_b methodCustom property. percent_b_above methodBBANDS . percent_b_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_b is above other . See combine_objs() . percent_b_below methodBBANDS . percent_b_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_b is below other . See combine_objs() . percent_b_crossed_above methodBBANDS . percent_b_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_b is crossed_above other . See combine_objs() . percent_b_crossed_below methodBBANDS . percent_b_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_b is crossed_below other . See combine_objs() . percent_b_equal methodBBANDS . percent_b_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_b is equal other . See combine_objs() . percent_b_stats methodBBANDS . percent_b_stats ( * args , ** kwargs ) Stats of percent_b as generic. plot method_BBANDS . plot ( column = None , plot_close = True , close_trace_kwargs = None , middle_trace_kwargs = None , upper_trace_kwargs = None , lower_trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Plot BBANDS.middle , BBANDS.upper and BBANDS.lower against BBANDS.close . Args column : str Name of the column to plot. plot_close : bool Whether to plot MA.close . close_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for BBANDS.close . middle_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for BBANDS.middle . upper_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for BBANDS.upper . lower_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for BBANDS.lower . add_trace_kwargs : dict Keyword arguments passed to add_trace . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage vbt . BBANDS . run ( ohlcv [ 'Close' ]) . plot () run class methodBBANDS . run ( close , window = Default ( 20 ), ewm = Default ( False ), alpha = Default ( 2 ), short_name = 'bb' , hide_params = None , hide_default = True , ** kwargs ) Run BBANDS indicator. Inputs: close Parameters: window , ewm , alpha Outputs: middle , upper , lower Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodBBANDS . run_combs ( close , window = Default ( 20 ), ewm = Default ( False ), alpha = Default ( 2 ), r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple BBANDS indicators using function comb_func . Inputs: close Parameters: window , ewm , alpha Outputs: middle , upper , lower comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to BBANDS.run() . upper propertyOutput array. upper_above methodBBANDS . upper_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where upper is above other . See combine_objs() . upper_below methodBBANDS . upper_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where upper is below other . See combine_objs() . upper_crossed_above methodBBANDS . upper_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where upper is crossed_above other . See combine_objs() . upper_crossed_below methodBBANDS . upper_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where upper is crossed_below other . See combine_objs() . upper_equal methodBBANDS . upper_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where upper is equal other . See combine_objs() . upper_stats methodBBANDS . upper_stats ( * args , ** kwargs ) Stats of upper as generic. window_list propertyList of window values. MA classMA ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Moving Average (MA). A moving average is a widely used indicator in technical analysis that helps smooth out price action by filtering out the “noise” from random short-term price fluctuations. See Moving Average (MA) . Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.indicators.basic.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.indicators.basic._MA apply_func methodMA . apply_func ( close , window , ewm , adjust , cache_dict ) Apply function for MA . close methodInput array. close_above methodMA . close_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is above other . See combine_objs() . close_below methodMA . close_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is below other . See combine_objs() . close_crossed_above methodMA . close_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_above other . See combine_objs() . close_crossed_below methodMA . close_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_below other . See combine_objs() . close_equal methodMA . close_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is equal other . See combine_objs() . close_stats methodMA . close_stats ( * args , ** kwargs ) Stats of close as generic. custom_func methodIndicatorFactory . from_apply_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , col = None , flex_2d = None , return_cache = False , use_cache = None , use_ray = False , ** _kwargs ) Custom function that forwards inputs and parameters to apply_func . ewm_list propertyList of ewm values. ma propertyOutput array. ma_above methodMA . ma_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ma is above other . See combine_objs() . ma_below methodMA . ma_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ma is below other . See combine_objs() . ma_crossed_above methodMA . ma_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ma is crossed_above other . See combine_objs() . ma_crossed_below methodMA . ma_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ma is crossed_below other . See combine_objs() . ma_equal methodMA . ma_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where ma is equal other . See combine_objs() . ma_stats methodMA . ma_stats ( * args , ** kwargs ) Stats of ma as generic. plot method_MA . plot ( column = None , plot_close = True , close_trace_kwargs = None , ma_trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Plot MA.ma against MA.close . Args column : str Name of the column to plot. plot_close : bool Whether to plot MA.close . close_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for MA.close . ma_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for MA.ma . add_trace_kwargs : dict Keyword arguments passed to add_trace . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage vbt . MA . run ( ohlcv [ 'Close' ], 10 ) . plot () run class methodMA . run ( close , window , ewm = Default ( False ), short_name = 'ma' , hide_params = None , hide_default = True , ** kwargs ) Run MA indicator. Inputs: close Parameters: window , ewm Outputs: ma Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodMA . run_combs ( close , window , ewm = Default ( False ), r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple MA indicators using function comb_func . Inputs: close Parameters: window , ewm Outputs: ma comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to MA.run() . window_list propertyList of window values. MACD classMACD ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Moving Average Convergence Divergence (MACD). Is a trend-following momentum indicator that shows the relationship between two moving averages of prices. See Moving Average Convergence Divergence – MACD . Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.indicators.basic.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.indicators.basic._MACD apply_func methodMACD . apply_func ( close , fast_window , slow_window , signal_window , macd_ewm , signal_ewm , adjust , cache_dict ) Apply function for MACD . close methodInput array. close_above methodMACD . close_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is above other . See combine_objs() . close_below methodMACD . close_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is below other . See combine_objs() . close_crossed_above methodMACD . close_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_above other . See combine_objs() . close_crossed_below methodMACD . close_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_below other . See combine_objs() . close_equal methodMACD . close_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is equal other . See combine_objs() . close_stats methodMACD . close_stats ( * args , ** kwargs ) Stats of close as generic. custom_func methodIndicatorFactory . from_apply_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , col = None , flex_2d = None , return_cache = False , use_cache = None , use_ray = False , ** _kwargs ) Custom function that forwards inputs and parameters to apply_func . fast_window_list propertyList of fast_window values. hist methodCustom property. hist_above methodMACD . hist_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where hist is above other . See combine_objs() . hist_below methodMACD . hist_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where hist is below other . See combine_objs() . hist_crossed_above methodMACD . hist_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where hist is crossed_above other . See combine_objs() . hist_crossed_below methodMACD . hist_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where hist is crossed_below other . See combine_objs() . hist_equal methodMACD . hist_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where hist is equal other . See combine_objs() . hist_stats methodMACD . hist_stats ( * args , ** kwargs ) Stats of hist as generic. macd propertyOutput array. macd_above methodMACD . macd_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where macd is above other . See combine_objs() . macd_below methodMACD . macd_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where macd is below other . See combine_objs() . macd_crossed_above methodMACD . macd_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where macd is crossed_above other . See combine_objs() . macd_crossed_below methodMACD . macd_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where macd is crossed_below other . See combine_objs() . macd_equal methodMACD . macd_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where macd is equal other . See combine_objs() . macd_ewm_list propertyList of macd_ewm values. macd_stats methodMACD . macd_stats ( * args , ** kwargs ) Stats of macd as generic. plot method_MACD . plot ( column = None , macd_trace_kwargs = None , signal_trace_kwargs = None , hist_trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Plot MACD.macd , MACD.signal and MACD.hist . Args column : str Name of the column to plot. macd_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for MACD.macd . signal_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for MACD.signal . hist_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Bar for MACD.hist . add_trace_kwargs : dict Keyword arguments passed to add_trace . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage vbt . MACD . run ( ohlcv [ 'Close' ]) . plot () run class methodMACD . run ( close , fast_window = Default ( 12 ), slow_window = Default ( 26 ), signal_window = Default ( 9 ), macd_ewm = Default ( False ), signal_ewm = Default ( False ), short_name = 'macd' , hide_params = None , hide_default = True , ** kwargs ) Run MACD indicator. Inputs: close Parameters: fast_window , slow_window , signal_window , macd_ewm , signal_ewm Outputs: macd , signal Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodMACD . run_combs ( close , fast_window = Default ( 12 ), slow_window = Default ( 26 ), signal_window = Default ( 9 ), macd_ewm = Default ( False ), signal_ewm = Default ( False ), r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple MACD indicators using function comb_func . Inputs: close Parameters: fast_window , slow_window , signal_window , macd_ewm , signal_ewm Outputs: macd , signal comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to MACD.run() . signal propertyOutput array. signal_above methodMACD . signal_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where signal is above other . See combine_objs() . signal_below methodMACD . signal_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where signal is below other . See combine_objs() . signal_crossed_above methodMACD . signal_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where signal is crossed_above other . See combine_objs() . signal_crossed_below methodMACD . signal_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where signal is crossed_below other . See combine_objs() . signal_equal methodMACD . signal_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where signal is equal other . See combine_objs() . signal_ewm_list propertyList of signal_ewm values. signal_stats methodMACD . signal_stats ( * args , ** kwargs ) Stats of signal as generic. signal_window_list propertyList of signal_window values. slow_window_list propertyList of slow_window values. MSTD classMSTD ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Moving Standard Deviation (MSTD). Standard deviation is an indicator that measures the size of an assets recent price moves in order to predict how volatile the price may be in the future. Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.indicators.basic.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.indicators.basic._MSTD apply_func methodMSTD . apply_func ( close , window , ewm , adjust , ddof , cache_dict ) Apply function for MSTD . close methodInput array. close_above methodMSTD . close_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is above other . See combine_objs() . close_below methodMSTD . close_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is below other . See combine_objs() . close_crossed_above methodMSTD . close_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_above other . See combine_objs() . close_crossed_below methodMSTD . close_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_below other . See combine_objs() . close_equal methodMSTD . close_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is equal other . See combine_objs() . close_stats methodMSTD . close_stats ( * args , ** kwargs ) Stats of close as generic. custom_func methodIndicatorFactory . from_apply_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , col = None , flex_2d = None , return_cache = False , use_cache = None , use_ray = False , ** _kwargs ) Custom function that forwards inputs and parameters to apply_func . ewm_list propertyList of ewm values. mstd propertyOutput array. mstd_above methodMSTD . mstd_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where mstd is above other . See combine_objs() . mstd_below methodMSTD . mstd_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where mstd is below other . See combine_objs() . mstd_crossed_above methodMSTD . mstd_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where mstd is crossed_above other . See combine_objs() . mstd_crossed_below methodMSTD . mstd_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where mstd is crossed_below other . See combine_objs() . mstd_equal methodMSTD . mstd_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where mstd is equal other . See combine_objs() . mstd_stats methodMSTD . mstd_stats ( * args , ** kwargs ) Stats of mstd as generic. plot method_MSTD . plot ( column = None , mstd_trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Plot MSTD.mstd . Args column : str Name of the column to plot. mstd_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for MSTD.mstd . add_trace_kwargs : dict Keyword arguments passed to add_trace . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage vbt . MSTD . run ( ohlcv [ 'Close' ], 10 ) . plot () run class methodMSTD . run ( close , window , ewm = Default ( False ), short_name = 'mstd' , hide_params = None , hide_default = True , ** kwargs ) Run MSTD indicator. Inputs: close Parameters: window , ewm Outputs: mstd Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodMSTD . run_combs ( close , window , ewm = Default ( False ), r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple MSTD indicators using function comb_func . Inputs: close Parameters: window , ewm Outputs: mstd comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to MSTD.run() . window_list propertyList of window values. OBV classOBV ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) On-balance volume (OBV). It relates price and volume in the stock market. OBV is based on a cumulative total volume. See On-Balance Volume (OBV) . Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.indicators.basic.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.run_combs() IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.indicators.basic._OBV close methodInput array. close_above methodOBV . close_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is above other . See combine_objs() . close_below methodOBV . close_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is below other . See combine_objs() . close_crossed_above methodOBV . close_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_above other . See combine_objs() . close_crossed_below methodOBV . close_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_below other . See combine_objs() . close_equal methodOBV . close_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is equal other . See combine_objs() . close_stats methodOBV . close_stats ( * args , ** kwargs ) Stats of close as generic. custom_func methodOBV . custom_func ( close , volume_ts ) Custom calculation function for OBV . obv propertyOutput array. obv_above methodOBV . obv_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where obv is above other . See combine_objs() . obv_below methodOBV . obv_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where obv is below other . See combine_objs() . obv_crossed_above methodOBV . obv_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where obv is crossed_above other . See combine_objs() . obv_crossed_below methodOBV . obv_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where obv is crossed_below other . See combine_objs() . obv_equal methodOBV . obv_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where obv is equal other . See combine_objs() . obv_stats methodOBV . obv_stats ( * args , ** kwargs ) Stats of obv as generic. plot method_OBV . plot ( column = None , obv_trace_kwargs = None , add_trace_kwargs = None , fig = None , ** layout_kwargs ) Plot OBV.obv . Args column : str Name of the column to plot. obv_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for OBV.obv . add_trace_kwargs : dict Keyword arguments passed to add_trace . fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage vbt . OBV . run ( ohlcv [ 'Close' ], ohlcv [ 'Volume' ]) . plot () run class methodOBV . run ( close , volume , short_name = 'obv' , hide_params = None , hide_default = True , ** kwargs ) Run OBV indicator. Inputs: close , volume Outputs: obv Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . volume methodInput array. volume_above methodOBV . volume_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where volume is above other . See combine_objs() . volume_below methodOBV . volume_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where volume is below other . See combine_objs() . volume_crossed_above methodOBV . volume_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where volume is crossed_above other . See combine_objs() . volume_crossed_below methodOBV . volume_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where volume is crossed_below other . See combine_objs() . volume_equal methodOBV . volume_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where volume is equal other . See combine_objs() . volume_stats methodOBV . volume_stats ( * args , ** kwargs ) Stats of volume as generic. RSI classRSI ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Relative Strength Index (RSI). Compares the magnitude of recent gains and losses over a specified time period to measure speed and change of price movements of a security. It is primarily used to attempt to identify overbought or oversold conditions in the trading of an asset. See Relative Strength Index (RSI) . Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.indicators.basic.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.indicators.basic._RSI apply_func methodRSI . apply_func ( close , window , ewm , adjust , cache_dict ) Apply function for RSI . close methodInput array. close_above methodRSI . close_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is above other . See combine_objs() . close_below methodRSI . close_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is below other . See combine_objs() . close_crossed_above methodRSI . close_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_above other . See combine_objs() . close_crossed_below methodRSI . close_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_below other . See combine_objs() . close_equal methodRSI . close_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is equal other . See combine_objs() . close_stats methodRSI . close_stats ( * args , ** kwargs ) Stats of close as generic. custom_func methodIndicatorFactory . from_apply_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , col = None , flex_2d = None , return_cache = False , use_cache = None , use_ray = False , ** _kwargs ) Custom function that forwards inputs and parameters to apply_func . ewm_list propertyList of ewm values. plot method_RSI . plot ( column = None , levels = ( 30 , 70 ), rsi_trace_kwargs = None , add_trace_kwargs = None , xref = 'x' , yref = 'y' , fig = None , ** layout_kwargs ) Plot RSI.rsi . Args column : str Name of the column to plot. levels : tuple Two extremes: bottom and top. rsi_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for RSI.rsi . add_trace_kwargs : dict Keyword arguments passed to add_trace . xref : str X coordinate axis. yref : str Y coordinate axis. fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage vbt . RSI . run ( ohlcv [ 'Close' ]) . plot () rsi propertyOutput array. rsi_above methodRSI . rsi_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where rsi is above other . See combine_objs() . rsi_below methodRSI . rsi_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where rsi is below other . See combine_objs() . rsi_crossed_above methodRSI . rsi_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where rsi is crossed_above other . See combine_objs() . rsi_crossed_below methodRSI . rsi_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where rsi is crossed_below other . See combine_objs() . rsi_equal methodRSI . rsi_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where rsi is equal other . See combine_objs() . rsi_stats methodRSI . rsi_stats ( * args , ** kwargs ) Stats of rsi as generic. run class methodRSI . run ( close , window = Default ( 14 ), ewm = Default ( False ), short_name = 'rsi' , hide_params = None , hide_default = True , ** kwargs ) Run RSI indicator. Inputs: close Parameters: window , ewm Outputs: rsi Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodRSI . run_combs ( close , window = Default ( 14 ), ewm = Default ( False ), r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple RSI indicators using function comb_func . Inputs: close Parameters: window , ewm Outputs: rsi comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to RSI.run() . window_list propertyList of window values. STOCH classSTOCH ( wrapper , input_list , input_mapper , in_output_list , output_list , param_list , mapper_list , short_name , level_names ) Stochastic Oscillator (STOCH). A stochastic oscillator is a momentum indicator comparing a particular closing price of a security to a range of its prices over a certain period of time. It is used to generate overbought and oversold trading signals, utilizing a 0-100 bounded range of values. See Stochastic Oscillator . Superclasses AttrResolver Configured Documented IndexingBase IndicatorBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping vectorbt.indicators.basic.ParamIndexer Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() IndicatorBase.config IndicatorBase.iloc IndicatorBase.in_output_names IndicatorBase.indexing_func() IndicatorBase.indexing_kwargs IndicatorBase.input_names IndicatorBase.level_names IndicatorBase.loc IndicatorBase.output_flags IndicatorBase.output_names IndicatorBase.param_names IndicatorBase.plots_defaults IndicatorBase.self_aliases IndicatorBase.short_name IndicatorBase.stats_defaults IndicatorBase.wrapper IndicatorBase.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses vectorbt.indicators.basic._STOCH apply_func methodSTOCH . apply_func ( high , low , close , k_window , d_window , d_ewm , adjust , cache_dict ) Apply function for STOCH . close methodInput array. close_above methodSTOCH . close_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is above other . See combine_objs() . close_below methodSTOCH . close_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is below other . See combine_objs() . close_crossed_above methodSTOCH . close_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_above other . See combine_objs() . close_crossed_below methodSTOCH . close_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is crossed_below other . See combine_objs() . close_equal methodSTOCH . close_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where close is equal other . See combine_objs() . close_stats methodSTOCH . close_stats ( * args , ** kwargs ) Stats of close as generic. custom_func methodIndicatorFactory . from_apply_func .< locals >. custom_func ( input_list , in_output_list , param_list , * args , input_shape = None , col = None , flex_2d = None , return_cache = False , use_cache = None , use_ray = False , ** _kwargs ) Custom function that forwards inputs and parameters to apply_func . d_ewm_list propertyList of d_ewm values. d_window_list propertyList of d_window values. high methodInput array. high_above methodSTOCH . high_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is above other . See combine_objs() . high_below methodSTOCH . high_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is below other . See combine_objs() . high_crossed_above methodSTOCH . high_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is crossed_above other . See combine_objs() . high_crossed_below methodSTOCH . high_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is crossed_below other . See combine_objs() . high_equal methodSTOCH . high_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where high is equal other . See combine_objs() . high_stats methodSTOCH . high_stats ( * args , ** kwargs ) Stats of high as generic. k_window_list propertyList of k_window values. low methodInput array. low_above methodSTOCH . low_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is above other . See combine_objs() . low_below methodSTOCH . low_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is below other . See combine_objs() . low_crossed_above methodSTOCH . low_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is crossed_above other . See combine_objs() . low_crossed_below methodSTOCH . low_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is crossed_below other . See combine_objs() . low_equal methodSTOCH . low_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where low is equal other . See combine_objs() . low_stats methodSTOCH . low_stats ( * args , ** kwargs ) Stats of low as generic. percent_d propertyOutput array. percent_d_above methodSTOCH . percent_d_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_d is above other . See combine_objs() . percent_d_below methodSTOCH . percent_d_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_d is below other . See combine_objs() . percent_d_crossed_above methodSTOCH . percent_d_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_d is crossed_above other . See combine_objs() . percent_d_crossed_below methodSTOCH . percent_d_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_d is crossed_below other . See combine_objs() . percent_d_equal methodSTOCH . percent_d_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_d is equal other . See combine_objs() . percent_d_stats methodSTOCH . percent_d_stats ( * args , ** kwargs ) Stats of percent_d as generic. percent_k propertyOutput array. percent_k_above methodSTOCH . percent_k_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_k is above other . See combine_objs() . percent_k_below methodSTOCH . percent_k_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_k is below other . See combine_objs() . percent_k_crossed_above methodSTOCH . percent_k_crossed_above ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_k is crossed_above other . See combine_objs() . percent_k_crossed_below methodSTOCH . percent_k_crossed_below ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_k is crossed_below other . See combine_objs() . percent_k_equal methodSTOCH . percent_k_equal ( other , level_name = None , allow_multiple = True , ** kwargs ) Return True for each element where percent_k is equal other . See combine_objs() . percent_k_stats methodSTOCH . percent_k_stats ( * args , ** kwargs ) Stats of percent_k as generic. plot method_STOCH . plot ( column = None , levels = ( 30 , 70 ), percent_k_trace_kwargs = None , percent_d_trace_kwargs = None , shape_kwargs = None , add_trace_kwargs = None , xref = 'x' , yref = 'y' , fig = None , ** layout_kwargs ) Plot STOCH.percent_k and STOCH.percent_d . Args column : str Name of the column to plot. levels : tuple Two extremes: bottom and top. percent_k_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for STOCH.percent_k . percent_d_trace_kwargs : dict Keyword arguments passed to plotly.graph_objects.Scatter for STOCH.percent_d . shape_kwargs : dict Keyword arguments passed to Figure or FigureWidget.add_shape for zone between levels. add_trace_kwargs : dict Keyword arguments passed to add_trace . xref : str X coordinate axis. yref : str Y coordinate axis. fig : Figure or FigureWidget Figure to add traces to. **layout_kwargs Keyword arguments for layout. Usage vbt . STOCH . run ( ohlcv [ 'High' ], ohlcv [ 'Low' ], ohlcv [ 'Close' ]) . plot () run class methodSTOCH . run ( high , low , close , k_window = Default ( 14 ), d_window = Default ( 3 ), d_ewm = Default ( False ), short_name = 'stoch' , hide_params = None , hide_default = True , ** kwargs ) Run STOCH indicator. Inputs: high , low , close Parameters: k_window , d_window , d_ewm Outputs: percent_k , percent_d Pass a list of parameter names as hide_params to hide their column levels. Set hide_default to False to show the column levels of the parameters with a default value. Other keyword arguments are passed to run_pipeline() . run_combs class methodSTOCH . run_combs ( high , low , close , k_window = Default ( 14 ), d_window = Default ( 3 ), d_ewm = Default ( False ), r = 2 , param_product = False , comb_func = itertools . combinations , run_unique = True , short_names = None , hide_params = None , hide_default = True , ** kwargs ) Create a combination of multiple STOCH indicators using function comb_func . Inputs: high , low , close Parameters: k_window , d_window , d_ewm Outputs: percent_k , percent_d comb_func must accept an iterable of parameter tuples and r . Also accepts all combinatoric iterators from itertools such as itertools.combinations . Pass r to specify how many indicators to run. Pass short_names to specify the short name for each indicator. Set run_unique to True to first compute raw outputs for all parameters, and then use them to build each indicator (faster). Other keyword arguments are passed to STOCH.run() .