<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="350" viewBox="0 0 700 350"><rect x="0" y="0" width="700" height="350" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-188f6a"><g class="clips"><clipPath id="clip188f6axyplot" class="plotclip"><rect width="630" height="290"/></clipPath><clipPath class="axesclip" id="clip188f6ax"><rect x="40" y="0" width="630" height="350"/></clipPath><clipPath class="axesclip" id="clip188f6ay"><rect x="0" y="30" width="700" height="290"/></clipPath><clipPath class="axesclip" id="clip188f6axy"><rect x="40" y="30" width="630" height="290"/></clipPath></g><g class="gradients"/><g class="patterns"/></defs><g class="bglayer"><rect class="bg" x="40" y="30" width="630" height="290" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(167.26999999999998,0)" d="M0,30v290" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(294.55,0)" d="M0,30v290" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(421.82,0)" d="M0,30v290" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(549.0899999999999,0)" d="M0,30v290" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,316.18)" d="M40,0h630" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,279.36)" d="M40,0h630" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,242.54)" d="M40,0h630" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,205.72)" d="M40,0h630" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,168.9)" d="M40,0h630" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,132.07999999999998)" d="M40,0h630" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,95.26)" d="M40,0h630" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,58.44)" d="M40,0h630" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="xzl zl crisp" transform="translate(40,0)" d="M0,30v290" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(40,30)" clip-path="url(#clip188f6axyplot)"><g class="scatterlayer mlayer"><g class="trace scatter trace39915c3a-946f-4369-b3d7-54d04d413bc8" style="stroke-miterlimit: 2;"><g class="fills"><g><path class="js-fill" d="M0,141.13L6.36,135.64L12.73,134.08L19.09,128.83L25.45,119.82L31.82,104.31L38.18,104.62L44.55,108.85L50.91,108.8L57.27,114.69L63.64,112.56L70,117.6L76.36,123.9L82.73,124.42L89.09,113.68L95.45,106.46L101.82,109.54L108.18,110.45L114.55,101.91L120.91,110.19L127.27,109.83L133.64,120.37L140,108.16L146.36,104.09L152.73,112.4L159.09,112.12L165.45,110.63L171.82,113.18L178.18,120.11L184.55,115.83L190.91,115.11L197.27,107.61L203.64,105.42L210,93.59L216.36,85.99L222.73,89.83L229.09,91.38L235.45,84.43L241.82,94.8L248.18,99.74L254.55,84.56L260.91,71.27L267.27,63.84L273.64,53.06L280,25.39L286.36,15.57L292.73,23.32L299.09,19.04L305.45,28.3L311.82,30.52L318.18,34.6L324.55,44.22L330.91,49.94L337.27,52.59L343.64,45.13L350,48.5L356.36,46.32L362.73,50.88L369.09,61.98L375.45,67.93L381.82,63.18L388.18,90.42L394.55,93.34L400.91,90.45L407.27,110.78L413.64,113.19L420,110.98L426.36,112L432.73,111.03L445.45,95.2L451.82,78.15L458.18,77.28L464.55,65.62L470.91,60.76L477.27,49.23L483.64,29.11L490,17.2L496.36,27.65L502.73,29.2L509.09,17.02L515.45,14.5L521.82,21.45L528.18,37.98L534.55,38.03L540.91,43.91L553.64,65.96L560,50.9L572.73,58.48L579.09,58.16L585.45,64.53L591.82,63.11L598.18,65.49L604.55,53.87L610.91,49.68L617.27,47.2L623.64,60.09L630,66.24L630,267L623.64,268.3L617.27,273.18L610.91,275.5L604.55,265.49L598.18,260.9L591.82,261.52L585.45,261.15L579.09,255.84L572.73,252.31L566.36,259.03L560,257.16L553.64,257.03L547.27,249.51L540.91,247.8L534.55,249.72L528.18,237.57L521.82,242.35L515.45,224.16L509.09,228.28L502.73,225.8L490,229.69L483.64,222.28L477.27,225.61L470.91,216.57L464.55,224.3L458.18,229.73L451.82,229.68L445.45,236.5L439.09,236.34L432.73,233.24L426.36,231.41L420,224.17L413.64,220.81L407.27,230.05L400.91,234.99L394.55,224.39L388.18,224.39L381.82,208.92L375.45,215.7L369.09,216.16L362.73,207.01L356.36,195.33L350,180.92L343.64,170.47L337.27,168.38L330.91,169.74L324.55,163.74L318.18,157.55L311.82,158.29L305.45,146.3L299.09,154.84L292.73,157.08L286.36,154.36L280,156.19L273.64,148.94L267.27,160.46L254.55,154.41L248.18,166.77L241.82,173.13L235.45,180.03L229.09,183.98L222.73,176.54L216.36,173.57L210,165.45L203.64,163L197.27,169.8L190.91,157.49L184.55,163.55L178.18,165.22L171.82,145.02L165.45,148.49L159.09,152.3L152.73,170.17L146.36,170.23L140,169.3L133.64,177.44L127.27,169.64L120.91,168.98L114.55,178.68L108.18,179.24L101.82,164.76L95.45,162.85L89.09,166.41L82.73,167.68L76.36,162.24L70,152.73L63.64,147.55L57.27,157.96L50.91,155.28L44.55,159.52L38.18,146.11L31.82,141.05L25.45,142.79L19.09,150.62L12.73,140.77L6.36,145.68L0,148.84Z" style="fill: rgb(0, 128, 0); fill-opacity: 0.3; stroke-width: 0;"/></g></g><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,148.84L6.36,145.68L12.73,140.77L19.09,150.62L25.45,142.79L31.82,141.05L38.18,146.11L44.55,159.52L50.91,155.28L57.27,157.96L63.64,147.55L70,152.73L76.36,162.24L82.73,167.68L89.09,166.41L95.45,162.85L101.82,164.76L108.18,179.24L114.55,178.68L120.91,168.98L127.27,169.64L133.64,177.44L140,169.3L146.36,170.23L152.73,170.17L159.09,152.3L165.45,148.49L171.82,145.02L178.18,165.22L184.55,163.55L190.91,157.49L197.27,169.8L203.64,163L210,165.45L216.36,173.57L222.73,176.54L229.09,183.98L235.45,180.03L241.82,173.13L248.18,166.77L254.55,154.41L267.27,160.46L273.64,148.94L280,156.19L286.36,154.36L292.73,157.08L299.09,154.84L305.45,146.3L311.82,158.29L318.18,157.55L324.55,163.74L330.91,169.74L337.27,168.38L343.64,170.47L350,180.92L356.36,195.33L362.73,207.01L369.09,216.16L375.45,215.7L381.82,208.92L388.18,224.39L394.55,224.39L400.91,234.99L407.27,230.05L413.64,220.81L420,224.17L426.36,231.41L432.73,233.24L439.09,236.34L445.45,236.5L451.82,229.68L458.18,229.73L464.55,224.3L470.91,216.57L477.27,225.61L483.64,222.28L490,229.69L502.73,225.8L509.09,228.28L515.45,224.16L521.82,242.35L528.18,237.57L534.55,249.72L540.91,247.8L547.27,249.51L553.64,257.03L560,257.16L566.36,259.03L572.73,252.31L579.09,255.84L585.45,261.15L591.82,261.52L598.18,260.9L604.55,265.49L610.91,275.5L617.27,273.18L623.64,268.3L630,267" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter trace20692fe9-bc9d-48a0-8ea9-799338e376db" style="stroke-miterlimit: 2;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,141.13L6.36,135.64L12.73,134.08L19.09,128.83L25.45,119.82L31.82,104.31L38.18,104.62L44.55,108.85L50.91,108.8L57.27,114.69L63.64,112.56L70,117.6L76.36,123.9L82.73,124.42L89.09,113.68L95.45,106.46L101.82,109.54L108.18,110.45L114.55,101.91L120.91,110.19L127.27,109.83L133.64,120.37L140,108.16L146.36,104.09L152.73,112.4L159.09,112.12L165.45,110.63L171.82,113.18L178.18,120.11L184.55,115.83L190.91,115.11L197.27,107.61L203.64,105.42L210,93.59L216.36,85.99L222.73,89.83L229.09,91.38L235.45,84.43L241.82,94.8L248.18,99.74L254.55,84.56L260.91,71.27L267.27,63.84L273.64,53.06L280,25.39L286.36,15.57L292.73,23.32L299.09,19.04L305.45,28.3L311.82,30.52L318.18,34.6L324.55,44.22L330.91,49.94L337.27,52.59L343.64,45.13L350,48.5L356.36,46.32L362.73,50.88L369.09,61.98L375.45,67.93L381.82,63.18L388.18,90.42L394.55,93.34L400.91,90.45L407.27,110.78L413.64,113.19L420,110.98L426.36,112L432.73,111.03L445.45,95.2L451.82,78.15L458.18,77.28L464.55,65.62L470.91,60.76L477.27,49.23L483.64,29.11L490,17.2L496.36,27.65L502.73,29.2L509.09,17.02L515.45,14.5L521.82,21.45L528.18,37.98L534.55,38.03L540.91,43.91L553.64,65.96L560,50.9L572.73,58.48L579.09,58.16L585.45,64.53L591.82,63.11L598.18,65.49L604.55,53.87L610.91,49.68L617.27,47.2L623.64,60.09L630,66.24" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter trace27e29c61-eb24-474a-a4c0-4092a9624663" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,141.13L6.36,135.64L12.73,134.08L19.09,128.83L25.45,119.82L31.82,104.31L38.18,104.62L44.55,108.85L50.91,108.8L57.27,114.69L63.64,112.56L70,117.6L76.36,123.9L82.73,124.42L89.09,113.68L95.45,106.46L101.82,109.54L108.18,110.45L114.55,101.91L120.91,110.19L127.27,109.83L133.64,120.37L140,108.16L146.36,104.09L152.73,112.4L159.09,112.12L165.45,110.63L171.82,113.18L178.18,120.11L184.55,115.83L190.91,115.11L197.27,107.61L203.64,105.42L210,93.59L216.36,85.99L222.73,89.83L229.09,91.38L235.45,84.43L241.82,94.8L248.18,99.74L254.55,84.56L260.91,71.27L267.27,63.84L273.64,53.06L280,25.39L286.36,15.57L292.73,23.32L299.09,19.04L305.45,28.3L311.82,30.52L318.18,34.6L324.55,44.22L330.91,49.94L337.27,52.59L343.64,45.13L350,48.5L356.36,46.32L362.73,50.88L369.09,61.98L375.45,67.93L381.82,63.18L388.18,90.42L394.55,93.34L400.91,90.45L407.27,110.78L413.64,113.19L420,110.98L426.36,112L432.73,111.03L445.45,95.2L451.82,78.15L458.18,77.28L464.55,65.62L470.91,60.76L477.27,49.23L483.64,29.11L490,17.2L496.36,27.65L502.73,29.2L509.09,17.02L515.45,14.5L521.82,21.45L528.18,37.98L534.55,38.03L540.91,43.91L553.64,65.96L560,50.9L572.73,58.48L579.09,58.16L585.45,64.53L591.82,63.11L598.18,65.49L604.55,53.87L610.91,49.68L617.27,47.2L623.64,60.09L630,66.24" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(44, 160, 44); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter trace51feb8e8-d404-425a-b403-0f2edcbc768a" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,148.84L6.36,145.68L12.73,140.77L19.09,150.62L25.45,142.79L31.82,141.05L38.18,146.11L44.55,159.52L50.91,155.28L57.27,157.96L63.64,147.55L70,152.73L76.36,162.24L82.73,167.68L89.09,166.41L95.45,162.85L101.82,164.76L108.18,179.24L114.55,178.68L120.91,168.98L127.27,169.64L133.64,177.44L140,169.3L146.36,170.23L152.73,170.17L159.09,152.3L165.45,148.49L171.82,145.02L178.18,165.22L184.55,163.55L190.91,157.49L197.27,169.8L203.64,163L210,165.45L216.36,173.57L222.73,176.54L229.09,183.98L235.45,180.03L241.82,173.13L248.18,166.77L254.55,154.41L267.27,160.46L273.64,148.94L280,156.19L286.36,154.36L292.73,157.08L299.09,154.84L305.45,146.3L311.82,158.29L318.18,157.55L324.55,163.74L330.91,169.74L337.27,168.38L343.64,170.47L350,180.92L356.36,195.33L362.73,207.01L369.09,216.16L375.45,215.7L381.82,208.92L388.18,224.39L394.55,224.39L400.91,234.99L407.27,230.05L413.64,220.81L420,224.17L426.36,231.41L432.73,233.24L439.09,236.34L445.45,236.5L451.82,229.68L458.18,229.73L464.55,224.3L470.91,216.57L477.27,225.61L483.64,222.28L490,229.69L502.73,225.8L509.09,228.28L515.45,224.16L521.82,242.35L528.18,237.57L534.55,249.72L540.91,247.8L547.27,249.51L553.64,257.03L560,257.16L566.36,259.03L572.73,252.31L579.09,255.84L585.45,261.15L591.82,261.52L598.18,260.9L604.55,265.49L610.91,275.5L617.27,273.18L623.64,268.3L630,267" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(220, 57, 18); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="333" transform="translate(40,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">0</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(167.26999999999998,0)">20</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(294.55,0)">40</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(421.82,0)">60</text></g><g class="xtick"><text text-anchor="middle" x="0" y="333" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(549.0899999999999,0)">80</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="39" y="4.199999999999999" transform="translate(0,316.18)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">0.8</text></g><g class="ytick"><text text-anchor="end" x="39" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,279.36)">0.85</text></g><g class="ytick"><text text-anchor="end" x="39" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,242.54)">0.9</text></g><g class="ytick"><text text-anchor="end" x="39" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,205.72)">0.95</text></g><g class="ytick"><text text-anchor="end" x="39" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,168.9)">1</text></g><g class="ytick"><text text-anchor="end" x="39" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,132.07999999999998)">1.05</text></g><g class="ytick"><text text-anchor="end" x="39" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,95.26)">1.1</text></g><g class="ytick"><text text-anchor="end" x="39" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,58.44)">1.15</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="iciclelayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-188f6a"><g class="clips"/></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-ytitle"/></g></svg>