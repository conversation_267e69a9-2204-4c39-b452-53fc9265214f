<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="750" height="600" style="" viewBox="0 0 750 600"><rect x="0" y="0" width="750" height="600" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-95aa6b"><g class="clips"><clipPath id="clip95aa6bxyplot" class="plotclip"><rect width="634" height="225.4"/></clipPath><clipPath id="clip95aa6bx2y2plot" class="plotclip"><rect width="634" height="225.4"/></clipPath><clipPath class="axesclip" id="clip95aa6bx"><rect x="75" y="0" width="634" height="600"/></clipPath><clipPath class="axesclip" id="clip95aa6by"><rect x="0" y="65" width="750" height="225.4"/></clipPath><clipPath class="axesclip" id="clip95aa6bxy"><rect x="75" y="65" width="634" height="225.4"/></clipPath><clipPath class="axesclip" id="clip95aa6by2"><rect x="0" y="322.6" width="750" height="225.4"/></clipPath><clipPath class="axesclip" id="clip95aa6bxy2"><rect x="75" y="322.6" width="634" height="225.4"/></clipPath><clipPath class="axesclip" id="clip95aa6bx2"><rect x="75" y="0" width="634" height="600"/></clipPath><clipPath class="axesclip" id="clip95aa6bx2y"><rect x="75" y="65" width="634" height="225.4"/></clipPath><clipPath class="axesclip" id="clip95aa6bx2y2"><rect x="75" y="322.6" width="634" height="225.4"/></clipPath></g><g class="gradients"/></defs><g class="bglayer"><rect class="bg" x="75" y="65" width="634" height="225.4" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/><rect class="bg" x="75" y="322.6" width="634" height="225.4" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(155.55,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(230.9,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(311.45,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(389.4,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(469.95,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(547.9,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(628.45,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,277.81)" d="M75,0h634" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,214.73)" d="M75,0h634" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,88.59)" d="M75,0h634" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="yzl zl crisp" transform="translate(0,151.66)" d="M75,0h634" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(75,65)" clip-path="url('#clip95aa6bxyplot')"><g class="scatterlayer mlayer"><g class="trace scatter trace8b88e9c0-9e17-46b4-9d71-2e7066009331" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,86.66L15.59,86.66L18.19,93.14L20.79,108.65L23.39,86.44L38.98,86.44L41.57,71.59L44.17,70.6L46.77,87.71L49.37,91.22L51.97,84.84L54.57,89.54L57.16,109.39L59.76,106.57L62.36,112.18L116.93,112.18L119.52,109.43L122.12,124.38L124.72,96.73L127.32,127.85L129.92,129.38L132.52,124.6L135.11,126.03L137.71,110.04L140.31,126.82L142.91,145.7L145.51,177.6L148.11,179.8L150.7,186.66L153.3,191.12L155.9,193.39L158.5,174.59L161.1,179.6L163.7,181.59L166.3,161.79L168.89,159.11L171.49,172.13L213.07,172.13L215.66,145.06L218.26,149.63L220.86,146.62L226.06,186.92L228.66,214.13L231.25,170.98L233.85,170.23L239.05,140.06L241.65,145.18L244.25,133.78L246.84,140.27L249.44,99.42L252.04,107.53L254.64,94.13L257.24,96.85L259.84,133.95L262.43,134.5L306.61,134.5L309.2,60.28L311.8,70.92L314.4,55.52L317,46.28L319.6,53.08L322.2,51.95L324.8,45.2L371.57,45.2L374.16,75.78L376.76,67.27L379.36,72.51L381.96,47.26L384.56,47.26L400.15,47.26L402.75,37.27L405.34,46.65L407.94,47.47L410.54,40.17L413.14,39.3L415.74,37.62L418.34,32.45L420.93,70.5L423.53,59.47L426.13,59.85L428.73,65.99L431.33,61.56L433.93,55.5L439.12,64.26L441.72,72.85L444.32,69.77L446.92,71.76L449.52,47.82L452.11,49.14L454.71,71.07L457.31,74.46L459.91,81.53L462.51,81.53L465.11,74.63L467.7,71.3L470.3,75.02L472.9,68.67L475.5,76.04L478.1,78.58L480.7,75.41L483.3,79.52L485.89,58.33L488.49,66.99L491.09,54.61L493.69,65.18L496.29,65.12L498.89,67.82L501.48,65.28L504.08,65.28L517.07,65.28L519.67,63.38L522.27,64.91L524.87,64.91L550.85,64.91L553.45,39.78L556.05,80.46L558.65,69.36L561.25,71.69L563.84,40.51L566.44,38.62L569.04,48.89L571.64,40.1L574.24,44.61L576.84,32.95L579.43,59.9L584.63,38.37L587.23,39.25L589.83,33.67L592.43,32.11L595.02,11.27L597.62,26.43L600.22,39.86L602.82,32.94L605.42,49.41L608.02,44.26L634,44.26" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"/><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="74" y="4.199999999999999" transform="translate(0,277.81)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">−0.2</text></g><g class="ytick"><text text-anchor="end" x="74" y="4.199999999999999" transform="translate(0,214.73)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">−0.1</text></g><g class="ytick"><text text-anchor="end" x="74" y="4.199999999999999" transform="translate(0,151.66)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0</text></g><g class="ytick"><text text-anchor="end" x="74" y="4.199999999999999" transform="translate(0,88.59)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0.1</text></g></g><g class="overaxes-above"/></g><g class="subplot x2y2"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x2"><path class="x2grid crisp" transform="translate(155.55,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(230.9,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(311.45,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(389.4,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(469.95,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(547.9,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(628.45,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y2"><path class="y2grid crisp" transform="translate(0,481.68000000000006)" d="M75,0h634" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y2grid crisp" transform="translate(0,407.78000000000003)" d="M75,0h634" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="y2zl zl crisp" transform="translate(0,333.87)" d="M75,0h634" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(75,322.6)" clip-path="url('#clip95aa6bx2y2plot')"><g class="scatterlayer mlayer"><g class="trace scatter trace1d118ed8-7868-4b36-8c5d-e1a8612ebe6d" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M23.39,62.8L38.98,62.8L41.57,48L44.17,11.27L46.77,50.37L49.37,58.4L51.97,58.4L54.57,58.4L57.16,99.91L59.76,99.91L62.36,106.3L67.56,106.3L70.16,75.16L75.35,75.16L77.95,64.57L80.55,24.85L83.15,24.85L85.75,11.27L88.34,11.27L119.52,11.27L122.12,47.62L124.72,47.62L127.32,85.4L129.92,89.03L132.52,89.03L140.31,89.03L142.91,127.91L145.51,203.86L148.11,209.12L150.7,197.75L153.3,208.61L155.9,214.13L158.5,214.13L161.1,214.13L163.7,177.88L166.3,134.57L168.89,54.52L171.49,48.63L174.09,45.72L192.28,45.72L194.88,11.27L215.66,11.27L218.26,23.06L220.86,23.06L223.46,69.43L226.06,119.39L228.66,189.65L239.05,189.65L241.65,186.1L244.25,186.1L246.84,136.41L252.04,30.65L254.64,30.65L257.24,30.65L259.84,105.71L262.43,107L265.03,107L280.62,107L283.22,100.95L285.82,12.65L288.42,11.27L309.2,11.27L311.8,35.2L332.59,35.2L335.19,26.26L340.39,26.26L342.98,11.27L371.57,11.27L374.16,78.51L394.95,78.51L397.55,23.2L400.15,23.2L402.75,11.27L405.34,31.65L407.94,33.43L418.34,33.43L420.93,93.38L439.12,93.38L441.72,98.44L444.32,50.01L446.92,50.01L452.11,50.01L454.71,62.61L457.31,70.08L459.91,85.69L472.9,85.69L475.5,82.91L478.1,35.19L480.7,33.86L483.3,36L485.89,36L496.29,36L498.89,40.74L514.48,40.74L517.07,17.4L519.67,17.4L522.27,14.74L543.06,14.74L545.66,11.27L553.45,11.27L556.05,100.01L576.84,100.01L579.43,69.46L597.62,69.46L600.22,71.11L602.82,71.11L605.42,91.11L618.41,91.11L621.01,60.42L623.61,46.84L626.2,46.84L628.8,11.27L631.4,11.27L634,11.27" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(75,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jan 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(155.55,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Feb 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(230.9,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Mar 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(311.45,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Apr 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(389.4,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">May 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(469.95,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jun 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(547.9,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jul 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(628.45,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Aug 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(709,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Sep 2020</text></g></g><g class="yaxislayer-above"><g class="y2tick"><text text-anchor="end" x="74" y="4.199999999999999" transform="translate(0,481.68000000000006)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">−0.1</text></g><g class="y2tick"><text text-anchor="end" x="74" y="4.199999999999999" transform="translate(0,407.78000000000003)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">−0.05</text></g><g class="y2tick"><text text-anchor="end" x="74" y="4.199999999999999" transform="translate(0,333.87)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-95aa6b"><g class="clips"/><clipPath id="legend95aa6b"><rect width="341" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(368,11.84999999999998)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="341" height="29" x="0" y="0"/><g class="scrollbox" transform="" clip-path="url('#legend95aa6b')"><g class="groups"><g class="traces" transform="translate(0,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">cumulative_returns</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="159.578125" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(162.078125,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">rolling_drawdown(10)</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="175.4375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-x2title"><text class="x2title" x="392" y="588.3" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Date</text></g><g class="g-ytitle"><text class="ytitle" transform="rotate(-90,19.746875000000003,177.7)" x="19.746875000000003" y="177.7" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Cumulative returns</text></g><g class="g-y2title" transform="translate(1.880859375,0)"><text class="y2title" transform="rotate(-90,12.121875000000003,435.3)" x="12.121875000000003" y="435.3" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Rolling drawdown</text></g><g class="annotation" data-index="0" style="opacity: 1;"><g class="annotation-text-g" transform="rotate(0,392,52.99999999999999)"><g class="cursor-pointer" transform="translate(311,41)"><rect class="bg" x="0.5" y="0.5" width="162" height="23" style="stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;"/><text class="annotation-text" text-anchor="middle" x="81.34375" y="18" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Cumulative Returns</text></g></g></g><g class="annotation" data-index="1" style="opacity: 1;"><g class="annotation-text-g" transform="rotate(0,392,310.6)"><g class="cursor-pointer" transform="translate(318,299)"><rect class="bg" x="0.5" y="0.5" width="148" height="23" style="stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;"/><text class="annotation-text" text-anchor="middle" x="74.25" y="18" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Rolling Drawdown</text></g></g></g></g></svg>