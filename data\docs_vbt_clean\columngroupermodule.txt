column_grouper moduleClass that exposes methods to group columns.

ColumnGrouper stores metadata related to grouping columns. It can return, for example, the number of groups, the start indices of groups, and other information useful for reducing operations that utilize grouping. It also allows to dynamically enable/disable/modify groups and checks whether a certain operation is permitted. get_group_lens_nb functionget_group_lens_nb ( groups ) Return count per group. get_groups_and_index functionget_groups_and_index ( index , group_by ) Return array of group indices pointing to the original index, and grouped index. group_by_to_index functiongroup_by_to_index ( index , group_by ) Convert mapper group_by to pd.Index . Note Index and mapper must have the same length. ColumnGrouper classColumnGrouper ( columns , group_by = None , allow_enable = True , allow_disable = True , allow_modify = True ) Class that exposes methods to group columns. group_by can be: boolean (False for no grouping, True for one group), integer (level by position), string (level by name), sequence of integers or strings that is shorter than columns (multiple levels), any other sequence that has the same length as columns (group per column). Set allow_enable to False to prohibit grouping if ColumnGrouper.group_by is None. Set allow_disable to False to prohibit disabling of grouping if ColumnGrouper.group_by is not None. Set allow_modify to False to prohibit modifying groups (you can still change their labels). All properties are read-only to enable caching. Note Columns should build groups that are coherent and sorted for using get_group_lens_nb() . Note This class is meant to be immutable. To change any attribute, use Configured.replace() . Superclasses Configured Documented Pickleable Inherited members Configured.config Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Configured.writeable_attrs Pickleable.load() Pickleable.save() allow_disable propertyWhether to allow disabling grouping. allow_enable propertyWhether to allow enabling grouping. allow_modify propertyWhether to allow changing groups. check_group_by methodColumnGrouper . check_group_by ( group_by = None , allow_enable = None , allow_disable = None , allow_modify = None ) Check passed group_by object against restrictions. columns propertyOriginal columns. get_columns methodColumnGrouper . get_columns ( ** kwargs ) Return grouped columns. get_group_count methodColumnGrouper . get_group_count ( ** kwargs ) Get number of groups. get_group_end_idxs methodColumnGrouper . get_group_end_idxs ( ** kwargs ) Get end index of each group as an array. get_group_lens methodColumnGrouper . get_group_lens ( group_by = None , ** kwargs ) See get_group_lens_nb. get_group_start_idxs methodColumnGrouper . get_group_start_idxs ( ** kwargs ) Get first index of each group as an array. get_groups methodColumnGrouper . get_groups ( ** kwargs ) Return groups array. get_groups_and_columns methodColumnGrouper . get_groups_and_columns ( group_by = None , ** kwargs ) See get_groups_and_index() . group_by propertyMapper for grouping. is_group_count_changed methodColumnGrouper . is_group_count_changed ( group_by = None ) Check whether the number of groups has changed. is_grouped methodColumnGrouper . is_grouped ( group_by = None ) Check whether columns are grouped. is_grouping_changed methodColumnGrouper . is_grouping_changed ( group_by = None ) Check whether column grouping has changed in any way. is_grouping_disabled methodColumnGrouper . is_grouping_disabled ( group_by = None ) Check whether column grouping has been disabled. is_grouping_enabled methodColumnGrouper . is_grouping_enabled ( group_by = None ) Check whether column grouping has been enabled. is_grouping_modified methodColumnGrouper . is_grouping_modified ( group_by = None ) Check whether column grouping has been modified. Doesn't care if grouping labels have been changed. is_sorted methodColumnGrouper . is_sorted ( group_by = None , ** kwargs ) Return whether groups are coherent and sorted. resolve_group_by methodColumnGrouper . resolve_group_by ( group_by = None , ** kwargs ) Resolve group_by from either object variable or keyword argument.