root_accessors moduleRoot pandas accessors. An accessor adds additional “namespace” to pandas objects. The vectorbt.root_accessors registers a custom vbt accessor on top of each pd.Series and pd.DataFrame object. It is the main entry point for all other accessors: vbt.base.accessors.BaseSR/DFAccessor -> pd.Series/DataFrame.vbt.* vbt.generic.accessors.GenericSR/DFAccessor -> pd.Series/DataFrame.vbt.* vbt.signals.accessors.SignalsSR/DFAccessor -> pd.Series/DataFrame.vbt.signals.* vbt.returns.accessors.ReturnsSR/DFAccessor -> pd.Series/DataFrame.vbt.returns.* vbt.ohlcv.accessors.OHLCVDFAccessor -> pd.DataFrame.vbt.ohlc.* and pd.DataFrame.vbt.ohlcv.* vbt.px_accessors.PXAccessor -> pd.DataFrame.vbt.px.* Additionally, some accessors subclass other accessors building the following inheritance hierarchy: vbt.base.accessors.BaseSR/DFAccessor -> vbt.generic.accessors.GenericSR/DFAccessor -> vbt.cat_accessors.CatSR/DFAccessor -> vbt.signals.accessors.SignalsSR/DFAccessor -> vbt.returns.accessors.ReturnsSR/DFAccessor -> vbt.ohlcv_accessors.OHLCVDFAccessor -> vbt.px_accessors.PXSR/DFAccessor So, for example, the method pd.Series.vbt.to_2d_array is also available as pd.Series.vbt.returns.to_2d_array . Note Accessors in vectorbt are not cached, so querying df.vbt twice will also call Vbt_DFAccessor twice. register_accessor functionregister_accessor ( name , cls ) Register a custom accessor. cls should subclass pandas.core.accessor.DirNamesMixin . register_dataframe_accessor functionregister_dataframe_accessor ( name ) Decorator to register a custom pd.DataFrame accessor on top of the pd.DataFrame . register_dataframe_vbt_accessor functionregister_dataframe_vbt_accessor ( name , parent = vectorbt . root_accessors . Vbt_DFAccessor ) Decorator to register a pd.DataFrame accessor on top of a parent accessor. register_series_accessor functionregister_series_accessor ( name ) Decorator to register a custom pd.Series accessor on top of the pd.Series . register_series_vbt_accessor functionregister_series_vbt_accessor ( name , parent = vectorbt . root_accessors . Vbt_SRAccessor ) Decorator to register a pd.Series accessor on top of a parent accessor. Accessor classAccessor ( name , accessor ) Custom property-like object. Note In contrast to other pandas accessors, this accessor is not cached! This prevents from using old data if the object has been changed in-place. Vbt_DFAccessor classVbt_DFAccessor ( obj , ** kwargs ) The main vectorbt accessor for pd.DataFrame . Superclasses AttrResolver BaseAccessor BaseDFAccessor Configured Documented GenericAccessor GenericDFAccessor IndexingBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping pandas.core.accessor.DirNamesMixin Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() BaseAccessor.align_to() BaseAccessor.apply() BaseAccessor.apply_and_concat() BaseAccessor.apply_on_index() BaseAccessor.broadcast() BaseAccessor.broadcast_to() BaseAccessor.combine() BaseAccessor.concat() BaseAccessor.drop_duplicate_levels() BaseAccessor.drop_levels() BaseAccessor.drop_redundant_levels() BaseAccessor.empty() BaseAccessor.empty_like() BaseAccessor.indexing_func() BaseAccessor.make_symmetric() BaseAccessor.rename_levels() BaseAccessor.repeat() BaseAccessor.select_levels() BaseAccessor.stack_index() BaseAccessor.tile() BaseAccessor.to_1d_array() BaseAccessor.to_2d_array() BaseAccessor.to_dict() BaseAccessor.unstack_to_array() BaseAccessor.unstack_to_df() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() GenericAccessor.apply_along_axis() GenericAccessor.apply_and_reduce() GenericAccessor.apply_mapping() GenericAccessor.applymap() GenericAccessor.barplot() GenericAccessor.bfill() GenericAccessor.binarize() GenericAccessor.boxplot() GenericAccessor.bshift() GenericAccessor.count() GenericAccessor.crossed_above() GenericAccessor.crossed_below() GenericAccessor.cumprod() GenericAccessor.cumsum() GenericAccessor.describe() GenericAccessor.diff() GenericAccessor.drawdown() GenericAccessor.ewm_mean() GenericAccessor.ewm_std() GenericAccessor.expanding_apply() GenericAccessor.expanding_max() GenericAccessor.expanding_mean() GenericAccessor.expanding_min() GenericAccessor.expanding_split() GenericAccessor.expanding_std() GenericAccessor.ffill() GenericAccessor.fillna() GenericAccessor.filter() GenericAccessor.fshift() GenericAccessor.get_drawdowns() GenericAccessor.get_ranges() GenericAccessor.groupby_apply() GenericAccessor.histplot() GenericAccessor.idxmax() GenericAccessor.idxmin() GenericAccessor.lineplot() GenericAccessor.max() GenericAccessor.maxabs_scale() GenericAccessor.mean() GenericAccessor.median() GenericAccessor.min() GenericAccessor.minmax_scale() GenericAccessor.normalize() GenericAccessor.pct_change() GenericAccessor.plot() GenericAccessor.power_transform() GenericAccessor.product() GenericAccessor.quantile_transform() GenericAccessor.range_split() GenericAccessor.rebase() GenericAccessor.reduce() GenericAccessor.resample_apply() GenericAccessor.resolve_self() GenericAccessor.robust_scale() GenericAccessor.rolling_apply() GenericAccessor.rolling_max() GenericAccessor.rolling_mean() GenericAccessor.rolling_min() GenericAccessor.rolling_split() GenericAccessor.rolling_std() GenericAccessor.scale() GenericAccessor.scatterplot() GenericAccessor.shuffle() GenericAccessor.split() GenericAccessor.std() GenericAccessor.sum() GenericAccessor.to_mapped() GenericAccessor.to_returns() GenericAccessor.transform() GenericAccessor.value_counts() GenericAccessor.zscore() GenericDFAccessor.config GenericDFAccessor.df_accessor_cls GenericDFAccessor.drawdowns GenericDFAccessor.flatten_grouped() GenericDFAccessor.heatmap() GenericDFAccessor.iloc GenericDFAccessor.indexing_kwargs GenericDFAccessor.loc GenericDFAccessor.mapping GenericDFAccessor.obj GenericDFAccessor.plots_defaults GenericDFAccessor.ranges GenericDFAccessor.self_aliases GenericDFAccessor.squeeze_grouped() GenericDFAccessor.sr_accessor_cls GenericDFAccessor.stats_defaults GenericDFAccessor.ts_heatmap() GenericDFAccessor.wrapper GenericDFAccessor.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.select_one() Wrapping.select_one_from_obj() ohlc class variableAccessor on top of OHLCV data. For DataFrames only. Accessible through pd.DataFrame.vbt.ohlcv . ohlcv class variableAccessor on top of OHLCV data. For DataFrames only. Accessible through pd.DataFrame.vbt.ohlcv . px class variableAccessor for running Plotly Express functions. For DataFrames only. Accessible through pd.DataFrame.vbt.px . returns class variableAccessor on top of return series. For DataFrames only. Accessible through pd.DataFrame.vbt.returns . signals class variableAccessor on top of signal series. For DataFrames only. Accessible through pd.DataFrame.vbt.signals . Vbt_SRAccessor classVbt_SRAccessor ( obj , ** kwargs ) The main vectorbt accessor for pd.Series . Superclasses AttrResolver BaseAccessor BaseSRAccessor Configured Documented GenericAccessor GenericSRAccessor IndexingBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping pandas.core.accessor.DirNamesMixin Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() BaseAccessor.align_to() BaseAccessor.apply() BaseAccessor.apply_and_concat() BaseAccessor.apply_on_index() BaseAccessor.broadcast() BaseAccessor.broadcast_to() BaseAccessor.combine() BaseAccessor.concat() BaseAccessor.drop_duplicate_levels() BaseAccessor.drop_levels() BaseAccessor.drop_redundant_levels() BaseAccessor.empty() BaseAccessor.empty_like() BaseAccessor.indexing_func() BaseAccessor.make_symmetric() BaseAccessor.rename_levels() BaseAccessor.repeat() BaseAccessor.select_levels() BaseAccessor.stack_index() BaseAccessor.tile() BaseAccessor.to_1d_array() BaseAccessor.to_2d_array() BaseAccessor.to_dict() BaseAccessor.unstack_to_array() BaseAccessor.unstack_to_df() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() GenericAccessor.apply_along_axis() GenericAccessor.apply_and_reduce() GenericAccessor.apply_mapping() GenericAccessor.applymap() GenericAccessor.barplot() GenericAccessor.bfill() GenericAccessor.binarize() GenericAccessor.boxplot() GenericAccessor.bshift() GenericAccessor.count() GenericAccessor.crossed_above() GenericAccessor.crossed_below() GenericAccessor.cumprod() GenericAccessor.cumsum() GenericAccessor.describe() GenericAccessor.diff() GenericAccessor.drawdown() GenericAccessor.ewm_mean() GenericAccessor.ewm_std() GenericAccessor.expanding_apply() GenericAccessor.expanding_max() GenericAccessor.expanding_mean() GenericAccessor.expanding_min() GenericAccessor.expanding_split() GenericAccessor.expanding_std() GenericAccessor.ffill() GenericAccessor.fillna() GenericAccessor.filter() GenericAccessor.fshift() GenericAccessor.get_drawdowns() GenericAccessor.get_ranges() GenericAccessor.groupby_apply() GenericAccessor.histplot() GenericAccessor.idxmax() GenericAccessor.idxmin() GenericAccessor.lineplot() GenericAccessor.max() GenericAccessor.maxabs_scale() GenericAccessor.mean() GenericAccessor.median() GenericAccessor.min() GenericAccessor.minmax_scale() GenericAccessor.normalize() GenericAccessor.pct_change() GenericAccessor.plot() GenericAccessor.power_transform() GenericAccessor.product() GenericAccessor.quantile_transform() GenericAccessor.range_split() GenericAccessor.rebase() GenericAccessor.reduce() GenericAccessor.resample_apply() GenericAccessor.resolve_self() GenericAccessor.robust_scale() GenericAccessor.rolling_apply() GenericAccessor.rolling_max() GenericAccessor.rolling_mean() GenericAccessor.rolling_min() GenericAccessor.rolling_split() GenericAccessor.rolling_std() GenericAccessor.scale() GenericAccessor.scatterplot() GenericAccessor.shuffle() GenericAccessor.split() GenericAccessor.std() GenericAccessor.sum() GenericAccessor.to_mapped() GenericAccessor.to_returns() GenericAccessor.transform() GenericAccessor.value_counts() GenericAccessor.zscore() GenericSRAccessor.config GenericSRAccessor.df_accessor_cls GenericSRAccessor.drawdowns GenericSRAccessor.flatten_grouped() GenericSRAccessor.heatmap() GenericSRAccessor.iloc GenericSRAccessor.indexing_kwargs GenericSRAccessor.loc GenericSRAccessor.mapping GenericSRAccessor.obj GenericSRAccessor.overlay_with_heatmap() GenericSRAccessor.plot_against() GenericSRAccessor.plots_defaults GenericSRAccessor.qqplot() GenericSRAccessor.ranges GenericSRAccessor.self_aliases GenericSRAccessor.squeeze_grouped() GenericSRAccessor.sr_accessor_cls GenericSRAccessor.stats_defaults GenericSRAccessor.ts_heatmap() GenericSRAccessor.volume() GenericSRAccessor.wrapper GenericSRAccessor.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.select_one() Wrapping.select_one_from_obj() px class variableAccessor for running Plotly Express functions. For Series only. Accessible through pd.Series.vbt.px . returns class variableAccessor on top of return series. For Series only. Accessible through pd.Series.vbt.returns . signals class variableAccessor on top of signal series. For Series only. Accessible through pd.Series.vbt.signals .