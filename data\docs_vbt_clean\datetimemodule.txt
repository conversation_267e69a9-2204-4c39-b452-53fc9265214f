datetime_ moduleUtilities for working with dates and time. convert_naive_time functionconvert_naive_time ( t , tz_out ) Return as naive time. datetime.time should not have tzinfo set. convert_tzaware_time functionconvert_tzaware_time ( t , tz_out ) Return as non-naive time. datetime.time should have tzinfo set. datetime_to_ms functiondatetime_to_ms ( dt ) Convert a datetime to milliseconds. freq_to_timedelta functionfreq_to_timedelta ( arg ) pd.to_timedelta that uses unit abbreviation with number. get_local_tz functionget_local_tz () Get local timezone. get_utc_tz functionget_utc_tz () Get UTC timezone. interval_to_ms functioninterval_to_ms ( interval ) Convert an interval string to milliseconds. is_tz_aware functionis_tz_aware ( dt ) Whether datetime is timezone-aware. naive_to_tzaware_time functionnaive_to_tzaware_time ( t , tz_out ) Return as non-naive time. datetime.time should not have tzinfo set. to_timezone functionto_timezone ( tz , to_py_timezone = None , ** kwargs ) Parse the timezone. Strings are parsed by pytz and dateparser , while integers and floats are treated as hour offsets. If the timezone object can't be checked for equality based on its properties, it's automatically converted to datetime.timezone . If to_py_timezone is set to True, will convert to datetime.timezone . **kwargs are passed to dateparser.parse . to_tzaware_datetime functionto_tzaware_datetime ( dt_like , naive_tz = None , tz = None , ** kwargs ) Parse the datetime as a timezone-aware datetime.datetime . See dateparser docs for valid string formats and **kwargs . Raw timestamps are localized to UTC, while naive datetime is localized to naive_tz . Set naive_tz to None to use the default value defined under datetime settings in settings . To explicitly convert the datetime to a timezone, use tz (uses to_timezone() ). tzaware_to_naive_time functiontzaware_to_naive_time ( t , tz_out ) Return as naive time. datetime.time should have tzinfo set.