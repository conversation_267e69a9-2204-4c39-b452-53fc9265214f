figure moduleUtilities for constructing and displaying figures. get_domain functionget_domain ( ref , fig ) Get domain of a coordinate axis. make_figure functionmake_figure ( * args , ** kwargs ) Make new figure. Returns either Figure or FigureWidget , depending on use_widgets defined under plotting in settings . make_subplots functionmake_subplots ( * args , ** kwargs ) Makes subplots and passes them to FigureWidget . Figure classFigure ( * args , ** kwargs ) Figure. Extends plotly.graph_objects.Figure . Superclasses FigureMixin plotly.basedatatypes.BaseFigure plotly.graph_objs._figure.Figure Inherited members FigureMixin.show_png() FigureMixin.show_svg() show methodFigure . show ( * args , ** kwargs ) Show the figure. FigureMixin classFigureMixin () Subclasses Figure FigureWidget show methodFigureMixin . show ( * args , ** kwargs ) Display the figure in PNG format. show_png methodFigureMixin . show_png ( ** kwargs ) Display the figure in PNG format. show_svg methodFigureMixin . show_svg ( ** kwargs ) Display the figure in SVG format. FigureWidget classFigureWidget ( * args , ** kwargs ) Figure widget. Extends plotly.graph_objects.FigureWidget . Superclasses FigureMixin ipywidgets.widgets.domwidget.DOMWidget ipywidgets.widgets.widget.LoggingHasTraits ipywidgets.widgets.widget.Widget plotly.basedatatypes.BaseFigure plotly.basewidget.BaseFigureWidget plotly.graph_objs._figurewidget.FigureWidget traitlets.traitlets.HasDescriptors traitlets.traitlets.HasTraits Inherited members FigureMixin.show_png() FigureMixin.show_svg() show methodFigureWidget . show ( * args , ** kwargs ) Show the figure.