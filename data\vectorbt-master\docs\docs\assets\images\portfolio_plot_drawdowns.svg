<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="750" height="600" style="" viewBox="0 0 750 600"><rect x="0" y="0" width="750" height="600" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-36bfd8"><g class="clips"><clipPath id="clip36bfd8xyplot" class="plotclip"><rect width="644" height="225.4"/></clipPath><clipPath id="clip36bfd8x2y2plot" class="plotclip"><rect width="644" height="225.4"/></clipPath><clipPath class="axesclip" id="clip36bfd8x"><rect x="76" y="0" width="644" height="600"/></clipPath><clipPath class="axesclip" id="clip36bfd8y"><rect x="0" y="65" width="750" height="225.4"/></clipPath><clipPath class="axesclip" id="clip36bfd8xy"><rect x="76" y="65" width="644" height="225.4"/></clipPath><clipPath class="axesclip" id="clip36bfd8y2"><rect x="0" y="322.6" width="750" height="225.4"/></clipPath><clipPath class="axesclip" id="clip36bfd8xy2"><rect x="76" y="322.6" width="644" height="225.4"/></clipPath><clipPath class="axesclip" id="clip36bfd8x2"><rect x="76" y="0" width="644" height="600"/></clipPath><clipPath class="axesclip" id="clip36bfd8x2y"><rect x="76" y="65" width="644" height="225.4"/></clipPath><clipPath class="axesclip" id="clip36bfd8x2y2"><rect x="76" y="322.6" width="644" height="225.4"/></clipPath></g><g class="gradients"/></defs><g class="bglayer"><rect class="bg" x="76" y="65" width="644" height="225.4" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/><rect class="bg" x="76" y="322.6" width="644" height="225.4" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"><path data-index="0" fill-rule="evenodd" d="M118.32,290.4H295.07V65H118.32Z" clip-path="url('#clip36bfd8x')" style="opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(255, 0, 0); fill-opacity: 1; stroke-width: 0px;"/><path data-index="1" fill-rule="evenodd" d="M476.8,290.4H516.63V65H476.8Z" clip-path="url('#clip36bfd8x')" style="opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(255, 0, 0); fill-opacity: 1; stroke-width: 0px;"/><path data-index="2" fill-rule="evenodd" d="M295.07,290.4H372.24V65H295.07Z" clip-path="url('#clip36bfd8x')" style="opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 128, 0); fill-opacity: 1; stroke-width: 0px;"/><path data-index="3" fill-rule="evenodd" d="M516.63,290.4H643.59V65H516.63Z" clip-path="url('#clip36bfd8x')" style="opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 128, 0); fill-opacity: 1; stroke-width: 0px;"/><path data-index="4" fill-rule="evenodd" d="M646.08,290.4H683.42V65H646.08Z" clip-path="url('#clip36bfd8x')" style="opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(255, 165, 0); fill-opacity: 1; stroke-width: 0px;"/></g></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(153.17000000000002,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(225.37,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(302.53999999999996,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(377.22,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(454.4,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(529.0799999999999,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(606.25,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(683.42,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,273.49)" d="M76,0h644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,213.14)" d="M76,0h644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,152.78)" d="M76,0h644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,92.43)" d="M76,0h644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"/><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(76,65)" clip-path="url('#clip36bfd8xyplot')"><g class="scatterlayer mlayer"><g class="trace scatter trace68e17c90-bb3c-4801-8453-cc0063de2785" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,87.78L14.94,87.78L17.43,93.98L19.92,108.82L22.41,87.57L37.34,87.57L39.83,73.37L42.32,72.41L44.81,88.79L47.3,92.15L49.79,86.04L52.28,90.54L54.77,109.53L57.26,106.84L59.75,112.2L112.03,112.2L114.51,109.57L117,123.87L119.49,97.41L121.98,127.2L124.47,128.66L126.96,124.09L129.45,125.45L131.94,110.15L134.43,126.22L136.92,144.28L139.41,174.8L141.9,176.91L144.39,183.47L146.88,187.74L149.37,189.91L151.86,171.92L154.35,176.72L156.84,178.62L159.32,159.68L161.81,157.11L164.3,169.56L204.13,169.56L206.62,143.66L209.11,148.03L211.6,145.16L219.07,209.76L221.56,168.46L224.05,167.75L226.54,154.07L229.03,138.88L231.52,143.78L234.01,132.87L236.5,139.08L238.99,100L241.48,107.75L243.97,94.93L246.46,97.53L248.94,133.04L251.43,133.56L293.75,133.56L296.24,62.54L298.73,72.72L301.22,57.99L303.71,49.14L306.2,55.65L308.69,54.57L311.18,48.11L355.99,48.11L358.48,77.37L360.97,69.23L363.46,74.25L365.95,50.08L368.44,50.08L383.37,50.08L385.86,40.53L388.35,49.5L390.84,50.29L393.33,43.3L395.82,42.46L398.31,40.86L400.8,35.92L403.29,72.32L405.78,61.76L408.27,62.13L410.76,68.01L413.25,63.76L415.74,57.97L420.72,66.34L423.21,74.57L425.7,71.62L428.18,73.53L430.67,50.62L433.16,51.88L435.65,72.87L438.14,76.11L440.63,82.87L443.12,82.87L445.61,76.27L448.1,73.09L450.59,76.64L453.08,70.57L455.57,77.63L458.06,80.05L460.55,77.02L463.04,80.95L465.53,60.67L468.02,68.96L470.51,57.11L472.99,67.23L475.48,67.17L477.97,69.76L480.46,67.33L482.95,67.33L495.4,67.33L497.89,65.5L500.38,66.97L502.87,66.97L527.76,66.97L530.25,42.93L532.74,81.85L535.23,71.23L537.72,73.46L540.21,43.62L542.7,41.81L545.19,51.65L547.68,43.23L550.17,47.55L552.66,36.39L555.15,62.17L560.13,41.57L562.61,42.42L565.1,37.08L567.59,35.58L570.08,15.64L572.57,30.16L575.06,43L577.55,36.38L580.04,52.14L582.53,47.22L607.42,47.22" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(148, 103, 189); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter tracedc69f158-c382-4f0c-aa86-953b7d218442" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(42.32,72.41)" d="M4.55,0L0,4.55L-4.55,0L0,-4.55Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(400.8,35.92)" d="M4.55,0L0,4.55L-4.55,0L0,-4.55Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(570.08,15.64)" d="M4.55,0L0,4.55L-4.55,0L0,-4.55Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace7e3bbe17-fd70-4850-8876-3260923f6574" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(219.07,209.76)" d="M4.55,0L0,4.55L-4.55,0L0,-4.55Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(440.63,82.87)" d="M4.55,0L0,4.55L-4.55,0L0,-4.55Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace452be90f-c9f5-4780-a415-de01f9fe81f5" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(296.24,62.54)" d="M4.55,0L0,4.55L-4.55,0L0,-4.55Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(567.59,35.58)" d="M4.55,0L0,4.55L-4.55,0L0,-4.55Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracea9e994b9-25f8-4cfe-8751-e301dba7eeb3" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(607.42,47.22)" d="M4.55,0L0,4.55L-4.55,0L0,-4.55Z" style="opacity: 1; stroke-width: 1px; fill: rgb(255, 170, 0); fill-opacity: 1; stroke: rgb(178, 118, 0); stroke-opacity: 1;"/></g><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"/><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="75" y="4.199999999999999" transform="translate(0,273.49)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">80</text></g><g class="ytick"><text text-anchor="end" x="75" y="4.199999999999999" transform="translate(0,213.14)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">90</text></g><g class="ytick"><text text-anchor="end" x="75" y="4.199999999999999" transform="translate(0,152.78)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">100</text></g><g class="ytick"><text text-anchor="end" x="75" y="4.199999999999999" transform="translate(0,92.43)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">110</text></g></g><g class="overaxes-above"/></g><g class="subplot x2y2"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x2"><path class="x2grid crisp" transform="translate(153.17000000000002,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(225.37,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(302.53999999999996,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(377.22,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(454.4,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(529.0799999999999,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(606.25,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="x2grid crisp" transform="translate(683.42,0)" d="M0,322.6v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y2"><path class="y2grid crisp" transform="translate(0,515.69)" d="M76,0h644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y2grid crisp" transform="translate(0,467.67)" d="M76,0h644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y2grid crisp" transform="translate(0,419.64000000000004)" d="M76,0h644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y2grid crisp" transform="translate(0,371.62)" d="M76,0h644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="y2zl zl crisp" transform="translate(0,323.6)" d="M76,0h644" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(76,322.6)" clip-path="url('#clip36bfd8x2y2plot')"><g class="scatterlayer mlayer"><g class="trace scatter trace765e0563-f2bf-4339-9322-7d8d3574aeb8" style="stroke-miterlimit: 2;"><g class="fills"><g><path class="js-fill" d="M607.42,1L0,1L0,1L14.94,1L17.43,10.86L19.92,34.48L22.41,1L42.32,1L44.81,26.41L47.3,31.63L49.79,22.15L52.28,29.13L54.77,58.6L57.26,54.42L59.75,62.75L112.03,62.75L114.51,58.66L117,80.86L119.49,39.8L121.98,86.02L124.47,88.28L126.96,81.2L129.45,83.31L131.94,59.56L134.43,84.49L136.92,112.52L139.41,159.88L141.9,163.16L144.39,173.34L146.88,179.97L149.37,183.34L151.86,155.42L154.35,162.86L156.84,165.82L159.32,136.42L161.81,132.44L164.3,151.76L204.13,151.76L206.62,111.57L209.11,118.35L211.6,113.88L219.07,214.13L221.56,150.05L224.05,148.94L226.54,127.72L229.03,104.14L231.52,111.75L234.01,94.82L236.5,104.46L238.99,43.81L241.48,55.84L243.97,35.94L246.46,39.98L248.94,95.08L251.43,95.88L293.75,95.88L296.24,1L298.73,16.55L301.22,1L303.71,1L306.2,10.74L308.69,9.11L311.18,1L355.99,1L358.48,44.69L360.97,32.53L363.46,40.03L365.95,3.95L368.44,3.95L383.37,3.95L385.86,1L388.35,14.24L390.84,15.4L393.33,5.09L395.82,3.86L398.31,1.48L400.8,1L403.29,54.36L405.78,38.88L408.27,39.42L410.76,48.03L413.25,41.81L415.74,33.31L420.72,45.59L423.21,57.64L425.7,53.33L428.18,56.12L430.67,22.54L433.16,24.4L435.65,55.15L438.14,59.9L440.63,69.81L443.12,69.81L445.61,60.13L448.1,55.48L450.59,60.69L453.08,51.78L455.57,62.12L458.06,65.68L460.55,61.23L463.04,67L465.53,37.28L468.02,49.42L470.51,32.07L472.99,46.89L475.48,46.81L477.97,50.6L480.46,47.03L482.95,47.03L495.4,47.03L497.89,44.36L500.38,46.51L502.87,46.51L527.76,46.51L530.25,11.28L532.74,68.32L535.23,52.75L537.72,56.01L540.21,12.29L542.7,9.65L545.19,24.05L547.68,11.72L550.17,18.05L552.66,1.7L555.15,39.48L560.13,9.29L562.61,10.53L565.1,2.7L567.59,1L570.08,1L572.57,21.63L575.06,39.88L577.55,30.47L580.04,52.88L582.53,45.88L607.42,45.88" style="fill: rgb(255, 111, 0); fill-opacity: 0.3; stroke-width: 0;"/></g></g><g class="errorbars"/><g class="lines"><path class="js-line" d="M0,1L14.94,1L17.43,10.86L19.92,34.48L22.41,1L42.32,1L44.81,26.41L47.3,31.63L49.79,22.15L52.28,29.13L54.77,58.6L57.26,54.42L59.75,62.75L112.03,62.75L114.51,58.66L117,80.86L119.49,39.8L121.98,86.02L124.47,88.28L126.96,81.2L129.45,83.31L131.94,59.56L134.43,84.49L136.92,112.52L139.41,159.88L141.9,163.16L144.39,173.34L146.88,179.97L149.37,183.34L151.86,155.42L154.35,162.86L156.84,165.82L159.32,136.42L161.81,132.44L164.3,151.76L204.13,151.76L206.62,111.57L209.11,118.35L211.6,113.88L219.07,214.13L221.56,150.05L224.05,148.94L226.54,127.72L229.03,104.14L231.52,111.75L234.01,94.82L236.5,104.46L238.99,43.81L241.48,55.84L243.97,35.94L246.46,39.98L248.94,95.08L251.43,95.88L293.75,95.88L296.24,1L298.73,16.55L301.22,1L303.71,1L306.2,10.74L308.69,9.11L311.18,1L355.99,1L358.48,44.69L360.97,32.53L363.46,40.03L365.95,3.95L368.44,3.95L383.37,3.95L385.86,1L388.35,14.24L390.84,15.4L393.33,5.09L395.82,3.86L398.31,1.48L400.8,1L403.29,54.36L405.78,38.88L408.27,39.42L410.76,48.03L413.25,41.81L415.74,33.31L420.72,45.59L423.21,57.64L425.7,53.33L428.18,56.12L430.67,22.54L433.16,24.4L435.65,55.15L438.14,59.9L440.63,69.81L443.12,69.81L445.61,60.13L448.1,55.48L450.59,60.69L453.08,51.78L455.57,62.12L458.06,65.68L460.55,61.23L463.04,67L465.53,37.28L468.02,49.42L470.51,32.07L472.99,46.89L475.48,46.81L477.97,50.6L480.46,47.03L482.95,47.03L495.4,47.03L497.89,44.36L500.38,46.51L502.87,46.51L527.76,46.51L530.25,11.28L532.74,68.32L535.23,52.75L537.72,56.01L540.21,12.29L542.7,9.65L545.19,24.05L547.68,11.72L550.17,18.05L552.66,1.7L555.15,39.48L560.13,9.29L562.61,10.53L565.1,2.7L567.59,1L570.08,1L572.57,21.63L575.06,39.88L577.55,30.47L580.04,52.88L582.53,45.88L607.42,45.88" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 111, 0); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(76,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jan 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(153.17000000000002,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Feb 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(225.37,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Mar 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(302.53999999999996,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Apr 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(377.22,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">May 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(454.4,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jun 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(529.0799999999999,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jul 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(606.25,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Aug 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(683.42,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Sep 2020</text></g></g><g class="yaxislayer-above"><g class="y2tick"><text text-anchor="end" x="75" y="4.199999999999999" transform="translate(0,515.69)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">−20%</text></g><g class="y2tick"><text text-anchor="end" x="75" y="4.199999999999999" transform="translate(0,467.67)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">−15%</text></g><g class="y2tick"><text text-anchor="end" x="75" y="4.199999999999999" transform="translate(0,419.64000000000004)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">−10%</text></g><g class="y2tick"><text text-anchor="end" x="75" y="4.199999999999999" transform="translate(0,371.62)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">−5%</text></g><g class="y2tick"><text text-anchor="end" x="75" y="4.199999999999999" transform="translate(0,323.6)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0%</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-36bfd8"><g class="clips"/><clipPath id="legend36bfd8"><rect width="559" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"><path data-index="5" fill-rule="evenodd" d="M76,323.6L720,323.6" clip-path="url('#clip36bfd8y2')" style="opacity: 1; stroke: rgb(128, 128, 128); stroke-opacity: 1; fill: rgb(0, 0, 0); fill-opacity: 0; stroke-dasharray: 9px, 9px; stroke-width: 2px;"/></g></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(161,11.84999999999998)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="559" height="29" x="0" y="0"/><g class="scrollbox" transform="" clip-path="url('#legend36bfd8')"><g class="groups"><g class="traces" transform="translate(0,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Value</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(148, 103, 189); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="75.953125" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(78.453125,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Peak</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M4.55,0L0,4.55L-4.55,0L0,-4.55Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="71.203125" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(152.15625,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Valley</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M4.55,0L0,4.55L-4.55,0L0,-4.55Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="78.75" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(233.40625,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Recovery/Peak</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M4.55,0L0,4.55L-4.55,0L0,-4.55Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="132.15625" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(368.0625,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Active</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M4.55,0L0,4.55L-4.55,0L0,-4.55Z" style="opacity: 1; stroke-width: 1px; fill: rgb(255, 170, 0); fill-opacity: 1; stroke: rgb(178, 118, 0); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="79.234375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(449.796875,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Drawdown</text><g class="layers"><g class="legendfill"><path class="js-fill" d="M5,-2h30v6h-30z" style="stroke-width: 0; fill: rgb(255, 111, 0); fill-opacity: 0.3;"/></g><g class="legendlines"><path class="js-line" d="M5,-2h30" style="fill: none; stroke: rgb(255, 111, 0); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="106.078125" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-x2title"><text class="x2title" x="398" y="588.3" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Date</text></g><g class="g-ytitle"><text class="ytitle" transform="rotate(-90,27.309375000000003,177.7)" x="27.309375000000003" y="177.7" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Value</text></g><g class="g-y2title" transform="translate(1.802734375,0)"><text class="y2title" transform="rotate(-90,12.200000000000003,435.3)" x="12.200000000000003" y="435.3" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Drawdown</text></g><g class="annotation" data-index="0" style="opacity: 1;"><g class="annotation-text-g" transform="rotate(0,398,53)"><g class="cursor-pointer" transform="translate(350,41)"><rect class="bg" x="0.5" y="0.5" width="96" height="23" style="stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;"/><text class="annotation-text" text-anchor="middle" x="48.546875" y="18" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Drawdowns</text></g></g></g><g class="annotation" data-index="1" style="opacity: 1;"><g class="annotation-text-g" transform="rotate(0,398,310.6)"><g class="cursor-pointer" transform="translate(349,299)"><rect class="bg" x="0.5" y="0.5" width="97" height="23" style="stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;"/><text class="annotation-text" text-anchor="middle" x="48.765625" y="18" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Underwater</text></g></g></g></g></svg>