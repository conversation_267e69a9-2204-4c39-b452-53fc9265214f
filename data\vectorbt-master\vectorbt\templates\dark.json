{"data": {"histogram2dcontour": [{"type": "histogram2dcontour", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "choropleth": [{"type": "choropleth", "colorbar": {"outlinewidth": 0, "ticks": ""}}], "histogram2d": [{"type": "histogram2d", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "heatmap": [{"type": "heatmap", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "contourcarpet": [{"type": "contourcarpet", "colorbar": {"outlinewidth": 0, "ticks": ""}}], "contour": [{"type": "contour", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "surface": [{"type": "surface", "colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]]}], "mesh3d": [{"type": "mesh3d", "colorbar": {"outlinewidth": 0, "ticks": ""}}], "scatter": [{"marker": {"line": {"color": "#2f3e4e"}}, "type": "scatter"}], "parcoords": [{"type": "parcoords", "line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatterpolargl": [{"type": "scatterpolargl", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "bar": [{"error_x": {"color": "#d6dfef"}, "error_y": {"color": "#d6dfef"}, "marker": {"line": {"color": "#1f2536", "width": 0.5}}, "type": "bar"}], "scattergeo": [{"type": "scattergeo", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatterpolar": [{"type": "scatterpolar", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "histogram": [{"type": "histogram", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scattergl": [{"marker": {"line": {"color": "#2f3e4e"}}, "type": "scattergl"}], "scatter3d": [{"type": "scatter3d", "line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scattermapbox": [{"type": "scattermapbox", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scatterternary": [{"type": "scatterternary", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "scattercarpet": [{"type": "scattercarpet", "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}}], "carpet": [{"aaxis": {"endlinecolor": "#A2B1C6", "gridcolor": "#323b56", "linecolor": "#323b56", "minorgridcolor": "#323b56", "startlinecolor": "#A2B1C6"}, "baxis": {"endlinecolor": "#A2B1C6", "gridcolor": "#323b56", "linecolor": "#323b56", "minorgridcolor": "#323b56", "startlinecolor": "#A2B1C6"}, "type": "carpet"}], "table": [{"cells": {"fill": {"color": "#323b56"}, "line": {"color": "#1f2536"}}, "header": {"fill": {"color": "#2a3f5f"}, "line": {"color": "#1f2536"}}, "type": "table"}], "barpolar": [{"marker": {"line": {"color": "#1f2536", "width": 0.5}}, "type": "barpolar"}], "pie": [{"automargin": true, "type": "pie"}]}, "layout": {"colorway": ["#1f77b4", "#ff7f0e", "#2ca02c", "#dc3912", "#9467bd", "#8c564b", "#e377c2", "#7f7f7f", "#bcbd22", "#17becf"], "font": {"color": "#d6dfef"}, "hovermode": "closest", "hoverlabel": {"align": "left"}, "paper_bgcolor": "#1f2536", "plot_bgcolor": "#1f2536", "polar": {"bgcolor": "#1f2536", "angularaxis": {"gridcolor": "#323b56", "linecolor": "#323b56", "ticks": ""}, "radialaxis": {"gridcolor": "#323b56", "linecolor": "#323b56", "ticks": ""}}, "ternary": {"bgcolor": "#1f2536", "aaxis": {"gridcolor": "#323b56", "linecolor": "#323b56", "ticks": ""}, "baxis": {"gridcolor": "#323b56", "linecolor": "#323b56", "ticks": ""}, "caxis": {"gridcolor": "#323b56", "linecolor": "#323b56", "ticks": ""}}, "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"sequential": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "sequentialminus": [[0.0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1.0, "#f0f921"]], "diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]]}, "xaxis": {"gridcolor": "#2f3e4e", "linecolor": "#323b56", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#2f3e4e", "automargin": true, "zerolinewidth": 2}, "yaxis": {"gridcolor": "#2f3e4e", "linecolor": "#323b56", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#2f3e4e", "automargin": true, "zerolinewidth": 2}, "scene": {"xaxis": {"backgroundcolor": "#1f2536", "gridcolor": "#323b56", "linecolor": "#323b56", "showbackground": true, "ticks": "", "zerolinecolor": "#aec0d6", "gridwidth": 2}, "yaxis": {"backgroundcolor": "#1f2536", "gridcolor": "#323b56", "linecolor": "#323b56", "showbackground": true, "ticks": "", "zerolinecolor": "#aec0d6", "gridwidth": 2}, "zaxis": {"backgroundcolor": "#1f2536", "gridcolor": "#323b56", "linecolor": "#323b56", "showbackground": true, "ticks": "", "zerolinecolor": "#aec0d6", "gridwidth": 2}}, "shapedefaults": {"line": {"color": "#d6dfef"}}, "annotationdefaults": {"arrowcolor": "#d6dfef", "arrowhead": 0, "arrowwidth": 1}, "geo": {"bgcolor": "#1f2536", "landcolor": "#1f2536", "subunitcolor": "#323b56", "showland": true, "showlakes": true, "lakecolor": "#1f2536"}, "title": {"x": 0.05}, "updatemenudefaults": {"bgcolor": "#323b56", "borderwidth": 0}, "sliderdefaults": {"bgcolor": "#aec0d6", "borderwidth": 1, "bordercolor": "#1f2536", "tickwidth": 0}, "mapbox": {"style": "dark"}}}