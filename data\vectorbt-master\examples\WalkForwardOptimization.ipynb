{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import vectorbt as vbt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["split_kwargs = dict(\n", "    n=30, \n", "    window_len=365 * 2, \n", "    set_lens=(180,), \n", "    left_to_right=False\n", ")  # 30 windows, each 2 years long, reserve 180 days for test\n", "pf_kwargs = dict(\n", "    direction='both',  # long and short\n", "    freq='d'\n", ")\n", "windows = np.arange(10, 50)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["price = vbt.YFData.download('BTC-USD').get('Close')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Date\n", "2014-09-17 00:00:00+00:00      457.334015\n", "2014-09-18 00:00:00+00:00      424.440002\n", "2014-09-19 00:00:00+00:00      394.795990\n", "2014-09-20 00:00:00+00:00      408.903992\n", "2014-09-21 00:00:00+00:00      398.821014\n", "                                 ...     \n", "2021-08-21 00:00:00+00:00    48905.492188\n", "2021-08-22 00:00:00+00:00    49321.652344\n", "2021-08-23 00:00:00+00:00    49546.148438\n", "2021-08-24 00:00:00+00:00    47706.117188\n", "2021-08-25 00:00:00+00:00    47449.765625\n", "Name: Close, Length: 2531, dtype: float64\n"]}], "source": ["print(price)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-60e299\"><g class=\"clips\"><clipPath id=\"clip60e299xyplot\" class=\"plotclip\"><rect width=\"635\" height=\"274\"/></clipPath><clipPath class=\"axesclip\" id=\"clip60e299x\"><rect x=\"35\" y=\"0\" width=\"635\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip60e299y\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"274\"/></clipPath><clipPath class=\"axesclip\" id=\"clip60e299xy\"><rect x=\"35\" y=\"46\" width=\"635\" height=\"274\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"35\" y=\"46\" width=\"635\" height=\"274\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(61.56,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(153.03,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(244.75,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(336.21,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(427.68,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(519.14,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(610.86,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,268.05)\" d=\"M35,0h635\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,229.11)\" d=\"M35,0h635\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,190.17)\" d=\"M35,0h635\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,151.23000000000002)\" d=\"M35,0h635\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,112.28)\" d=\"M35,0h635\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,73.34)\" d=\"M35,0h635\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,306.99)\" d=\"M35,0h635\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(35,46)\" clip-path=\"url(#clip60e299xyplot)\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter tracef8d3e869-ba1a-4881-aae3-a67bb60b7652\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,259.21L1.75,259.35L2,259.39L9.77,259.61L10.02,259.62L20.3,259.53L20.55,259.58L26.31,259.75L26.56,259.77L102.99,259.59L103.24,259.42L107,259.69L107.25,259.72L112.27,259.37L112.52,259.38L114.02,259.22L114.27,259.22L120.28,259.25L120.53,259.25L125.05,259.52L125.3,259.52L129.31,259.44L129.56,259.41L134.07,259.43L134.32,259.41L141.08,259.35L141.33,259.35L158.62,258.63L158.88,258.37L160.88,258.12L161.13,258.4L162.13,258.4L162.38,258.54L164.64,258.38L164.89,258.36L166.39,258.41L166.64,258.44L176.92,258.72L177.17,258.73L189.7,258.51L189.95,258.5L198.47,258.11L198.72,258.07L208.24,257.46L208.49,257.36L210.5,256.5L210.75,257.05L211.75,257.48L212,257.46L213,257.81L213.25,257.79L226.54,256.37L226.79,256.64L228.04,256.13L228.29,256.37L229.29,256.89L229.54,256.63L230.8,257.23L231.05,256.92L236.81,256.28L237.06,256.21L239.57,255.74L239.82,255.46L243.07,253.95L243.32,254.22L244.08,253.64L244.33,253.25L245.58,251.48L245.83,252.02L246.08,252.42L246.33,253.05L246.58,252.6L246.83,252.21L247.08,252.52L247.33,252.09L247.58,251.62L250.09,249.47L250.34,250.64L250.59,250.41L250.84,251.23L251.34,251.19L251.59,250.65L251.84,251.07L252.1,250.91L252.35,250.39L255.1,251.51L255.35,251.23L256.36,250.84L256.61,251.19L257.11,251.19L257.36,251.75L258.36,252.3L258.61,253.21L258.86,253.48L259.11,252.32L259.36,251.96L259.61,252.14L259.86,250.02L260.11,250.6L260.62,250.36L260.87,250.27L261.37,251.14L261.62,250.59L261.87,250.05L262.87,250.41L263.12,250.44L264.63,247.68L264.88,247.98L265.13,247.83L266.63,243.95L266.88,244.13L267.88,245.41L268.13,245.03L270.39,242.68L270.64,241.94L271.39,244.5L271.64,243.95L271.89,243.09L272.14,243.08L272.39,244.53L273.4,244.91L273.65,245.87L273.9,248.71L274.4,246.88L274.65,247.04L274.9,245.16L275.4,245.78L275.65,246.85L276.4,246.65L276.65,245.7L276.9,245.84L277.15,244.64L277.66,244.78L277.91,244.1L278.41,243.82L278.66,244.18L278.91,244.52L280.66,242.2L280.91,239.78L281.41,238.28L281.92,238.7L282.17,239.16L282.67,238.76L282.92,237.58L283.67,237.9L283.92,239.47L285.17,237.03L285.42,237.12L286.93,232.15L287.18,233.65L287.68,231.94L287.93,233.18L288.68,237.82L288.93,235.45L289.18,235.15L290.44,229.7L290.69,229.06L291.44,229.69L291.69,228.85L293.69,217.87L293.94,216.9L294.45,214.59L294.7,205.34L294.95,191.29L295.45,201.89L295.7,200.81L296.2,193.17L296.45,197.1L296.7,196.49L297.2,185.07L297.7,186.56L297.95,191.77L298.71,207.13L299.21,206.76L299.46,206.37L299.71,198.3L300.21,204.11L300.46,203.92L300.71,210.56L301.21,207.81L301.46,202.65L301.96,200.25L302.21,193.12L302.46,192.74L302.72,196.83L303.72,208.79L303.97,206.55L304.22,205.07L304.47,207.36L304.72,207.18L305.22,217.42L305.47,216.31L305.72,215.79L305.97,210.76L306.22,215.82L306.47,218.42L306.72,218.67L306.98,216.76L307.48,217.49L307.73,216.44L307.98,215.1L308.23,217L308.48,221.64L308.73,221.19L308.98,225.28L309.73,228.76L309.98,233.91L310.98,226.97L311.24,227.42L311.49,229.33L312.99,217.72L313.24,219.9L313.74,216.59L313.99,219.36L314.24,222.03L314.49,220.88L314.74,222.78L314.99,223.36L316,218.35L316.25,217.82L317,215.93L317.25,219.01L318.25,226.47L318.5,223.69L320.01,230.16L320.26,228.97L321.01,226.22L321.26,227L321.51,226.41L323.76,234.34L324.02,233.41L324.27,231.96L324.52,234.3L324.77,234.47L325.02,235.15L325.27,234.08L325.52,233.64L325.77,234.63L326.27,233.86L326.52,230.27L327.02,229.89L327.27,228.56L327.77,230.22L328.02,229.2L328.28,228.69L329.53,223.23L329.78,226.55L330.78,224.31L331.03,225.01L331.28,225.48L331.53,225.03L331.78,223.05L332.54,223.4L332.79,224.49L333.54,225.77L333.79,228.12L334.54,227.05L334.79,227.85L335.29,229.47L335.79,228.88L336.04,227.84L336.54,229.68L336.8,231.56L337.3,231.86L337.55,232.35L337.8,232.3L338.05,233.2L339.05,231.63L339.3,231.23L339.8,231.73L340.3,231.19L340.55,231.09L341.06,231.66L341.31,234.57L341.56,234.1L341.81,235.36L342.06,236.27L342.31,235L342.56,235.85L342.81,235.49L343.06,235.68L343.31,234.77L344.06,234.79L344.31,237.3L345.07,236.66L345.32,237.26L345.57,237.02L345.82,238L346.82,235.24L347.07,235.57L347.32,235.3L348.57,234.74L348.82,236.34L349.07,236.09L349.33,236.74L349.58,236.7L350.08,236.23L350.33,234.74L351.08,231.92L351.33,232.36L351.58,232.1L351.83,232.1L352.08,230.97L352.33,228.19L352.83,230.03L353.08,229.2L353.84,229.14L354.09,230.7L356.59,236.91L356.84,236.48L357.09,236.37L358.1,236.33L358.35,235.37L359.1,236.43L359.35,235.73L359.6,236.16L359.85,235.55L360.1,234.82L360.85,234.18L361.1,233.36L361.85,233.59L362.11,232.98L362.86,232.33L363.11,234.54L363.86,236.75L364.11,236.46L364.36,236.34L364.86,236.26L365.11,235.61L365.86,235.61L366.11,236.53L367.62,234.86L367.87,235.31L368.12,235.89L368.37,235.7L368.62,234.99L370.37,235.38L370.63,235.2L371.88,235.35L372.13,236.63L372.88,236.5L373.13,235.31L376.39,235.73L376.64,236.33L378.64,235.83L378.9,235.56L380.4,236.23L380.65,238.65L381.4,239.36L381.65,239.09L381.9,242.02L383.16,245.88L383.41,245.38L383.66,246.28L383.91,246.12L384.16,244.41L384.41,244.33L384.66,245.35L384.91,244.58L385.16,244.87L385.41,245.83L385.66,245.58L385.91,246.37L386.16,247.28L386.41,247.68L386.66,247.46L386.91,246.92L388.42,248.39L388.67,248.33L389.67,244.89L389.92,245.82L390.17,245.36L390.67,245.11L390.92,246.14L391.17,245.97L391.42,246.76L391.68,245.71L392.18,245.94L392.43,246.42L392.93,245.64L393.43,245.97L393.68,246.02L393.93,245.12L394.43,245.3L394.68,245.28L395.68,247.16L395.94,246.56L396.94,246.75L397.19,246.47L398.19,247.03L398.44,246.97L401.95,247.76L402.2,246.71L404.46,246.69L404.71,245.75L405.96,244.86L406.21,246.16L407.21,245.98L407.46,245.96L410.97,245.57L411.22,245.23L413.73,245.47L413.98,245.08L415.23,244.8L415.48,241.99L417.49,240.26L417.74,241.27L420.49,239.97L420.74,239.29L421.75,240.48L422,240.41L422.5,240.16L422.75,239.95L425,236.15L425.25,232.94L425.51,233.84L425.76,230.56L426.26,229.04L426.51,230.29L426.76,232.4L427.01,232.68L427.26,229.07L428.01,231.09L428.26,230.3L428.51,229.89L428.76,229.64L429.01,227.22L429.26,226.7L429.77,227.27L430.02,228.6L430.77,226.95L431.02,229.03L431.27,230.98L431.77,230.53L432.02,229.67L432.52,231.05L432.77,229.84L433.02,230.12L433.27,229.27L433.52,228.94L433.77,227.14L435.28,223.89L435.53,221.49L436.78,210.31L437.03,217.45L437.28,212.68L437.53,214.42L437.78,218.87L438.03,219.78L438.29,218.93L438.54,214.41L439.04,218.24L439.29,217.35L439.54,216.4L440.04,212.03L440.29,213.65L440.54,216.76L440.79,214.98L441.04,216.63L441.29,221.05L441.54,218.57L441.79,224.09L442.04,223.24L442.29,219.46L442.8,219.06L443.3,220.72L443.55,222.44L445.05,223.92L445.3,223.58L446.81,215.02L447.06,216.3L447.56,214.39L447.81,214.8L448.06,216.78L448.31,216.12L448.56,216.67L448.81,218.56L449.06,221.85L449.56,220.59L449.81,221.15L450.06,220.71L450.31,218.48L451.07,221.54L451.32,220.46L451.82,221.51L452.07,220.61L452.32,221.33L452.82,223.96L453.32,223.49L453.57,222.99L454.07,219.62L454.57,219.81L454.82,220.68L455.07,220.04L455.58,220.75L455.83,221.6L457.08,220.7L457.33,220.97L458.83,221.78L459.08,223.11L459.84,229.38L460.09,228.86L460.34,228.88L460.59,229.43L460.84,228.7L461.09,228.5L461.34,228.31L461.59,228.83L461.84,229.04L462.34,229.89L462.59,228.88L462.84,228.95L463.09,227.52L463.34,227.56L463.6,228.59L464.6,229.04L464.85,229.66L466.1,228.89L466.35,229.54L466.85,231.81L467.1,227.27L467.6,223.8L468.11,224.28L468.36,225.14L469.36,225.03L469.61,224.34L470.36,224.9L470.61,226.71L470.86,226.67L471.11,225.73L472.12,227.08L472.37,227.92L472.87,227.59L473.12,228.64L473.87,231.23L474.12,232.58L474.37,232.19L474.62,233.55L475.87,230.77L476.12,231.52L476.38,232.08L477.13,232.75L477.38,231.99L478.13,231.54L478.38,232.17L479.63,233.25L479.88,233.14L480.38,235.13L480.64,232.66L481.39,232.99L481.64,231.74L482.64,232.8L482.89,232.6L483.64,232.59L483.89,232.98L484.14,232.95L484.39,233.79L485.65,229.2L485.9,229.53L486.15,230.31L486.9,229.09L487.15,229.28L488.15,226.22L488.4,226.17L489.66,228.26L489.91,228.11L490.16,228.41L490.41,227.52L491.41,223.96L491.66,224.58L491.91,224.42L492.66,225.24L492.91,223.56L495.17,220.84L495.42,222.48L495.67,222.31L495.92,223.26L496.17,221.5L496.42,223.48L496.67,223.58L497.43,222.35L497.68,223.41L498.93,227.51L499.18,227.65L500.18,225.64L500.43,225.47L501.69,230.19L501.94,241.64L502.19,239.33L502.69,239.99L502.94,241.47L503.44,240.59L503.69,236.88L504.19,236.91L504.44,238.29L504.94,234.77L505.44,234.84L505.69,235.8L506.2,237.93L506.45,235.95L506.95,235.27L507.2,234.54L507.95,234.55L508.2,232.68L509.95,234.34L510.21,234.35L510.46,235.13L510.71,233.28L511.21,232.73L511.46,233L511.71,234.19L511.96,234.2L512.21,233.28L513.46,230.64L513.71,230.59L514.72,225.99L514.97,226.35L515.47,225.93L515.72,224.9L515.97,222.24L516.47,223.63L516.72,226.89L516.97,227.5L517.22,226.71L517.47,224.89L517.72,223.09L518.22,224.48L518.47,223.33L518.98,223.11L519.23,223.91L520.23,226.76L520.48,226.31L520.73,226.59L520.98,225.24L521.73,223.22L521.98,224.15L522.23,221.4L522.73,223.39L522.99,222.83L523.49,223.4L523.74,222.99L523.99,222.94L524.49,222.56L524.74,224.69L525.74,224.19L525.99,223.85L527.25,224.76L527.5,223.42L527.75,223.49L528,224.72L528.75,225.77L529,225.39L529.25,225.2L530.75,225.66L531,224.48L532.01,224.86L532.26,225.01L533.26,225.2L533.51,225.43L534.76,224.49L535.01,223.9L536.02,222.42L536.27,218.19L536.52,218.5L536.77,217.77L537.02,217.72L537.52,215.2L537.77,217.95L539.27,215.22L539.52,215.53L539.78,214.74L540.03,216.56L541.53,213.27L541.78,214.3L542.53,215.85L542.78,215.5L543.03,215.57L543.28,215.14L543.53,216.73L544.79,215.39L545.04,215.51L545.29,214.38L545.54,216.55L545.79,221.1L546.04,220.06L546.29,221.39L546.54,220.96L546.79,220.61L547.04,221.54L547.54,220.64L547.79,220.49L548.04,220.33L548.3,220.79L548.55,219.4L549.8,217.79L550.05,218.4L550.8,221.17L551.05,219.15L552.05,219.24L552.3,218.75L553.31,219.85L553.56,219.39L553.81,218.92L554.06,219.63L554.31,219.41L554.56,218.46L555.31,216.66L556.06,216.49L557.32,215.27L557.57,214.59L558.57,209.95L558.82,210.25L559.07,210.08L559.32,207.82L559.57,209.31L560.07,208.24L560.32,207.33L560.82,208.23L561.08,206.67L561.33,205.95L561.58,200.32L561.83,200.38L562.08,203.23L563.33,197.61L563.58,197.45L564.08,198.86L564.33,195.9L565.59,188.4L565.84,189.46L566.09,189.48L566.34,186.59L566.59,188.05L566.84,194.21L567.09,194.37L567.34,192L567.84,184.57L568.34,186.22L568.59,185.27L568.84,188.17L569.34,185.66L569.6,186.26L570.6,190.67L570.85,187.77L571.6,185.38L571.85,178.01L572.6,168.04L572.85,169.57L573.1,172.19L574.86,155.52L575.11,154.44L577.61,102.12L577.87,104.24L578.12,111.63L578.62,128.89L578.87,115.68L579.12,108.39L579.87,121.62L580.12,118.35L580.37,120.53L580.62,122.56L580.87,140.95L581.12,132.46L581.62,135.25L581.87,134.95L582.13,134.16L582.38,142.48L582.88,127.36L583.38,132.04L583.63,130.39L584.88,108.08L585.13,109.5L586.13,74.43L586.39,76L586.64,77.56L586.89,71.28L587.14,74.29L587.39,69.4L587.64,57.92L587.89,59.74L588.14,43.36L588.39,42.53L588.64,36.92L589.89,80.54L590.14,81.13L590.39,85.22L590.65,67.72L590.9,72.6L591.15,64.19L591.4,71.89L591.65,70.46L591.9,70.52L592.15,61.59L593.15,35.89L593.4,37.73L593.65,22.5L594.15,43.28L594.4,39.79L594.65,31.74L596.66,59.65L596.91,46.28L598.66,29.74L598.91,36.67L599.42,31.01L599.67,34.38L599.92,42.73L600.92,26.54L601.17,27.76L601.42,13.7L601.92,14.44L602.17,21.22L602.42,24.68L602.67,42.08L602.92,43.99L603.17,41.08L603.43,51.07L604.43,70.16L604.68,50.62L604.93,46.68L605.18,47.5L605.43,52.44L605.93,35.8L606.18,40.46L606.43,38.25L606.68,53.3L606.93,37.37L607.18,41.38L607.43,37.64L607.69,32L607.94,34.23L608.19,43.47L608.44,40.18L608.69,69.59L609.19,66.75L609.44,78.9L609.69,80.09L609.94,91.45L610.19,93.9L610.44,116.9L610.69,102.18L611.19,114.82L611.44,125.59L612.2,107.98L612.45,111.31L612.95,126.19L613.2,122.06L613.45,115.61L613.7,118.14L613.95,114.67L614.2,108.31L615.45,130.65L615.7,115.57L616.46,122.55L616.71,108.74L617.21,103.64L617.46,111.66L617.71,112.81L617.96,121.63L618.21,122.3L618.46,121.98L618.71,137.64L619.46,126.01L619.71,137.79L619.96,135.65L620.22,126.06L620.47,126.9L620.72,121.32L621.22,130.26L621.47,128.99L621.72,125.99L621.97,123.58L622.22,129.58L622.47,127.68L622.72,129.15L622.97,132.96L623.72,127.66L623.97,131.88L624.98,138.63L625.23,138.2L625.48,137.17L625.98,144.92L626.23,135.95L626.48,135.16L626.73,130.22L627.48,115.59L627.73,107.54L628.48,96.52L628.74,98.89L628.99,105.32L629.49,112.42L629.74,106.21L630.99,80.44L631.24,83.48L631.49,83.44L631.74,87.98L631.99,74.88L633,86.94L633.25,86.53L633.5,79.07L634.5,68.05L634.75,75.22L635,76.22\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(61.56,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">2015</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(153.03,0)\">2016</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(244.75,0)\">2017</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(336.21,0)\">2018</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(427.68,0)\">2019</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(519.14,0)\">2020</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(610.86,0)\">2021</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"34\" y=\"4.199999999999999\" transform=\"translate(0,306.99)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"34\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,268.05)\">10k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"34\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,229.11)\">20k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"34\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,190.17)\">30k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"34\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,151.23000000000002)\">40k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"34\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,112.28)\">50k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"34\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,73.34)\">60k</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-60e299\"><g class=\"clips\"/><clipPath id=\"legend60e299\"><rect width=\"78\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(592,11.519999999999996)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"78\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legend60e299)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"74.859375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["price.vbt.plot().show_svg()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def roll_in_and_out_samples(price, **kwargs):\n", "    return price.vbt.rolling_split(**kwargs)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"216\" style=\"\" viewBox=\"0 0 700 216\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"216\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-24de82\"><g class=\"clips\"><clipPath id=\"clip24de82xyplot\" class=\"plotclip\"><rect width=\"640\" height=\"142\"/></clipPath><clipPath class=\"axesclip\" id=\"clip24de82x\"><rect x=\"30\" y=\"0\" width=\"640\" height=\"216\"/></clipPath><clipPath class=\"axesclip\" id=\"clip24de82y\"><rect x=\"0\" y=\"44\" width=\"700\" height=\"142\"/></clipPath><clipPath class=\"axesclip\" id=\"clip24de82xy\"><rect x=\"30\" y=\"44\" width=\"640\" height=\"142\"/></clipPath></g><g class=\"gradients\"><linearGradient x1=\"1\" x2=\"0\" y1=\"0\" y2=\"0\" id=\"g24de82-legendfill-55b49322-2427-4846-9cd2-0ad674f87ac5\"><stop offset=\"0%\" stop-color=\"rgb(31, 119, 180)\" stop-opacity=\"1\"/><stop offset=\"100%\" stop-color=\"rgb(31, 119, 180)\" stop-opacity=\"1\"/></linearGradient><linearGradient x1=\"1\" x2=\"0\" y1=\"0\" y2=\"0\" id=\"g24de82-legendfill-747b88e2-6116-4066-8f0f-1068888b5382\"><stop offset=\"0%\" stop-color=\"rgb(255, 127, 14)\" stop-opacity=\"1\"/><stop offset=\"100%\" stop-color=\"rgb(255, 127, 14)\" stop-opacity=\"1\"/></linearGradient></g><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"30\" y=\"44\" width=\"640\" height=\"142\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(56.89,0)\" d=\"M0,44v142\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(149.04000000000002,0)\" d=\"M0,44v142\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(241.44,0)\" d=\"M0,44v142\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(333.59,0)\" d=\"M0,44v142\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(425.74,0)\" d=\"M0,44v142\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(517.89,0)\" d=\"M0,44v142\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(610.29,0)\" d=\"M0,44v142\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,183.63)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,169.43)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,155.23000000000002)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,141.03)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,126.83)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,112.63)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,98.43)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,84.22999999999999)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,70.03)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,55.83)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(30,44)\" clip-path=\"url(#clip24de82xyplot)\"><g class=\"heatmaplayer mlayer\"><g class=\"hm\"><image xmlns=\"http://www.w3.org/2000/svg\" preserveAspectRatio=\"none\" height=\"142\" width=\"640\" x=\"0\" y=\"0\" xlink:href=\"data:image/png;base64,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\" style=\"opacity: 1;\"/></g><g class=\"hm\"><image xmlns=\"http://www.w3.org/2000/svg\" preserveAspectRatio=\"none\" height=\"142\" width=\"640\" x=\"0\" y=\"0\" xlink:href=\"data:image/png;base64,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\" style=\"opacity: 1;\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"199\" transform=\"translate(56.89,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">2015</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"199\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(149.04000000000002,0)\">2016</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"199\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(241.44,0)\">2017</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"199\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(333.59,0)\">2018</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"199\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(425.74,0)\">2019</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"199\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(517.89,0)\">2020</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"199\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(610.29,0)\">2021</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,183.63)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">29</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,169.43)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">26</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,155.23000000000002)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">23</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,141.03)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">20</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,126.83)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">17</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,112.63)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">14</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,98.43)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">11</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,84.22999999999999)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">8</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,70.03)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,55.83)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">2</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-24de82\"><g class=\"clips\"/><clipPath id=\"legend24de82\"><rect width=\"218\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(452,12.159999999999997)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"218\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legend24de82)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">in-sample</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legend3dandfriends\" transform=\"translate(20,0)\" d=\"M-15,-2V4H15V-2Z\" style=\"stroke-miterlimit: 1; fill: url('#g24de82-legendfill-55b49322-2427-4846-9cd2-0ad674f87ac5');\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"101.890625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(104.390625,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">out-sample</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legend3dandfriends\" transform=\"translate(20,0)\" d=\"M-15,-2V4H15V-2Z\" style=\"stroke-miterlimit: 1; fill: url('#g24de82-legendfill-747b88e2-6116-4066-8f0f-1068888b5382');\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"110.609375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["roll_in_and_out_samples(price, **split_kwargs, plot=True, trace_names=['in-sample', 'out-sample']).show_svg()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(550, 30) 30\n", "(180, 30) 30\n"]}], "source": ["(in_price, in_indexes), (out_price, out_indexes) = roll_in_and_out_samples(price, **split_kwargs)\n", "\n", "print(in_price.shape, len(in_indexes))  # in-sample\n", "print(out_price.shape, len(out_indexes))  # out-sample"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def simulate_holding(price, **kwargs):\n", "    pf = vbt.Portfolio.from_holding(price, **kwargs)\n", "    return pf.sharpe_ratio()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["split_idx\n", "0     0.229171\n", "1     0.493885\n", "2     1.576114\n", "3     1.255349\n", "4     1.697116\n", "5     1.606954\n", "6     2.000155\n", "7     2.398767\n", "8     2.377776\n", "9     2.539653\n", "10    3.037085\n", "11    2.605911\n", "12    2.259943\n", "13    2.064727\n", "14    1.840279\n", "15    1.695307\n", "16    0.631063\n", "17    0.493426\n", "18    0.466068\n", "19   -0.139716\n", "20    0.576409\n", "21    0.395839\n", "22    0.402171\n", "23    0.779667\n", "24    0.484766\n", "25    1.206379\n", "26    1.356616\n", "27    1.193132\n", "28    1.207407\n", "29    1.740877\n", "Name: sharpe_ratio, dtype: float64\n"]}], "source": ["in_hold_sharpe = simulate_holding(in_price, **pf_kwargs)\n", "\n", "print(in_hold_sharpe)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def simulate_all_params(price, windows, **kwargs):\n", "    fast_ma, slow_ma = vbt.MA.run_combs(price, windows, r=2, short_names=['fast', 'slow'])\n", "    entries = fast_ma.ma_crossed_above(slow_ma)\n", "    exits = fast_ma.ma_crossed_below(slow_ma)\n", "    pf = vbt.Portfolio.from_signals(price, entries, exits, **kwargs)\n", "    return pf.sharpe_ratio()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["fast_window  slow_window  split_idx\n", "10           11           0            0.856870\n", "                          1            1.125426\n", "                          2            0.497444\n", "                          3            0.366434\n", "                          4            0.845251\n", "                                         ...   \n", "48           49           25          -0.072416\n", "                          26          -0.403375\n", "                          27          -1.093233\n", "                          28          -0.921787\n", "                          29          -0.593033\n", "Name: sharpe_ratio, Length: 23400, dtype: float64\n"]}], "source": ["# Simulate all params for in-sample ranges\n", "in_sharpe = simulate_all_params(in_price, windows, **pf_kwargs)\n", "\n", "print(in_sharpe)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def get_best_index(performance, higher_better=True):\n", "    if higher_better:\n", "        return performance[performance.groupby('split_idx').idxmax()].index\n", "    return performance[performance.groupby('split_idx').idxmin()].index"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MultiIndex([(27, 29,  0),\n", "            (19, 27,  1),\n", "            (21, 25,  2),\n", "            (23, 25,  3),\n", "            (23, 25,  4),\n", "            (44, 45,  5),\n", "            (30, 48,  6),\n", "            (37, 43,  7),\n", "            (10, 21,  8),\n", "            (10, 21,  9),\n", "            (10, 21, 10),\n", "            (10, 21, 11),\n", "            (10, 21, 12),\n", "            (10, 21, 13),\n", "            (10, 21, 14),\n", "            (10, 22, 15),\n", "            (10, 22, 16),\n", "            (10, 22, 17),\n", "            (17, 22, 18),\n", "            (18, 19, 19),\n", "            (13, 21, 20),\n", "            (45, 49, 21),\n", "            (45, 49, 22),\n", "            (18, 21, 23),\n", "            (13, 21, 24),\n", "            (15, 18, 25),\n", "            (13, 20, 26),\n", "            (13, 20, 27),\n", "            (13, 20, 28),\n", "            (13, 20, 29)],\n", "           names=['fast_window', 'slow_window', 'split_idx'])\n"]}], "source": ["in_best_index = get_best_index(in_sharpe)\n", "\n", "print(in_best_index)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def get_best_params(best_index, level_name):\n", "    return best_index.get_level_values(level_name).to_numpy()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[27 29]\n", " [19 27]\n", " [21 25]\n", " [23 25]\n", " [23 25]\n", " [44 45]\n", " [30 48]\n", " [37 43]\n", " [10 21]\n", " [10 21]\n", " [10 21]\n", " [10 21]\n", " [10 21]\n", " [10 21]\n", " [10 21]\n", " [10 22]\n", " [10 22]\n", " [10 22]\n", " [17 22]\n", " [18 19]\n", " [13 21]\n", " [45 49]\n", " [45 49]\n", " [18 21]\n", " [13 21]\n", " [15 18]\n", " [13 20]\n", " [13 20]\n", " [13 20]\n", " [13 20]]\n"]}], "source": ["in_best_fast_windows = get_best_params(in_best_index, 'fast_window')\n", "in_best_slow_windows = get_best_params(in_best_index, 'slow_window')\n", "in_best_window_pairs = np.array(list(zip(in_best_fast_windows, in_best_slow_windows)))\n", "\n", "print(in_best_window_pairs)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-36c1d8\"><g class=\"clips\"><clipPath id=\"clip36c1d8xyplot\" class=\"plotclip\"><rect width=\"640\" height=\"274\"/></clipPath><clipPath class=\"axesclip\" id=\"clip36c1d8x\"><rect x=\"30\" y=\"0\" width=\"640\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip36c1d8y\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"274\"/></clipPath><clipPath class=\"axesclip\" id=\"clip36c1d8xy\"><rect x=\"30\" y=\"46\" width=\"640\" height=\"274\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"30\" y=\"46\" width=\"640\" height=\"274\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(140.34,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(250.69,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(361.03,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(471.38,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(581.72,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,306.3)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,243.07)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,179.84)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,116.61)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,53.38)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(30,0)\" d=\"M0,46v274\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(30,46)\" clip-path=\"url(#clip36c1d8xyplot)\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter traced92e3d3c-cad3-4c35-8d06-8cda85cc3c63\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,152.81L22.07,203.39L44.14,190.75L66.21,178.1L88.28,178.1L110.34,45.32L132.41,133.84L154.48,89.58L176.55,260.3L375.17,260.3L397.24,216.04L419.31,209.72L441.38,241.33L463.45,38.99L485.52,38.99L507.59,209.72L529.66,241.33L551.72,228.68L573.79,241.33L640,241.33\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter tracec0f02b22-44d3-4418-a460-88d364277b44\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,140.16L44.14,165.45L66.21,165.45L88.28,165.45L110.34,38.99L132.41,20.02L154.48,51.64L176.55,190.75L198.62,190.75L308.97,190.75L331.03,184.42L397.24,184.42L419.31,203.39L441.38,190.75L463.45,13.7L485.52,13.7L507.59,190.75L529.66,190.75L551.72,209.72L573.79,197.07L595.86,197.07L640,197.07\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(30,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(140.34,0)\">5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(250.69,0)\">10</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(361.03,0)\">15</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(471.38,0)\">20</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(581.72,0)\">25</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,306.3)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">10</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,243.07)\">20</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,179.84)\">30</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,116.61)\">40</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,53.38)\">50</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-36c1d8\"><g class=\"clips\"/><clipPath id=\"legend36c1d8\"><rect width=\"245\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(425,11.519999999999996)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"245\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legend36c1d8)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">fast_window</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"117.828125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(120.328125,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">slow_window</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"122.078125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pd.DataFrame(in_best_window_pairs, columns=['fast_window', 'slow_window']).vbt.plot().show_svg()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["split_idx\n", "0     1.773721\n", "1     2.255167\n", "2     1.605040\n", "3     2.302425\n", "4     3.470567\n", "5     3.208988\n", "6     3.335303\n", "7     3.245646\n", "8     3.099480\n", "9     1.633831\n", "10   -0.118486\n", "11    0.140842\n", "12    0.192643\n", "13   -1.635978\n", "14   -1.918136\n", "15   -0.525469\n", "16    2.720792\n", "17    3.453221\n", "18    1.631937\n", "19    0.085609\n", "20    0.291843\n", "21    0.060253\n", "22    1.204436\n", "23    0.876595\n", "24    2.097729\n", "25    3.843597\n", "26    4.741930\n", "27    3.892515\n", "28    1.066166\n", "29    0.470709\n", "Name: sharpe_ratio, dtype: float64\n"]}], "source": ["out_hold_sharpe = simulate_holding(out_price, **pf_kwargs)\n", "\n", "print(out_hold_sharpe)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["fast_window  slow_window  split_idx\n", "10           11           0           -1.018512\n", "                          1           -0.279485\n", "                          2           -0.097736\n", "                          3            1.209532\n", "                          4            2.518178\n", "                                         ...   \n", "48           49           25           0.938274\n", "                          26           4.069947\n", "                          27           3.016070\n", "                          28           0.108060\n", "                          29          -0.482026\n", "Name: sharpe_ratio, Length: 23400, dtype: float64\n"]}], "source": ["# Simulate all params for out-sample ranges\n", "out_sharpe = simulate_all_params(out_price, windows, **pf_kwargs)\n", "\n", "print(out_sharpe)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def simulate_best_params(price, best_fast_windows, best_slow_windows, **kwargs):\n", "    fast_ma = vbt.MA.run(price, window=best_fast_windows, per_column=True)\n", "    slow_ma = vbt.MA.run(price, window=best_slow_windows, per_column=True)\n", "    entries = fast_ma.ma_crossed_above(slow_ma)\n", "    exits = fast_ma.ma_crossed_below(slow_ma)\n", "    pf = vbt.Portfolio.from_signals(price, entries, exits, **kwargs)\n", "    return pf.sharpe_ratio()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ma_window  ma_window  split_idx\n", "27         29         0            1.164082\n", "19         27         1            0.501229\n", "21         25         2            1.414590\n", "23         25         3            1.031654\n", "                      4            1.431372\n", "44         45         5            1.120318\n", "30         48         6            0.425328\n", "37         43         7           -0.564391\n", "10         21         8            3.439371\n", "                      9            2.783249\n", "                      10           1.187116\n", "                      11           1.771893\n", "                      12           0.341544\n", "                      13           0.065343\n", "                      14           0.030836\n", "           22         15           1.702848\n", "                      16           2.727809\n", "                      17           0.861896\n", "17         22         18           0.476459\n", "18         19         19           0.433287\n", "13         21         20           1.762454\n", "45         49         21          -2.053022\n", "                      22          -1.580201\n", "18         21         23           0.849560\n", "13         21         24           1.261873\n", "15         18         25           4.088163\n", "13         20         26           3.851092\n", "                      27           1.234676\n", "                      28          -1.773549\n", "                      29          -1.803552\n", "Name: sharpe_ratio, dtype: float64\n"]}], "source": ["# Use best params from in-sample ranges and simulate them for out-sample ranges\n", "out_test_sharpe = simulate_best_params(out_price, in_best_fast_windows, in_best_slow_windows, **pf_kwargs)\n", "\n", "print(out_test_sharpe)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["cv_results_df = pd.DataFrame({\n", "    'in_sample_hold': in_hold_sharpe.values,\n", "    'in_sample_median': in_sharpe.groupby('split_idx').median().values,\n", "    'in_sample_best': in_sharpe[in_best_index].values,\n", "    'out_sample_hold': out_hold_sharpe.values,\n", "    'out_sample_median': out_sharpe.groupby('split_idx').median().values,\n", "    'out_sample_test': out_test_sharpe.values\n", "})"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-eacb48\"><g class=\"clips\"><clipPath id=\"clipeacb48xyplot\" class=\"plotclip\"><rect width=\"640\" height=\"255\"/></clipPath><clipPath class=\"axesclip\" id=\"clipeacb48x\"><rect x=\"30\" y=\"0\" width=\"640\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clipeacb48y\"><rect x=\"0\" y=\"65\" width=\"700\" height=\"255\"/></clipPath><clipPath class=\"axesclip\" id=\"clipeacb48xy\"><rect x=\"30\" y=\"65\" width=\"640\" height=\"255\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"30\" y=\"65\" width=\"640\" height=\"255\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(140.34,0)\" d=\"M0,65v255\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(250.69,0)\" d=\"M0,65v255\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(361.03,0)\" d=\"M0,65v255\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(471.38,0)\" d=\"M0,65v255\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(581.72,0)\" d=\"M0,65v255\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,305.46000000000004)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,170.36)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,102.81)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(30,0)\" d=\"M0,65v255\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/><path class=\"yzl zl crisp\" transform=\"translate(0,237.91)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(30,65)\" clip-path=\"url(#clipeacb48xyplot)\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace941418f1-edca-4d53-954a-37bc00e754b3\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,165.17L22.07,156.23L44.14,119.68L66.21,130.51L88.28,115.59L110.34,118.63L132.41,105.35L154.48,91.89L176.55,92.6L198.62,87.13L220.69,70.33L242.76,84.89L264.83,96.58L286.9,103.17L308.97,110.75L331.03,115.65L353.1,151.59L375.17,156.24L397.24,157.17L419.31,177.63L441.38,153.44L463.45,159.54L485.52,159.33L507.59,146.58L529.66,156.54L551.72,132.16L573.79,127.09L595.86,132.61L617.93,132.13L640,114.11\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter tracecb488cbd-db09-4d51-a3b6-480d46982da4\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,151.83L22.07,141.96L44.14,145.38L66.21,144.53L88.28,131.38L110.34,128.21L132.41,142.37L154.48,130.63L176.55,125.31L198.62,127.7L220.69,119.34L242.76,110.77L264.83,116.32L286.9,130.27L308.97,130.07L331.03,135.96L353.1,136.38L375.17,144.84L397.24,140.39L419.31,148.3L441.38,145.51L463.45,135.44L485.52,146.69L507.59,132.39L529.66,141.99L551.72,136.06L573.79,135.18L595.86,155.26L617.93,135.02L640,129.08\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1; stroke-dasharray: 9px, 9px;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace5b9a7e3f-f38d-44c2-8572-a7a728b7d0e5\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,103.9L22.07,105.14L44.14,105.36L66.21,115.55L88.28,98.68L132.41,108.57L154.48,99.37L176.55,98.99L198.62,91.57L220.69,68.39L242.76,60.49L264.83,63.95L286.9,71.28L308.97,67.4L331.03,76.26L353.1,88.87L375.17,107.84L397.24,97.38L419.31,103.97L463.45,97.84L485.52,100.41L507.59,95.57L529.66,93.87L551.72,100.53L573.79,96.62L595.86,104.51L617.93,84.98L640,81.92\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1; stroke-dasharray: 3px, 3px;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter tracefb9440de-2637-4a77-b5d7-e014e0941d00\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,113L22.07,96.74L44.14,118.7L66.21,95.14L88.28,55.69L110.34,64.53L132.41,60.26L154.48,63.29L176.55,68.22L198.62,117.73L220.69,176.91L242.76,168.15L264.83,166.4L286.9,228.16L308.97,237.69L331.03,190.66L353.1,81.01L375.17,56.28L397.24,117.79L419.31,170.02L441.38,163.05L463.45,170.87L485.52,132.23L507.59,143.3L529.66,102.06L551.72,43.09L573.79,12.75L595.86,41.44L617.93,136.9L640,157.01\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace4e1b85dc-c0f2-4377-996c-b6452aa9194e\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,129.28L22.07,125.31L44.14,122.13L66.21,136.28L88.28,133.47L110.34,134L132.41,113.63L154.48,134.77L176.55,110.42L198.62,102.14L220.69,182.32L242.76,184.17L264.83,201.87L286.9,163.09L308.97,115.38L331.03,121.58L353.1,94.21L375.17,101.83L397.24,150.12L419.31,217.25L441.38,184.2L463.45,145.56L485.52,118.95L507.59,142.11L529.66,176.83L551.72,84.05L573.79,41.34L595.86,103.03L617.93,136.54L640,144.64\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1; stroke-dasharray: 9px, 9px;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter trace929c9422-3094-4e4e-b846-b04e1dc43652\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,133.59L22.07,155.98L44.14,125.13L66.21,138.06L88.28,124.56L110.34,135.07L132.41,158.54L154.48,191.97L176.55,56.74L198.62,78.9L220.69,132.81L242.76,113.06L264.83,161.37L286.9,170.7L308.97,171.87L331.03,115.4L353.1,80.78L375.17,143.8L397.24,156.82L419.31,158.27L441.38,113.38L463.45,242.25L485.52,226.28L507.59,144.22L529.66,130.29L551.72,34.83L573.79,42.84L595.86,131.21L617.93,232.81L640,233.82\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1; stroke-dasharray: 3px, 3px;\"/></g><g class=\"points\"/><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(30,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(140.34,0)\">5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(250.69,0)\">10</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(361.03,0)\">15</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(471.38,0)\">20</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(581.72,0)\">25</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,305.46000000000004)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">−2</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,237.91)\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,170.36)\">2</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,102.81)\">4</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-eacb48\"><g class=\"clips\"/><clipPath id=\"legendeacb48\"><rect width=\"481\" height=\"48\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(189,11.899999999999991)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"481\" height=\"48\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legendeacb48)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">in_sample_hold</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"137.34375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(167.3125,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">in_sample_median</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-dasharray: 9px, 9px; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"156.09375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(334.625,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">in_sample_best</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-dasharray: 3px, 3px; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"137.3125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,33.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">out_sample_hold</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"146.0625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(167.3125,33.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">out_sample_median</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-dasharray: 9px, 9px; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"164.8125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(334.625,33.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">out_sample_test</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-dasharray: 3px, 3px; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"143.28125\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["color_schema = vbt.settings['plotting']['color_schema']\n", "\n", "cv_results_df.vbt.plot(\n", "    trace_kwargs=[\n", "        dict(line_color=color_schema['blue']),\n", "        dict(line_color=color_schema['blue'], line_dash='dash'),\n", "        dict(line_color=color_schema['blue'], line_dash='dot'),\n", "        dict(line_color=color_schema['orange']),\n", "        dict(line_color=color_schema['orange'], line_dash='dash'),\n", "        dict(line_color=color_schema['orange'], line_dash='dot')\n", "    ]\n", ").show_svg()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 4}