records packageModules for working with records. Records are the second form of data representation in vectorbt. They allow storing sparse event data such as drawdowns, orders, trades, and positions, without converting them back to the matrix form and occupying the user's memory. Sub-modulesvectorbt.records.base vectorbt.records.col_mapper vectorbt.records.decorators vectorbt.records.mapped_array vectorbt.records.nb