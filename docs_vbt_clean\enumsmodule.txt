enums moduleNamed tuples and enumerated types. Defines enums and other schemas for vectorbt.signals . FactoryMode FactoryModeTFactory mode. { "Entries" : 0 , "Exits" : 1 , "Both" : 2 , "Chain" : 3 } Attributes Entries Generate entries only using generate_func . Takes no input signal arrays. Produces one output signal array - entries . Such generators often have no suffix. Exits Generate exits only using generate_ex_func . Takes one input signal array - entries . Produces one output signal array - exits . Such generators often have suffix 'X'. Both Generate both entries and exits using generate_enex_func . Takes no input signal arrays. Produces two output signal arrays - entries and exits . Such generators often have suffix 'NX'. Chain Generate chain of entries and exits using generate_enex_func . Takes one input signal array - entries . Produces two output signal arrays - new_entries and exits . Such generators often have suffix 'CX'. StopType StopTypeTStop type. { "StopLoss" : 0 , "TrailStop" : 1 , "TakeProfit" : 2 } FactoryModeT classFactoryModeT ( Entries = 0 , Exits = 1 , Both = 2 , Chain = 3 ) FactoryModeT(Entries, Exits, Both, Chain) Superclasses builtins.tuple Both method-wrapperAlias for field number 2 Chain method-wrapperAlias for field number 3 Entries method-wrapperAlias for field number 0 Exits method-wrapperAlias for field number 1 StopTypeT classStopTypeT ( StopLoss = 0 , TrailStop = 1 , TakeProfit = 2 ) StopTypeT(StopLoss, TrailStop, TakeProfit) Superclasses builtins.tuple StopLoss method-wrapperAlias for field number 0 TakeProfit method-wrapperAlias for field number 2 TrailStop method-wrapperAlias for field number 1