col_mapper moduleClass for mapping column arrays. ColumnMapper classColumnMapper ( wrapper , col_arr , ** kwargs ) Used by Records and MappedArray classes to make use of column and group metadata. Superclasses AttrResolver Configured Documented IndexingBase PandasIndexer Pickleable Wrapping Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() PandasIndexer.xs() Pickleable.load() Pickleable.save() Wrapping.config Wrapping.iloc Wrapping.indexing_func() Wrapping.indexing_kwargs Wrapping.loc Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Wrapping.self_aliases Wrapping.wrapper Wrapping.writeable_attrs col_arr propertyColumn array. col_map methodColumn map. More flexible than ColumnMapper.col_range . More suited for mapped arrays. col_range methodColumn index. Faster than ColumnMapper.col_map but only compatible with sorted columns. More suited for records. get_col_arr methodColumnMapper . get_col_arr ( group_by = None ) Get group-aware column array. get_col_map methodColumnMapper . get_col_map ( group_by = None ) Get group-aware column map. get_col_range methodColumnMapper . get_col_range ( group_by = None ) Get group-aware column range. is_sorted methodColumnMapper . is_sorted () Check whether column array is sorted.