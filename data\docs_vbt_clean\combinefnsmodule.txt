combine_fns moduleFunctions for combining arrays. Combine functions combine two or more NumPy arrays using a custom function. The emphasis here is done upon stacking the results into one NumPy array - since vectorbt is all about brute-forcing large spaces of hyperparameters, concatenating the results of each hyperparameter combination into a single DataFrame is important. All functions are available in both Python and Numba-compiled form. apply_and_concat_multiple functionapply_and_concat_multiple ( n , apply_func , * args , show_progress = False , tqdm_kwargs = None , ** kwargs ) Identical to apply_and_concat_one() , except that the result of apply_func must be multiple 1-dim or 2-dim arrays. Each of these arrays at i will be concatenated with the array at the same position at i+1 . apply_and_concat_multiple_nb functionapply_and_concat_multiple_nb ( n , apply_func_nb , * args ) A Numba-compiled version of apply_and_concat_multiple() . Note Output of apply_func_nb must be strictly homogeneous apply_func_nb must be Numba-compiled *args must be Numba-compatible No support for **kwargs apply_and_concat_multiple_ray functionapply_and_concat_multiple_ray ( * args , ** kwargs ) Distributed version of apply_and_concat_multiple() . apply_and_concat_none functionapply_and_concat_none ( n , apply_func , * args , show_progress = False , tqdm_kwargs = None , ** kwargs ) For each value i from 0 to n , apply apply_func with arguments *args and **kwargs , and output nothing. Meant for in-place outputs. apply_func must accept arguments i , *args and **kwargs . apply_and_concat_none_nb functionapply_and_concat_none_nb ( n , apply_func_nb , * args ) A Numba-compiled version of apply_and_concat_none() . Note apply_func_nb must be Numba-compiled *args must be Numba-compatible No support for **kwargs apply_and_concat_one functionapply_and_concat_one ( n , apply_func , * args , show_progress = False , tqdm_kwargs = None , ** kwargs ) For each value i from 0 to n , apply apply_func with arguments *args and **kwargs , and concat the results along axis 1. The result of apply_func must be a single 1-dim or 2-dim array. apply_func must accept arguments i , *args and **kwargs . apply_and_concat_one_nb functionapply_and_concat_one_nb ( n , apply_func_nb , * args ) A Numba-compiled version of apply_and_concat_one() . Note apply_func_nb must be Numba-compiled *args must be Numba-compatible No support for **kwargs apply_and_concat_one_ray functionapply_and_concat_one_ray ( * args , ** kwargs ) Distributed version of apply_and_concat_one() . combine_and_concat functioncombine_and_concat ( obj , others , combine_func , * args , ** kwargs ) Use apply_and_concat_one() to combine obj with each element from others using combine_func . combine_and_concat_nb functioncombine_and_concat_nb ( obj , others , combine_func_nb , * args ) A Numba-compiled version of combine_and_concat() . combine_and_concat_ray functioncombine_and_concat_ray ( obj , others , combine_func , * args , ** kwargs ) Distributed version of combine_and_concat() . combine_multiple functioncombine_multiple ( objs , combine_func , * args , ** kwargs ) Combine objs pairwise into a single object. combine_multiple_nb functioncombine_multiple_nb ( objs , combine_func_nb , * args ) A Numba-compiled version of combine_multiple() . Note combine_func_nb must be Numba-compiled objs and *args must be Numba-compatible objs must be strictly homogeneous No support for **kwargs ray_apply functionray_apply ( n , apply_func , * args , ray_force_init = False , ray_func_kwargs = None , ray_init_kwargs = None , ray_shutdown = False , ** kwargs ) Run apply_func in distributed manner. Set ray_reinit to True to terminate the Ray runtime and initialize a new one. ray_func_kwargs will be passed to ray.remote and ray_init_kwargs to ray.init . Set ray_shutdown to True to terminate the Ray runtime upon the job end. select_and_combine functionselect_and_combine ( i , obj , others , combine_func , * args , ** kwargs ) Combine obj and an element from others at i using combine_func . select_and_combine_nb functionselect_and_combine_nb ( i , obj , others , combine_func_nb , * args ) A Numba-compiled version of select_and_combine() . Note combine_func_nb must be Numba-compiled obj , others and *args must be Numba-compatible others must be strictly homogeneous No support for **kwargs to_2d_multiple_nb functionto_2d_multiple_nb ( a ) Expand the dimensions of each array in a along axis 1. Note a must be strictly homogeneous to_2d_one_nb functionto_2d_one_nb ( a ) Expand the dimensions of array a along axis 1. Note a must be strictly homogeneous