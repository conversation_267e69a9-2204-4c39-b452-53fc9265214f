<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="700" height="350" style="" viewBox="0 0 700 350"><rect x="0" y="0" width="700" height="350" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-f21b1f"><g class="clips"><clipPath id="clipf21b1fxyplot" class="plotclip"><rect width="637" height="261"/></clipPath><clipPath class="axesclip" id="clipf21b1fx"><rect x="33" y="0" width="637" height="350"/></clipPath><clipPath class="axesclip" id="clipf21b1fy"><rect x="0" y="46" width="700" height="261"/></clipPath><clipPath class="axesclip" id="clipf21b1fxy"><rect x="33" y="46" width="637" height="261"/></clipPath></g><g class="gradients"/><g class="patterns"/></defs><g class="bglayer"><rect class="bg" x="33" y="46" width="637" height="261" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"><path data-index="0" fill-rule="evenodd" d="M69.22999999999999,289.58000000000004H163.32V214.19H69.22999999999999Z" clip-path="url(#clipf21b1fxy)" style="opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 128, 0); fill-opacity: 1; stroke-width: 0px;"/><path data-index="1" fill-rule="evenodd" d="M69.22999999999999,289.58000000000004H257.40999999999997V138.81H69.22999999999999Z" clip-path="url(#clipf21b1fxy)" style="opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 128, 0); fill-opacity: 1; stroke-width: 0px;"/><path data-index="2" fill-rule="evenodd" d="M351.5,63.43H445.59V138.81H351.5Z" clip-path="url(#clipf21b1fxy)" style="opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(255, 0, 0); fill-opacity: 1; stroke-width: 0px;"/><path data-index="3" fill-rule="evenodd" d="M351.5,63.43H539.6800000000001V214.19H351.5Z" clip-path="url(#clipf21b1fxy)" style="opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(255, 0, 0); fill-opacity: 1; stroke-width: 0px;"/><path data-index="4" fill-rule="evenodd" d="M351.5,63.43H633.77V289.58000000000004H351.5Z" clip-path="url(#clipf21b1fxy)" style="opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(255, 0, 0); fill-opacity: 1; stroke-width: 0px;"/><path data-index="5" fill-rule="evenodd" d="M351.5,63.43H633.77V289.58000000000004H351.5Z" clip-path="url(#clipf21b1fxy)" style="opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(255, 0, 0); fill-opacity: 1; stroke-width: 0px;"/></g><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(69.22999999999999,0)" d="M0,46v261" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(163.32,0)" d="M0,46v261" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(257.40999999999997,0)" d="M0,46v261" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(351.5,0)" d="M0,46v261" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(445.59,0)" d="M0,46v261" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(539.6800000000001,0)" d="M0,46v261" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(633.77,0)" d="M0,46v261" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,289.58000000000004)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,251.88)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,214.19)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,176.5)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,138.81)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,101.12)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,63.43)" d="M33,0h637" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"/><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(33,46)" clip-path="url(#clipf21b1fxyplot)"><g class="scatterlayer mlayer"><g class="trace scatter tracefbf947d9-9fc9-4263-a704-e69999472392" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M36.23,243.58L318.5,17.43L412.59,92.81L600.77,243.58" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"><path class="point" transform="translate(36.23,243.58)" d="M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z" style="opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;"/><path class="point" transform="translate(130.32,168.19)" d="M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z" style="opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;"/><path class="point" transform="translate(224.41,92.81)" d="M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z" style="opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;"/><path class="point" transform="translate(318.5,17.43)" d="M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z" style="opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;"/><path class="point" transform="translate(412.59,92.81)" d="M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z" style="opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;"/><path class="point" transform="translate(506.68,168.19)" d="M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z" style="opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;"/><path class="point" transform="translate(600.77,243.58)" d="M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z" style="opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace04ff3989-5830-4a44-949e-a4366b891a6d" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(36.23,243.58)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(36.23,243.58)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(318.5,17.43)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(318.5,17.43)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(318.5,17.43)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(318.5,17.43)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracea99c6e88-b30e-485e-b86c-8444dd6576bd" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(130.32,168.19)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(224.41,92.81)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter traceba19e2a9-bc0a-44e0-a222-e8dc43accd67" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(412.59,92.81)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(506.68,168.19)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(600.77,243.58)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace42795de0-0ea9-4b54-bdf8-46c7a842d65b" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(600.77,243.58)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(255, 170, 0); fill-opacity: 1; stroke: rgb(178, 118, 0); stroke-opacity: 1;"/></g><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="320" transform="translate(69.22999999999999,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;"><tspan class="line" dy="0em" x="0" y="320">Jan 1</tspan><tspan class="line" dy="1.3em" x="0" y="320">2020</tspan></text></g><g class="xtick"><text text-anchor="middle" x="0" y="320" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(163.32,0)">Jan 2</text></g><g class="xtick"><text text-anchor="middle" x="0" y="320" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(257.40999999999997,0)">Jan 3</text></g><g class="xtick"><text text-anchor="middle" x="0" y="320" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(351.5,0)">Jan 4</text></g><g class="xtick"><text text-anchor="middle" x="0" y="320" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(445.59,0)">Jan 5</text></g><g class="xtick"><text text-anchor="middle" x="0" y="320" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(539.6800000000001,0)">Jan 6</text></g><g class="xtick"><text text-anchor="middle" x="0" y="320" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(633.77,0)">Jan 7</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" transform="translate(0,289.58000000000004)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">1</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,251.88)">1.5</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,214.19)">2</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,176.5)">2.5</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,138.81)">3</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,101.12)">3.5</text></g><g class="ytick"><text text-anchor="end" x="32" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,63.43)">4</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="iciclelayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-f21b1f"><g class="clips"/><clipPath id="legendf21b1f"><rect width="455" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(215,11.779999999999994)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="455" height="29" x="0" y="0"/><g class="scrollbox" transform="" clip-path="url(#legendf21b1f)"><g class="groups"><g class="traces" transform="translate(0,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Price</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z" style="opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="71.5625" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(74.0625,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Entry</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(66, 133, 244); fill-opacity: 1; stroke: rgb(11, 84, 205); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="74.640625" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(151.203125,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Exit - Profit</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="110.984375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(264.6875,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Exit - Loss</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="105.578125" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(372.765625,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Active</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M3.5,3.5H-3.5V-3.5H3.5Z" style="opacity: 1; stroke-width: 1px; fill: rgb(255, 170, 0); fill-opacity: 1; stroke: rgb(178, 118, 0); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="79.234375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-ytitle"/></g></svg>