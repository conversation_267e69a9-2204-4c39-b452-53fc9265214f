tags moduleUtilities for working with tags. match_tags functionmatch_tags ( tags , in_tags ) Match tags in tags to that in in_tags . Multiple tags in tags are combined using OR rule, that is, returns True if any of them is found in in_tags . If any tag is not an identifier, evaluates it as a boolean expression. All tags in in_tags should be identifiers. Usage from vectorbt.utils.tags import match_tags match_tags ( 'hello' , 'hello' ) True match_tags ( 'hello' , 'world' ) False match_tags ([ 'hello' , 'world' ], 'world' ) True match_tags ( 'hello' , [ 'hello' , 'world' ]) True match_tags ( 'hello and world' , [ 'hello' , 'world' ]) True match_tags ( 'hello and not world' , [ 'hello' , 'world' ]) False