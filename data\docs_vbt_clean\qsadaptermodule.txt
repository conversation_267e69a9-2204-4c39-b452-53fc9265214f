qs_adapter moduleAdapter class for quantstats. Note Accessors do not utilize caching. We can access the adapter from ReturnsAccessor : import numpy as np import pandas as pd import vectorbt as vbt import quantstats as qs np . random . seed ( 42 ) rets = pd . Series ( np . random . uniform ( - 0.1 , 0.1 , size = ( 100 ,))) benchmark_rets = pd . Series ( np . random . uniform ( - 0.1 , 0.1 , size = ( 100 ,))) rets . vbt . returns . qs . r_squared ( benchmark = benchmark_rets ) 0.0011582111228735541 Which is the same as: qs . stats . r_squared ( rets , benchmark_rets ) So why not just using qs.stats ? First, we can define all parameters such as benchmark returns once and avoid passing them repeatedly to every function. Second, vectorbt automatically translates parameters passed to ReturnsAccessor for the use in quantstats. # Defaults that vectorbt understands ret_acc = rets . vbt . returns ( ... benchmark_rets = benchmark_rets , ... freq = 'd' , ... year_freq = '365d' , ... defaults = dict ( risk_free = 0.001 ) ... ) ret_acc . qs . r_squared () 0.0011582111228735541 ret_acc . qs . sharpe () -1.9158923252075455 # Defaults that only quantstats understands qs_defaults = dict ( ... benchmark = benchmark_rets , ... periods = 365 , ... periods_per_year = 365 , ... rf = 0.001 ... ) ret_acc_qs = rets . vbt . returns . qs ( defaults = qs_defaults ) ret_acc_qs . r_squared () 0.0011582111228735541 ret_acc_qs . sharpe () -1.9158923252075455 The adapter automatically passes the returns to the particular function. It also merges the defaults defined in the settings, the defaults passed to ReturnsAccessor , and the defaults passed to QSAdapter itself, and matches them with the argument names listed in the function's signature. For example, the periods and periods_per_year arguments default to the annualization factor ReturnsAccessor.ann_factor , which itself is based on the freq argument. This makes the results produced by quantstats and vectorbt at least somewhat similar. vbt . settings . array_wrapper [ 'freq' ] = 'h' vbt . settings . returns [ 'year_freq' ] = '365d' rets . vbt . returns . sharpe_ratio () # ReturnsAccessor -9.38160953971508 rets . vbt . returns . qs . sharpe () # quantstats via QSAdapter -9.38160953971508 We can still override any argument by overriding its default or by passing it directly to the function: rets . vbt . returns . qs ( defaults = dict ( periods = 252 )) . sharpe () -1.5912029345745982 rets . vbt . returns . qs . sharpe ( periods = 252 ) -1.5912029345745982 qs . stats . sharpe ( rets ) -1.5912029345745982 attach_qs_methods functionattach_qs_methods ( cls , replace_signature = True ) Class decorator to attach quantstats methods. QSAdapter classQSAdapter ( returns_accessor , defaults = None , ** kwargs ) Adapter class for quantstats. Superclasses Configured Documented Pickleable Inherited members Configured.config Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Configured.writeable_attrs Pickleable.load() Pickleable.save() adjusted_sortino methodQSAdapter . adjusted_sortino ( * , rf = 0 , periods = 252 , annualize = True , smart = False ) See quantstats.stats.adjusted_sortino . aggregate_returns methodQSAdapter . aggregate_returns ( * , period = None , compounded = True ) See quantstats.utils.aggregate_returns . autocorr_penalty methodQSAdapter . autocorr_penalty ( * , prepare_returns = False ) See quantstats.stats.autocorr_penalty . avg_loss methodQSAdapter . avg_loss ( * , aggregate = None , compounded = True , prepare_returns = True ) See quantstats.stats.avg_loss . avg_return methodQSAdapter . avg_return ( * , aggregate = None , compounded = True , prepare_returns = True ) See quantstats.stats.avg_return . avg_win methodQSAdapter . avg_win ( * , aggregate = None , compounded = True , prepare_returns = True ) See quantstats.stats.avg_win . basic_report methodQSAdapter . basic_report ( * , benchmark = None , rf = 0.0 , grayscale = False , figsize = ( 8 , 5 ), display = True , compounded = True , periods_per_year = 252 , match_dates = True , ** kwargs ) See quantstats.reports.basic . best methodQSAdapter . best ( * , aggregate = None , compounded = True , prepare_returns = True ) See quantstats.stats.best . cagr methodQSAdapter . cagr ( * , rf = 0.0 , compounded = True , periods = 252 ) See quantstats.stats.cagr . calmar methodQSAdapter . calmar ( * , prepare_returns = True ) See quantstats.stats.calmar . common_sense_ratio methodQSAdapter . common_sense_ratio ( * , prepare_returns = True ) See quantstats.stats.common_sense_ratio . comp methodQSAdapter . comp () See quantstats.stats.comp . compare methodQSAdapter . compare ( * , benchmark , aggregate = None , compounded = True , round_vals = None , prepare_returns = True ) See quantstats.stats.compare . compsum methodQSAdapter . compsum () See quantstats.stats.compsum . conditional_value_at_risk methodQSAdapter . conditional_value_at_risk ( * , sigma = 1 , confidence = 0.95 , prepare_returns = True ) See quantstats.stats.conditional_value_at_risk . consecutive_losses methodQSAdapter . consecutive_losses ( * , aggregate = None , compounded = True , prepare_returns = True ) See quantstats.stats.consecutive_losses . consecutive_wins methodQSAdapter . consecutive_wins ( * , aggregate = None , compounded = True , prepare_returns = True ) See quantstats.stats.consecutive_wins . cpc_index methodQSAdapter . cpc_index ( * , prepare_returns = True ) See quantstats.stats.cpc_index . cvar methodQSAdapter . cvar ( * , sigma = 1 , confidence = 0.95 , prepare_returns = True ) See quantstats.stats.cvar . defaults propertyDefaults for QSAdapter . Merges qs_adapter.defaults from settings , returns_accessor.defaults (with adapted naming), and defaults from QSAdapter . defaults_mapping propertyCommon argument names in quantstats mapped to ReturnsAccessor.defaults . distribution methodQSAdapter . distribution ( * , compounded = True , prepare_returns = True ) See quantstats.stats.distribution . expected_return methodQSAdapter . expected_return ( * , aggregate = None , compounded = True , prepare_returns = True ) See quantstats.stats.expected_return . expected_shortfall methodQSAdapter . expected_shortfall ( * , sigma = 1 , confidence = 0.95 ) See quantstats.stats.expected_shortfall . exponential_stdev methodQSAdapter . exponential_stdev ( * , window = 30 , is_halflife = False ) See quantstats.utils.exponential_stdev . exposure methodQSAdapter . exposure ( * , prepare_returns = True ) See quantstats.stats.exposure . full_report methodQSAdapter . full_report ( * , benchmark = None , rf = 0.0 , grayscale = False , figsize = ( 8 , 5 ), display = True , compounded = True , periods_per_year = 252 , match_dates = True , ** kwargs ) See quantstats.reports.full . gain_to_pain_ratio methodQSAdapter . gain_to_pain_ratio ( * , rf = 0 , resolution = 'D' ) See quantstats.stats.gain_to_pain_ratio . geometric_mean methodQSAdapter . geometric_mean ( * , aggregate = None , compounded = True ) See quantstats.stats.geometric_mean . ghpr methodQSAdapter . ghpr ( * , aggregate = None , compounded = True ) See quantstats.stats.ghpr . greeks methodQSAdapter . greeks ( * , benchmark , periods = 252.0 , prepare_returns = True ) See quantstats.stats.greeks . group_returns methodQSAdapter . group_returns ( * , groupby , compounded = False ) See quantstats.utils.group_returns . html_report methodQSAdapter . html_report ( * , benchmark = None , rf = 0.0 , grayscale = False , title = 'Strategy Tearsheet' , output = None , compounded = True , periods_per_year = 252 , download_filename = 'quantstats-tearsheet.html' , figfmt = 'svg' , template_path = None , match_dates = True , ** kwargs ) See quantstats.reports.html . implied_volatility methodQSAdapter . implied_volatility ( * , periods = 252 , annualize = True ) See quantstats.stats.implied_volatility . information_ratio methodQSAdapter . information_ratio ( * , benchmark , prepare_returns = True ) See quantstats.stats.information_ratio . kelly_criterion methodQSAdapter . kelly_criterion ( * , prepare_returns = True ) See quantstats.stats.kelly_criterion . kurtosis methodQSAdapter . kurtosis ( * , prepare_returns = True ) See quantstats.stats.kurtosis . log_returns methodQSAdapter . log_returns ( * , rf = 0.0 , nperiods = None ) See quantstats.utils.log_returns . make_index methodQSAdapter . make_index ( * , rebalance = '1M' , period = 'max' , returns = None , match_dates = False ) See quantstats.utils.make_index . make_portfolio methodQSAdapter . make_portfolio ( * , start_balance = 100000.0 , mode = 'comp' , round_to = None ) See quantstats.utils.make_portfolio . metrics_report methodQSAdapter . metrics_report ( * , benchmark = None , rf = 0.0 , display = True , mode = 'basic' , sep = False , compounded = True , periods_per_year = 252 , prepare_returns = True , match_dates = True , ** kwargs ) See quantstats.reports.metrics . monthly_returns methodQSAdapter . monthly_returns ( * , eoy = True , compounded = True , prepare_returns = True ) See quantstats.stats.monthly_returns . omega methodQSAdapter . omega ( * , rf = 0.0 , required_return = 0.0 , periods = 252 ) See quantstats.stats.omega . outlier_loss_ratio methodQSAdapter . outlier_loss_ratio ( * , quantile = 0.01 , prepare_returns = True ) See quantstats.stats.outlier_loss_ratio . outlier_win_ratio methodQSAdapter . outlier_win_ratio ( * , quantile = 0.99 , prepare_returns = True ) See quantstats.stats.outlier_win_ratio . outliers methodQSAdapter . outliers ( * , quantile = 0.95 ) See quantstats.stats.outliers . payoff_ratio methodQSAdapter . payoff_ratio ( * , prepare_returns = True ) See quantstats.stats.payoff_ratio . plot_daily_returns methodQSAdapter . plot_daily_returns ( * , benchmark , grayscale = False , figsize = ( 10 , 4 ), fontname = 'Arial' , lw = 0.5 , log_scale = False , ylabel = 'Returns' , subtitle = True , savefig = None , show = True , prepare_returns = True , active = False ) See quantstats.plots.daily_returns . plot_distribution methodQSAdapter . plot_distribution ( * , fontname = 'Arial' , grayscale = False , ylabel = True , figsize = ( 10 , 6 ), subtitle = True , compounded = True , savefig = None , show = True , title = None , prepare_returns = True ) See quantstats.plots.distribution . plot_drawdown methodQSAdapter . plot_drawdown ( * , grayscale = False , figsize = ( 10 , 5 ), fontname = 'Arial' , lw = 1 , log_scale = False , match_volatility = False , compound = False , ylabel = 'Drawdown' , resample = None , subtitle = True , savefig = None , show = True ) See quantstats.plots.drawdown . plot_drawdowns_periods methodQSAdapter . plot_drawdowns_periods ( * , periods = 5 , lw = 1.5 , log_scale = False , fontname = 'Arial' , grayscale = False , title = None , figsize = ( 10 , 5 ), ylabel = True , subtitle = True , compounded = True , savefig = None , show = True , prepare_returns = True ) See quantstats.plots.drawdowns_periods . plot_earnings methodQSAdapter . plot_earnings ( * , start_balance = 100000.0 , mode = 'comp' , grayscale = False , figsize = ( 10 , 6 ), title = 'Portfolio Earnings' , fontname = 'Arial' , lw = 1.5 , subtitle = True , savefig = None , show = True ) See quantstats.plots.earnings . plot_histogram methodQSAdapter . plot_histogram ( * , benchmark = None , resample = 'ME' , fontname = 'Arial' , grayscale = False , figsize = ( 10 , 5 ), ylabel = True , subtitle = True , compounded = True , savefig = None , show = True , prepare_returns = True ) See quantstats.plots.histogram . plot_log_returns methodQSAdapter . plot_log_returns ( * , benchmark = None , grayscale = False , figsize = ( 10 , 5 ), fontname = 'Arial' , lw = 1.5 , match_volatility = False , compound = True , cumulative = True , resample = None , ylabel = 'Cumulative Returns' , subtitle = True , savefig = None , show = True , prepare_returns = True ) See quantstats.plots.log_returns . plot_monthly_heatmap methodQSAdapter . plot_monthly_heatmap ( * , benchmark = None , annot_size = 13 , figsize = ( 8 , 5 ), cbar = True , square = False , returns_label = 'Strategy' , compounded = True , eoy = False , grayscale = False , fontname = 'Arial' , ylabel = True , savefig = None , show = True , active = False ) See quantstats.plots.monthly_heatmap . plot_monthly_returns methodQSAdapter . plot_monthly_returns ( * , annot_size = 9 , figsize = ( 10 , 5 ), cbar = True , square = False , compounded = True , eoy = False , grayscale = False , fontname = 'Arial' , ylabel = True , savefig = None , show = True ) See quantstats.plots.monthly_returns . plot_returns methodQSAdapter . plot_returns ( * , benchmark = None , grayscale = False , figsize = ( 10 , 6 ), fontname = 'Arial' , lw = 1.5 , match_volatility = False , compound = True , cumulative = True , resample = None , ylabel = 'Cumulative Returns' , subtitle = True , savefig = None , show = True , prepare_returns = True ) See quantstats.plots.returns . plot_rolling_beta methodQSAdapter . plot_rolling_beta ( * , benchmark , window1 = 126 , window1_label = '6-Months' , window2 = 252 , window2_label = '12-Months' , lw = 1.5 , fontname = 'Arial' , grayscale = False , figsize = ( 10 , 3 ), ylabel = True , subtitle = True , savefig = None , show = True , prepare_returns = True ) See quantstats.plots.rolling_beta . plot_rolling_sharpe methodQSAdapter . plot_rolling_sharpe ( * , benchmark = None , rf = 0.0 , period = 126 , period_label = '6-Months' , periods_per_year = 252 , lw = 1.25 , fontname = 'Arial' , grayscale = False , figsize = ( 10 , 3 ), ylabel = 'Sharpe' , subtitle = True , savefig = None , show = True ) See quantstats.plots.rolling_sharpe . plot_rolling_sortino methodQSAdapter . plot_rolling_sortino ( * , benchmark = None , rf = 0.0 , period = 126 , period_label = '6-Months' , periods_per_year = 252 , lw = 1.25 , fontname = 'Arial' , grayscale = False , figsize = ( 10 , 3 ), ylabel = 'Sortino' , subtitle = True , savefig = None , show = True ) See quantstats.plots.rolling_sortino . plot_rolling_volatility methodQSAdapter . plot_rolling_volatility ( * , benchmark = None , period = 126 , period_label = '6-Months' , periods_per_year = 252 , lw = 1.5 , fontname = 'Arial' , grayscale = False , figsize = ( 10 , 3 ), ylabel = 'Volatility' , subtitle = True , savefig = None , show = True ) See quantstats.plots.rolling_volatility . plot_snapshot methodQSAdapter . plot_snapshot ( * , grayscale = False , figsize = ( 10 , 8 ), title = 'Portfolio Summary' , fontname = 'Arial' , lw = 1.5 , mode = 'comp' , subtitle = True , savefig = None , show = True , log_scale = False , ** kwargs ) See quantstats.plots.snapshot . plot_yearly_returns methodQSAdapter . plot_yearly_returns ( * , benchmark = None , fontname = 'Arial' , grayscale = False , hlw = 1.5 , hlcolor = 'red' , hllabel = '' , match_volatility = False , log_scale = False , figsize = ( 10 , 5 ), ylabel = True , subtitle = True , compounded = True , savefig = None , show = True , prepare_returns = True ) See quantstats.plots.yearly_returns . plots_report methodQSAdapter . plots_report ( * , benchmark = None , grayscale = False , figsize = ( 8 , 5 ), mode = 'basic' , compounded = True , periods_per_year = 252 , prepare_returns = True , match_dates = True , ** kwargs ) See quantstats.reports.plots . profit_factor methodQSAdapter . profit_factor ( * , prepare_returns = True ) See quantstats.stats.profit_factor . profit_ratio methodQSAdapter . profit_ratio ( * , prepare_returns = True ) See quantstats.stats.profit_ratio . r2 methodQSAdapter . r2 ( * , benchmark ) See quantstats.stats.r2 . r_squared methodQSAdapter . r_squared ( * , benchmark , prepare_returns = True ) See quantstats.stats.r_squared . rar methodQSAdapter . rar ( * , rf = 0.0 ) See quantstats.stats.rar . recovery_factor methodQSAdapter . recovery_factor ( * , rf = 0.0 , prepare_returns = True ) See quantstats.stats.recovery_factor . remove_outliers methodQSAdapter . remove_outliers ( * , quantile = 0.95 ) See quantstats.stats.remove_outliers . returns_accessor propertyReturns accessor. risk_of_ruin methodQSAdapter . risk_of_ruin ( * , prepare_returns = True ) See quantstats.stats.risk_of_ruin . risk_return_ratio methodQSAdapter . risk_return_ratio ( * , prepare_returns = True ) See quantstats.stats.risk_return_ratio . rolling_greeks methodQSAdapter . rolling_greeks ( * , benchmark , periods = 252 , prepare_returns = True ) See quantstats.stats.rolling_greeks . rolling_sharpe methodQSAdapter . rolling_sharpe ( * , rf = 0.0 , rolling_period = 126 , annualize = True , periods_per_year = 252 , prepare_returns = True ) See quantstats.stats.rolling_sharpe . rolling_sortino methodQSAdapter . rolling_sortino ( * , rf = 0 , rolling_period = 126 , annualize = True , periods_per_year = 252 , ** kwargs ) See quantstats.stats.rolling_sortino . rolling_volatility methodQSAdapter . rolling_volatility ( * , rolling_period = 126 , periods_per_year = 252 , prepare_returns = True ) See quantstats.stats.rolling_volatility . ror methodQSAdapter . ror () See quantstats.stats.ror . serenity_index methodQSAdapter . serenity_index ( * , rf = 0 ) See quantstats.stats.serenity_index . sharpe methodQSAdapter . sharpe ( * , rf = 0.0 , periods = 252 , annualize = True , smart = False ) See quantstats.stats.sharpe . skew methodQSAdapter . skew ( * , prepare_returns = True ) See quantstats.stats.skew . smart_sharpe methodQSAdapter . smart_sharpe ( * , rf = 0.0 , periods = 252 , annualize = True ) See quantstats.stats.smart_sharpe . smart_sortino methodQSAdapter . smart_sortino ( * , rf = 0 , periods = 252 , annualize = True ) See quantstats.stats.smart_sortino . sortino methodQSAdapter . sortino ( * , rf = 0 , periods = 252 , annualize = True , smart = False ) See quantstats.stats.sortino . tail_ratio methodQSAdapter . tail_ratio ( * , cutoff = 0.95 , prepare_returns = True ) See quantstats.stats.tail_ratio . to_drawdown_series methodQSAdapter . to_drawdown_series () See quantstats.stats.to_drawdown_series . to_excess_returns methodQSAdapter . to_excess_returns ( * , rf , nperiods = None ) See quantstats.utils.to_excess_returns . to_log_returns methodQSAdapter . to_log_returns ( * , rf = 0.0 , nperiods = None ) See quantstats.utils.to_log_returns . to_prices methodQSAdapter . to_prices ( * , base = 100000.0 ) See quantstats.utils.to_prices . treynor_ratio methodQSAdapter . treynor_ratio ( * , benchmark , periods = 252.0 , rf = 0.0 ) See quantstats.stats.treynor_ratio . ulcer_index methodQSAdapter . ulcer_index () See quantstats.stats.ulcer_index . ulcer_performance_index methodQSAdapter . ulcer_performance_index ( * , rf = 0 ) See quantstats.stats.ulcer_performance_index . upi methodQSAdapter . upi ( * , rf = 0 ) See quantstats.stats.upi . value_at_risk methodQSAdapter . value_at_risk ( * , sigma = 1 , confidence = 0.95 , prepare_returns = True ) See quantstats.stats.value_at_risk . var methodQSAdapter . var ( * , sigma = 1 , confidence = 0.95 , prepare_returns = True ) See quantstats.stats.var . volatility methodQSAdapter . volatility ( * , periods = 252 , annualize = True , prepare_returns = True ) See quantstats.stats.volatility . win_loss_ratio methodQSAdapter . win_loss_ratio ( * , prepare_returns = True ) See quantstats.stats.win_loss_ratio . win_rate methodQSAdapter . win_rate ( * , aggregate = None , compounded = True , prepare_returns = True ) See quantstats.stats.win_rate . worst methodQSAdapter . worst ( * , aggregate = None , compounded = True , prepare_returns = True ) See quantstats.stats.worst .