image_ moduleUtilities for images. hstack_image_arrays functionhstack_image_arrays ( a , b ) Stack NumPy images horizontally. save_animation functionsave_animation ( fname , index , plot_func , * args , delta = None , step = 1 , fps = 3 , writer_kwargs = None , show_progress = True , tqdm_kwargs = None , to_image_kwargs = None , ** kwargs ) Save animation to a file. Args fname : str File name. index : iterable Index to iterate over. plot_func : callable Plotting function. Should take subset of index , *args , and **kwargs , and return either a Plotly figure, image that can be read by imageio.imread , or a NumPy array. *args Positional arguments passed to plot_func . delta : int Window size of each iteration. step : int Step of each iteration. fps : int Frames per second. Will be translated to duration by 1000 / fps . writer_kwargs : dict Keyword arguments passed to imageio.get_writer . show_progress : bool Whether to show the progress bar. tqdm_kwargs : dict Keyword arguments passed to tqdm . to_image_kwargs : dict Keyword arguments passed to plotly.graph_objects.Figure.to_image . **kwargs Keyword arguments passed to plot_func . vstack_image_arrays functionvstack_image_arrays ( a , b ) Stack NumPy images vertically.