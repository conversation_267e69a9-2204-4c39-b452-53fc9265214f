{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# returns"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import vectorbt as vbt"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"Collapsed": "false"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.7/site-packages/pandas_datareader/compat/__init__.py:7: FutureWarning: pandas.util.testing is deprecated. Use the functions in the public API at pandas.testing instead.\n", "  from pandas.util.testing import assert_frame_equal\n", "/Users/<USER>/miniconda3/lib/python3.7/site-packages/empyrical/utils.py:32: UserWarning: Unable to import pandas_datareader. Suppressing import error and continuing. All data reading functionality will raise errors; but has been deprecated and will be removed in a later version.\n", "  warnings.warn(msg)\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "from numba import njit\n", "import empyrical"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Disable caching for performance testing\n", "vbt.settings.caching['enabled'] = False\n", "vbt.settings.returns['year_freq'] = '252 days' # same as empyrical"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              a    b    c\n", "2018-01-01  1.0  5.0  1.0\n", "2018-01-02  2.0  4.0  2.0\n", "2018-01-03  3.0  3.0  3.0\n", "2018-01-04  4.0  2.0  2.0\n", "2018-01-05  5.0  1.0  1.0\n"]}], "source": ["index = pd.DatetimeIndex([\n", "    datetime(2018, 1, 1),\n", "    datetime(2018, 1, 2),\n", "    datetime(2018, 1, 3),\n", "    datetime(2018, 1, 4),\n", "    datetime(2018, 1, 5)\n", "], freq='D')\n", "columns = ['a', 'b', 'c']\n", "ts = pd.DataFrame({\n", "    'a': [1, 2, 3, 4, 5], \n", "    'b': [5, 4, 3, 2, 1],\n", "    'c': [1, 2, 3, 2, 1]\n", "}, index=index).astype(np.float32)\n", "\n", "print(ts)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1000, 1000)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["big_ts = pd.DataFrame(np.random.randint(1, 10, size=(1000, 1000)).astype(float))\n", "big_ts.index = [datetime(2018, 1, 1) + timedelta(days=i) for i in range(1000)]\n", "big_ts.shape"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                   a         b         c\n", "2018-01-01       NaN       NaN       NaN\n", "2018-01-02  1.000000 -0.200000  1.000000\n", "2018-01-03  0.500000 -0.250000  0.500000\n", "2018-01-04  0.333333 -0.333333 -0.333333\n", "2018-01-05  0.250000 -0.500000 -0.500000\n"]}], "source": ["returns = ts.vbt.pct_change()\n", "print(returns)\n", "\n", "big_returns = big_ts.vbt.pct_change()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["np.random.seed(42)\n", "benchmark_rets = returns['a'] * np.random.uniform(0.8, 1.2, returns.shape[0])\n", "big_benchmark_rets = big_returns[0] * np.random.uniform(0.8, 1.2, big_returns.shape[0])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["252 days 00:00:00\n", "252 days 00:00:00\n", "252 days 00:00:00\n", "252 days 00:00:00\n"]}], "source": ["# Test year frequency\n", "print(returns.vbt.returns.year_freq)\n", "print(returns['a'].vbt.returns.year_freq)\n", "print(returns.vbt.returns(year_freq='252 days').year_freq)\n", "print(returns['a'].vbt.returns(year_freq='252 days').year_freq)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["252.0\n", "252.0\n"]}], "source": ["print(returns.vbt.returns.ann_factor) # default\n", "print(returns.vbt.returns(year_freq='252 days').ann_factor)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01         NaN\n", "2018-01-02    1.000000\n", "2018-01-03    0.500000\n", "2018-01-04    0.333333\n", "2018-01-05    0.250000\n", "Freq: D, Name: a, dtype: float64\n", "                   a         b         c\n", "2018-01-01       NaN       NaN       NaN\n", "2018-01-02  1.000000 -0.200000  1.000000\n", "2018-01-03  0.500000 -0.250000  0.500000\n", "2018-01-04  0.333333 -0.333333 -0.333333\n", "2018-01-05  0.250000 -0.500000 -0.500000\n", "The slowest run took 24.92 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "56 µs ± 106 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(returns['a'].vbt.returns.daily()) # already daily, do nothing\n", "print(returns.vbt.returns.daily())\n", "\n", "%timeit big_returns.vbt.returns.daily()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01    4.0\n", "Freq: 252D, Name: a, dtype: float64\n", "              a    b             c\n", "2018-01-01  4.0 -0.8  2.980232e-08\n", "5.96 ms ± 69.6 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(returns['a'].vbt.returns.annual())\n", "print(returns.vbt.returns.annual())\n", "\n", "%timeit big_returns.vbt.returns.annual()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2018-01-01    0.0\n", "2018-01-02    1.0\n", "2018-01-03    2.0\n", "2018-01-04    3.0\n", "2018-01-05    4.0\n", "Freq: D, dtype: float64\n", "963 µs ± 24.7 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "2018-01-01    0.0\n", "2018-01-02    1.0\n", "2018-01-03    2.0\n", "2018-01-04    3.0\n", "2018-01-05    4.0\n", "Freq: D, Name: a, dtype: float64\n", "The slowest run took 10.08 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "161 µs ± 220 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "              a    b             c\n", "2018-01-01  0.0  0.0  0.000000e+00\n", "2018-01-02  1.0 -0.2  1.000000e+00\n", "2018-01-03  2.0 -0.4  2.000000e+00\n", "2018-01-04  3.0 -0.6  1.000000e+00\n", "2018-01-05  4.0 -0.8  2.980232e-08\n", "              a    b    c\n", "2018-01-01  1.0  1.0  1.0\n", "2018-01-02  2.0  0.8  2.0\n", "2018-01-03  3.0  0.6  3.0\n", "2018-01-04  4.0  0.4  2.0\n", "2018-01-05  5.0  0.2  1.0\n", "4.31 ms ± 308 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.cum_returns(returns['a']))\n", "%timeit empyrical.cum_returns(big_returns[0])\n", "\n", "print(returns['a'].vbt.returns.cumulative())\n", "%timeit big_returns[0].vbt.returns.cumulative()\n", "\n", "print(returns.vbt.returns.cumulative())\n", "print(returns.vbt.returns.cumulative(start_value=1))\n", "%timeit big_returns.vbt.returns.cumulative()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.000000149011612\n", "84.9 µs ± 10.8 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "4.000000149011612\n", "The slowest run took 7.79 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "124 µs ± 145 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a    4.000000e+00\n", "b   -8.000000e-01\n", "c    2.980232e-08\n", "Name: total_return, dtype: float64\n", "3.31 ms ± 74.7 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "              a     b    c\n", "2018-01-01  NaN   NaN  NaN\n", "2018-01-02  1.0 -0.20  1.0\n", "2018-01-03  2.0 -0.40  2.0\n", "2018-01-04  3.0 -0.60  1.0\n", "2018-01-05  1.5 -0.75 -0.5\n", "117 ms ± 9.41 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.cum_returns_final(returns['a']))\n", "%timeit empyrical.cum_returns_final(big_returns[0])\n", "\n", "print(returns['a'].vbt.returns.total())\n", "%timeit big_returns[0].vbt.returns.total()\n", "\n", "print(returns.vbt.returns.total())\n", "%timeit big_returns.vbt.returns.total()\n", "\n", "print(returns.vbt.returns.rolling_total(3, minp=1))\n", "%timeit big_returns.vbt.returns.rolling_total(3, minp=1)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.690786886567203e+35\n", "88.2 µs ± 19.2 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "1.6907868865671834e+35\n", "The slowest run took 5.69 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "160 µs ± 152 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a    1.690787e+35\n", "b   -1.000000e+00\n", "c    1.502038e-06\n", "Name: annualized_return, dtype: float64\n", "3.66 ms ± 122 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "                       a    b             c\n", "2018-01-01           NaN  NaN           NaN\n", "2018-01-02  8.507059e+37 -1.0  8.507059e+37\n", "2018-01-03  1.197252e+40 -1.0  1.197252e+40\n", "2018-01-04  3.741454e+50 -1.0  1.934286e+25\n", "2018-01-05  2.672771e+33 -1.0 -1.000000e+00\n", "145 ms ± 10.7 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.annual_return(returns['a']))\n", "%timeit empyrical.annual_return(big_returns[0])\n", "\n", "print(returns['a'].vbt.returns.annualized())\n", "%timeit big_returns[0].vbt.returns.annualized()\n", "\n", "print(returns.vbt.returns.annualized())\n", "%timeit big_returns.vbt.returns.annualized()\n", "\n", "print(returns.vbt.returns.rolling_annualized(3, minp=1))\n", "%timeit big_returns.vbt.returns.rolling_annualized(3, minp=1)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.121838249438074\n", "24.1 µs ± 10.1 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "2.121838249438074\n", "The slowest run took 5.02 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "172 µs ± 149 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a    2.121838\n", "b    0.830587\n", "c    4.466341\n", "Name: annualized_volatility, dtype: float64\n", "5.78 ms ± 82.4 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "                   a         b         c\n", "2018-01-01       NaN       NaN       NaN\n", "2018-01-02       NaN       NaN       NaN\n", "2018-01-03  2.233170  0.223317  2.233170\n", "2018-01-04  2.191425  0.425454  4.254544\n", "2018-01-05  0.804033  0.804033  3.384043\n", "262 ms ± 13.6 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.annual_volatility(returns['a'], alpha=3.))\n", "%timeit empyrical.annual_volatility(big_returns[0], alpha=3.)\n", "\n", "print(returns['a'].vbt.returns.annualized_volatility(levy_alpha=3.))\n", "%timeit big_returns[0].vbt.returns.annualized_volatility(levy_alpha=3.)\n", "\n", "print(returns.vbt.returns.annualized_volatility(levy_alpha=3.))\n", "%timeit big_returns.vbt.returns.annualized_volatility(levy_alpha=3.)\n", "\n", "print(returns.vbt.returns.rolling_annualized_volatility(3, minp=1, levy_alpha=3.))\n", "%timeit big_returns.vbt.returns.rolling_annualized_volatility(3, minp=1, levy_alpha=3.)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-1.2500000139698388\n", "143 µs ± 36.8 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "-1.2500000139698388\n", "The slowest run took 138.16 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "2.12 ms ± 4.94 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a         NaN\n", "b   -1.250000\n", "c    0.000002\n", "Name: calmar_ratio, dtype: float64\n", "11.5 ms ± 61.6 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "             a    b             c\n", "2018-01-01 NaN  NaN           NaN\n", "2018-01-02 NaN -5.0           NaN\n", "2018-01-03 NaN -2.5           NaN\n", "2018-01-04 NaN -2.0  5.802859e+25\n", "2018-01-05 NaN -1.5 -1.500000e+00\n", "616 ms ± 13.9 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.calmar_ratio(returns['b']))\n", "%timeit empyrical.calmar_ratio(big_returns[0])\n", "\n", "print(returns['b'].vbt.returns.calmar_ratio())\n", "%timeit big_returns[0].vbt.returns.calmar_ratio()\n", "\n", "print(returns.vbt.returns.calmar_ratio())\n", "%timeit big_returns.vbt.returns.calmar_ratio()\n", "\n", "print(returns.vbt.returns.rolling_calmar_ratio(3, minp=1))\n", "%timeit big_returns.vbt.returns.rolling_calmar_ratio(3, minp=1)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.7319528661672228\n", "414 µs ± 31.5 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "1.7319528661672228\n", "The slowest run took 136.51 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "1.99 ms ± 4.63 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a         inf\n", "b    0.000000\n", "c    1.731953\n", "Name: omega_ratio, dtype: float64\n", "8.65 ms ± 66.1 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "              a    b         c\n", "2018-01-01  NaN  NaN       NaN\n", "2018-01-02  inf  0.0       inf\n", "2018-01-03  inf  0.0       inf\n", "2018-01-04  inf  0.0  4.303734\n", "2018-01-05  inf  0.0  0.573267\n", "652 ms ± 13.7 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.omega_ratio(returns['c'], risk_free=0.01, required_return=0.1))\n", "%timeit empyrical.omega_ratio(big_returns[0], risk_free=0.01, required_return=0.1)\n", "\n", "print(returns['c'].vbt.returns.omega_ratio(risk_free=0.01, required_return=0.1))\n", "%timeit big_returns[0].vbt.returns.omega_ratio(risk_free=0.01, required_return=0.1)\n", "\n", "print(returns.vbt.returns.omega_ratio(risk_free=0.01, required_return=0.1))\n", "%timeit big_returns.vbt.returns.omega_ratio(risk_free=0.01, required_return=0.1)\n", "\n", "print(returns.vbt.returns.rolling_omega_ratio(3, minp=1, risk_free=0.01, required_return=0.1))\n", "%timeit big_returns.vbt.returns.rolling_omega_ratio(3, minp=1, risk_free=0.01, required_return=0.1)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["24.139822936194918\n", "80.5 µs ± 19.4 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "24.139822936194918\n", "The slowest run took 124.12 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "2 ms ± 4.62 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a    24.139823\n", "b   -39.938439\n", "c     3.517158\n", "Name: sharpe_ratio, dtype: float64\n", "8.53 ms ± 70.5 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "                    a           b          c\n", "2018-01-01        NaN         NaN        NaN\n", "2018-01-02        NaN         NaN        NaN\n", "2018-01-03  33.225918 -105.514710  33.225918\n", "2018-01-04  27.503962  -63.894201   8.929476\n", "2018-01-05  43.786248  -46.280396  -3.588519\n", "387 ms ± 14.5 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.sharpe_ratio(returns['a'], risk_free=0.01))\n", "%timeit empyrical.sharpe_ratio(big_returns[0], risk_free=0.01)\n", "\n", "print(returns['a'].vbt.returns.sharpe_ratio(risk_free=0.01))\n", "%timeit big_returns[0].vbt.returns.sharpe_ratio(risk_free=0.01)\n", "\n", "print(returns.vbt.returns.sharpe_ratio(risk_free=0.01))\n", "%timeit big_returns.vbt.returns.sharpe_ratio(risk_free=0.01)\n", "\n", "print(returns.vbt.returns.rolling_sharpe_ratio(3, minp=1, risk_free=0.01))\n", "%timeit big_returns.vbt.returns.rolling_sharpe_ratio(3, minp=1, risk_free=0.01)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a         NaN\n", "b         <PERSON><PERSON>\n", "c    0.000536\n", "Name: deflated_sharpe_ratio, dtype: float64\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Documents/SourceTree/vectorbt/vectorbt/returns/metrics.py:23: RuntimeWarning: invalid value encountered in sqrt\n", "  np.sqrt(1 - skew * est_sharpe + ((kurtosis - 1) / 4) * est_sharpe ** 2))\n", "/Users/<USER>/miniconda3/lib/python3.7/site-packages/scipy/stats/_distn_infrastructure.py:1922: RuntimeWarning: invalid value encountered in greater_equal\n", "  cond2 = (x >= np.asarray(_b)) & cond0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["19 ms ± 751 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "1.0\n"]}], "source": ["print(returns.vbt.returns.deflated_sharpe_ratio(risk_free=0.01))\n", "%timeit big_returns.vbt.returns.deflated_sharpe_ratio(risk_free=0.01)\n", "\n", "# can specify var_sharpe and nb_trials expclicitly\n", "print(big_returns[0].vbt.returns.deflated_sharpe_ratio(\n", "    risk_free=0.01, \n", "    var_sharpe=np.var(big_returns.vbt.returns.sharpe_ratio(risk_free=0.01)),\n", "    nb_trials=big_returns.shape[1]\n", "))"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6.920801865722236\n", "44.2 µs ± 23.6 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "6.920801865722236\n", "The slowest run took 115.52 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "1.69 ms ± 3.89 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a    0.000000\n", "b    6.920802\n", "c    5.874521\n", "Name: downside_risk, dtype: float64\n", "6.09 ms ± 59.7 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "              a         b         c\n", "2018-01-01  NaN       NaN       NaN\n", "2018-01-02  0.0  4.762352  0.000000\n", "2018-01-03  0.0  5.174456  0.000000\n", "2018-01-04  0.0  5.798563  3.971565\n", "2018-01-05  0.0  7.503555  6.783313\n", "377 ms ± 13.7 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.downside_risk(returns['b'], required_return=0.1))\n", "%timeit empyrical.downside_risk(big_returns[0], required_return=0.1)\n", "\n", "print(returns['b'].vbt.returns.downside_risk(required_return=0.1))\n", "%timeit big_returns[0].vbt.returns.downside_risk(required_return=0.1)\n", "\n", "print(returns.vbt.returns.downside_risk(required_return=0.1))\n", "%timeit big_returns.vbt.returns.downside_risk(required_return=0.1)\n", "\n", "print(returns.vbt.returns.rolling_downside_risk(3, minp=1, required_return=0.1))\n", "%timeit big_returns.vbt.returns.rolling_downside_risk(3, minp=1, required_return=0.1)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-15.32336860018125\n", "113 µs ± 33.5 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "-15.32336860018125\n", "The slowest run took 123.76 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "1.85 ms ± 4.29 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a          inf\n", "b   -15.323369\n", "c     2.859808\n", "Name: sortino_ratio, dtype: float64\n", "8.87 ms ± 72.6 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "              a          b          c\n", "2018-01-01  NaN        NaN        NaN\n", "2018-01-02  inf -15.874508        inf\n", "2018-01-03  inf -15.827749        inf\n", "2018-01-04  inf -15.693543  18.330304\n", "2018-01-05  inf -15.485994  -7.842775\n", "491 ms ± 13.3 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.sortino_ratio(returns['b'], required_return=0.1))\n", "%timeit empyrical.sortino_ratio(big_returns[0], required_return=0.1)\n", "\n", "print(returns['b'].vbt.returns.sortino_ratio(required_return=0.1))\n", "%timeit big_returns[0].vbt.returns.sortino_ratio(required_return=0.1)\n", "\n", "print(returns.vbt.returns.sortino_ratio(required_return=0.1))\n", "%timeit big_returns.vbt.returns.sortino_ratio(required_return=0.1)\n", "\n", "print(returns.vbt.returns.rolling_sortino_ratio(3, minp=1, required_return=0.1))\n", "%timeit big_returns.vbt.returns.rolling_sortino_ratio(3, minp=1, required_return=0.1)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-0.5575108270794908\n", "432 µs ± 73.3 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "-0.5575108270794908\n", "144 µs ± 12.5 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a   -0.557511\n", "b   -2.718676\n", "c   -1.185416\n", "Name: information_ratio, dtype: float64\n", "8.39 ms ± 224 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "                   a          b         c\n", "2018-01-01       NaN        NaN       NaN\n", "2018-01-02       NaN        NaN       NaN\n", "2018-01-03 -1.197205  -2.636038 -1.197205\n", "2018-01-04 -0.903634  -2.536990 -0.905136\n", "2018-01-05 -0.206482 -12.233914 -1.276646\n", "386 ms ± 15.2 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.excess_sharpe(returns['a'], benchmark_rets))\n", "%timeit empyrical.excess_sharpe(big_returns[0], benchmark_rets)\n", "\n", "print(returns['a'].vbt.returns.information_ratio(benchmark_rets)) # will broadcast\n", "%timeit big_returns[0].vbt.returns.information_ratio(big_benchmark_rets)\n", "\n", "print(returns.vbt.returns.information_ratio(benchmark_rets))\n", "%timeit big_returns.vbt.returns.information_ratio(big_benchmark_rets)\n", "\n", "print(returns.vbt.returns.rolling_information_ratio(benchmark_rets, 3, minp=1))\n", "%timeit big_returns.vbt.returns.rolling_information_ratio(big_benchmark_rets, 3, minp=1)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.7853755820643185\n", "1.17 ms ± 451 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "0.7853755820643185\n", "107 µs ± 6.25 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a    0.785376\n", "b    0.252235\n", "c    1.547239\n", "Name: beta, dtype: float64\n", "10 ms ± 253 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "                   a         b         c\n", "2018-01-01       NaN       NaN       NaN\n", "2018-01-02       NaN       NaN       NaN\n", "2018-01-03  0.788784  0.078878  0.788784\n", "2018-01-04  0.796948  0.140305  1.403054\n", "2018-01-05  0.762210  0.727668  3.117926\n", "592 ms ± 13.1 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.beta(returns['a'], benchmark_rets))\n", "%timeit empyrical.beta(big_returns[0], benchmark_rets)\n", "\n", "print(returns['a'].vbt.returns.beta(benchmark_rets))\n", "%timeit big_returns[0].vbt.returns.beta(big_benchmark_rets)\n", "\n", "print(returns.vbt.returns.beta(benchmark_rets))\n", "%timeit big_returns.vbt.returns.beta(big_benchmark_rets)\n", "\n", "print(returns.vbt.returns.rolling_beta(benchmark_rets, 3, minp=1))\n", "%timeit big_returns.vbt.returns.rolling_beta(big_benchmark_rets, 3, minp=1)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["21533608.259557564\n", "1.08 ms ± 185 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "21533608.259557564\n", "The slowest run took 126.75 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "2.62 ms ± 6.07 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a    2.153361e+07\n", "b   -1.000000e+00\n", "c   -1.000000e+00\n", "Name: alpha, dtype: float64\n", "16.3 ms ± 5.61 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "                       a    b             c\n", "2018-01-01           NaN  NaN           NaN\n", "2018-01-02           NaN  NaN           NaN\n", "2018-01-03  1.221461e+07 -1.0  1.221461e+07\n", "2018-01-04  1.606601e+06 -1.0 -1.000000e+00\n", "2018-01-05  1.344194e+08 -1.0 -1.000000e+00\n", "1.01 s ± 17.8 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.alpha(returns['a'], benchmark_rets, risk_free=0.01))\n", "%timeit empyrical.alpha(big_returns[0], benchmark_rets, risk_free=0.01)\n", "\n", "print(returns['a'].vbt.returns.alpha(benchmark_rets, risk_free=0.01))\n", "%timeit big_returns[0].vbt.returns.alpha(big_benchmark_rets, risk_free=0.01)\n", "\n", "print(returns.vbt.returns.alpha(benchmark_rets, risk_free=0.01))\n", "%timeit big_returns.vbt.returns.alpha(big_benchmark_rets, risk_free=0.01)\n", "\n", "print(returns.vbt.returns.rolling_alpha(benchmark_rets, 3, minp=1, risk_free=0.01))\n", "%timeit big_returns.vbt.returns.rolling_alpha(big_benchmark_rets, 3, minp=1, risk_free=0.01)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.5238094437960337\n", "137 µs ± 18.6 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "3.5238094437960337\n", "The slowest run took 481.62 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "4.87 ms ± 11.7 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a    3.523809\n", "b    0.436842\n", "c    1.947368\n", "Name: tail_ratio, dtype: float64\n", "23.7 ms ± 90.3 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "                   a         b         c\n", "2018-01-01       NaN       NaN       NaN\n", "2018-01-02  1.000000  1.000000  1.000000\n", "2018-01-03  1.857143  0.818182  1.857143\n", "2018-01-04  2.714285  0.630769  3.800000\n", "2018-01-05  1.870968  0.534483  0.862069\n", "2.44 s ± 64.9 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.tail_ratio(returns['a']))\n", "%timeit empyrical.tail_ratio(big_returns[0])\n", "\n", "print(returns['a'].vbt.returns.tail_ratio())\n", "%timeit big_returns[0].vbt.returns.tail_ratio()\n", "\n", "print(returns.vbt.returns.tail_ratio())\n", "%timeit big_returns.vbt.returns.tail_ratio()\n", "\n", "print(returns.vbt.returns.rolling_tail_ratio(3, minp=1))\n", "%timeit big_returns.vbt.returns.rolling_tail_ratio(3, minp=1)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.26250000596046447\n", "81.6 µs ± 32.3 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "0.26250000596046447\n", "The slowest run took 503.96 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "4.88 ms ± 11.8 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a    0.2625\n", "b   -0.4750\n", "c   -0.4750\n", "Name: value_at_risk, dtype: float64\n", "13.6 ms ± 196 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "                   a         b         c\n", "2018-01-01       NaN       NaN       NaN\n", "2018-01-02  1.000000 -0.200000  1.000000\n", "2018-01-03  0.525000 -0.247500  0.525000\n", "2018-01-04  0.350000 -0.325000 -0.250000\n", "2018-01-05  0.258333 -0.483333 -0.483333\n", "1.29 s ± 36.3 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.value_at_risk(returns.iloc[1:]['a'], cutoff=0.05))\n", "%timeit empyrical.value_at_risk(big_returns[0], cutoff=0.05)\n", "\n", "print(returns['a'].vbt.returns.value_at_risk(cutoff=0.05))\n", "%timeit big_returns[0].vbt.returns.value_at_risk(cutoff=0.05)\n", "\n", "print(returns.vbt.returns.value_at_risk(cutoff=0.05))\n", "%timeit big_returns.vbt.returns.value_at_risk(cutoff=0.05)\n", "\n", "print(returns.vbt.returns.rolling_value_at_risk(3, minp=1, cutoff=0.05))\n", "%timeit big_returns.vbt.returns.rolling_value_at_risk(3, minp=1, cutoff=0.05)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.25\n", "The slowest run took 4.48 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "41.2 µs ± 27.7 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "0.25\n", "The slowest run took 433.89 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "4.25 ms ± 10.2 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a    0.25\n", "b   -0.50\n", "c   -0.50\n", "Name: cond_value_at_risk, dtype: float64\n", "12.4 ms ± 117 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "                   a         b         c\n", "2018-01-01       NaN       NaN       NaN\n", "2018-01-02  1.000000 -0.200000  1.000000\n", "2018-01-03  0.500000 -0.250000  0.500000\n", "2018-01-04  0.333333 -0.333333 -0.333333\n", "2018-01-05  0.250000 -0.500000 -0.500000\n", "1.97 s ± 39.4 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.conditional_value_at_risk(returns.iloc[1:]['a'], cutoff=0.05))\n", "%timeit empyrical.conditional_value_at_risk(big_returns[0], cutoff=0.05)\n", "\n", "print(returns['a'].vbt.returns.cond_value_at_risk(cutoff=0.05))\n", "%timeit big_returns[0].vbt.returns.cond_value_at_risk(cutoff=0.05)\n", "\n", "print(returns.vbt.returns.cond_value_at_risk(cutoff=0.05))\n", "%timeit big_returns.vbt.returns.cond_value_at_risk(cutoff=0.05)\n", "\n", "print(returns.vbt.returns.rolling_cond_value_at_risk(3, minp=1, cutoff=0.05))\n", "%timeit big_returns.vbt.returns.rolling_cond_value_at_risk(3, minp=1, cutoff=0.05)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.00691706997447195\n", "188 µs ± 118 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "0.006917069974471952\n", "The slowest run took 99.74 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "2.06 ms ± 4.72 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.7/site-packages/empyrical/stats.py:447: RuntimeWarning: invalid value encountered in double_scalars\n", "  return ending_value ** (1 / num_years) - 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["a    6.917070e-03\n", "b   -4.091036e-38\n", "c    6.144892e-44\n", "Name: capture, dtype: float64\n", "8.52 ms ± 3.92 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "                   a             b             c\n", "2018-01-01       NaN           NaN           NaN\n", "2018-01-02  0.000019 -2.224899e-43  1.892734e-05\n", "2018-01-03  0.000055 -4.590579e-45  5.496077e-05\n", "2018-01-04  0.000024 -6.439659e-56  1.245614e-30\n", "2018-01-05  0.353571 -1.322864e-34 -1.322864e-34\n", "297 ms ± 12.9 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.capture(returns['a'], benchmark_rets))\n", "%timeit empyrical.capture(big_returns[0], big_benchmark_rets)\n", "\n", "print(returns['a'].vbt.returns.capture(benchmark_rets))\n", "%timeit big_returns[0].vbt.returns.capture(big_benchmark_rets)\n", "\n", "print(returns.vbt.returns.capture(benchmark_rets))\n", "%timeit big_returns.vbt.returns.capture(big_benchmark_rets)\n", "\n", "print(returns.vbt.returns.rolling_capture(benchmark_rets, 3, minp=1))\n", "%timeit big_returns.vbt.returns.rolling_capture(big_benchmark_rets, 3, minp=1)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0019948153957327634\n", "428 µs ± 55.2 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "0.0019948153957327634\n", "The slowest run took 108.89 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "2.18 ms ± 5 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a    1.994815e-03\n", "b   -1.839889e-47\n", "c    3.454480e-53\n", "Name: up_capture, dtype: float64\n", "7.84 ms ± 4.83 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "                       a             b             c\n", "2018-01-01           NaN           NaN           NaN\n", "2018-01-02  3.582444e-10 -4.950174e-86  3.582444e-10\n", "2018-01-03  4.074546e-07 -3.110292e-67  4.074546e-07\n", "2018-01-04  2.409368e-05 -6.439659e-56  1.245614e-30\n", "2018-01-05  3.535714e-01 -1.322864e-34 -1.322864e-34\n", "844 ms ± 15.5 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.up_capture(returns['a'], benchmark_rets))\n", "%timeit empyrical.up_capture(big_returns[0], big_benchmark_rets)\n", "\n", "print(returns['a'].vbt.returns.up_capture(benchmark_rets))\n", "%timeit big_returns[0].vbt.returns.up_capture(big_benchmark_rets)\n", "\n", "print(returns.vbt.returns.up_capture(benchmark_rets))\n", "%timeit big_returns.vbt.returns.up_capture(big_benchmark_rets)\n", "\n", "print(returns.vbt.returns.rolling_up_capture(benchmark_rets, 3, minp=1))\n", "%timeit big_returns.vbt.returns.rolling_up_capture(big_benchmark_rets, 3, minp=1)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["nan\n", "nan\n", "The slowest run took 114.37 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "2.29 ms ± 5.28 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a   NaN\n", "b   <PERSON><PERSON>\n", "c   NaN\n", "Name: down_capture, dtype: float64\n", "7.7 ms ± 4.79 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "             a   b   c\n", "2018-01-01 NaN NaN NaN\n", "2018-01-02 NaN NaN NaN\n", "2018-01-03 NaN NaN NaN\n", "2018-01-04 NaN NaN NaN\n", "2018-01-05 NaN NaN NaN\n", "844 ms ± 19.2 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.down_capture(returns['a'], benchmark_rets))\n", "#%timeit empyrical.down_capture(big_returns[0], big_benchmark_rets)\n", "\n", "print(returns['a'].vbt.returns.down_capture(benchmark_rets))\n", "%timeit big_returns[0].vbt.returns.down_capture(big_benchmark_rets)\n", "\n", "print(returns.vbt.returns.down_capture(benchmark_rets))\n", "%timeit big_returns.vbt.returns.down_capture(big_benchmark_rets)\n", "\n", "print(returns.vbt.returns.rolling_down_capture(benchmark_rets, 3, minp=1))\n", "%timeit big_returns.vbt.returns.rolling_down_capture(big_benchmark_rets, 3, minp=1)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["              a    b         c\n", "2018-01-01  0.0  0.0  0.000000\n", "2018-01-02  0.0 -0.2  0.000000\n", "2018-01-03  0.0 -0.4  0.000000\n", "2018-01-04  0.0 -0.6 -0.333333\n", "2018-01-05  0.0 -0.8 -0.666667\n", "8.76 ms ± 831 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(returns.vbt.returns.drawdown())\n", "\n", "%timeit big_returns.vbt.returns.drawdown()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-0.7999999910593033\n", "The slowest run took 4.07 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "87.9 µs ± 62.1 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "-0.7999999910593032\n", "The slowest run took 210.20 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "2.01 ms ± 4.76 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a    0.000000\n", "b   -0.800000\n", "c   -0.666667\n", "Name: max_drawdown, dtype: float64\n", "8.07 ms ± 124 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "              a         b         c\n", "2018-01-01  NaN       NaN       NaN\n", "2018-01-02  0.0 -0.200000  0.000000\n", "2018-01-03  0.0 -0.400000  0.000000\n", "2018-01-04  0.0 -0.500000 -0.333333\n", "2018-01-05  0.0 -0.666667 -0.666667\n", "490 ms ± 14 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(empyrical.max_drawdown(returns['b']))\n", "%timeit empyrical.max_drawdown(big_returns[0])\n", "\n", "print(returns['b'].vbt.returns.max_drawdown())\n", "%timeit big_returns[0].vbt.returns.max_drawdown()\n", "\n", "print(returns.vbt.returns.max_drawdown())\n", "%timeit big_returns.vbt.returns.max_drawdown()\n", "\n", "print(returns.vbt.returns.rolling_max_drawdown(3, minp=1))\n", "%timeit big_returns.vbt.returns.rolling_max_drawdown(3, minp=1)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Drawdowns(**Config({\n", "    \"wrapper\": \"<vectorbt.base.array_wrapper.ArrayWrapper object at 0x7fb4a0a65ef0> of shape (5, 3)\",\n", "    \"records_arr\": \"<numpy.ndarray object at 0x7fb4865b94e0> of shape (2,)\",\n", "    \"idx_field\": \"end_idx\",\n", "    \"col_mapper\": null,\n", "    \"ts\": \"<pandas.core.frame.DataFrame object at 0x7fb486d7a438> of shape (5, 3)\"\n", "}))\n", "11.3 ms ± 620 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "a         NaN\n", "b   -0.800000\n", "c   -0.666667\n", "Name: max_drawdown, dtype: float64\n"]}], "source": ["print(returns.vbt.returns.drawdowns)\n", "%timeit big_returns.vbt.returns.drawdowns\n", "\n", "print(returns.vbt.returns.drawdowns.max_drawdown())"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Start                        2018-01-01 00:00:00\n", "End                          2018-01-05 00:00:00\n", "Period                           5 days 00:00:00\n", "Total Return [%]                      -79.999999\n", "Benchmark Return [%]                   451.85973\n", "Annualized Return [%]                     -100.0\n", "Annualized Volatility [%]               83.05873\n", "Max Drawdown [%]                       79.999999\n", "Max Drawdown Duration            4 days 00:00:00\n", "Sharpe Ratio                          -39.938439\n", "Calmar Ratio                               -1.25\n", "Omega Ratio                                  0.0\n", "<PERSON><PERSON><PERSON>                         -15.323369\n", "Skew                                   -1.065369\n", "Kurtosis                                0.645216\n", "Tail Ratio                              0.436842\n", "Common Sense Ratio                           0.0\n", "Value at Risk                             -0.475\n", "Alpha                                       -1.0\n", "Beta                                    0.252235\n", "Name: b, dtype: object\n", "48.8 ms ± 623 µs per loop (mean ± std. dev. of 7 runs, 10 loops each)\n", "Start                                            2018-01-01 00:00:00\n", "End                                              2018-01-05 00:00:00\n", "Period                                               5 days 00:00:00\n", "Total Return [%]                                          106.666673\n", "Benchmark Return [%]                                       451.85973\n", "Annualized Return [%]        5635956288557277813711347645725802496.0\n", "Annualized Volatility [%]                                 247.292207\n", "Max Drawdown [%]                                           73.333332\n", "Max Drawdown Duration                                3 days 00:00:00\n", "Sharpe Ratio                                               -4.093819\n", "Calmar Ratio                                               -0.624999\n", "Omega Ratio                                                      inf\n", "<PERSON><PERSON><PERSON>                                                    inf\n", "Skew                                                        0.256871\n", "Kurtosis                                                   -0.254095\n", "Tail Ratio                                                   1.96934\n", "Common Sense Ratio            198600359944397791384906716353134592.0\n", "Value at Risk                                              -0.229167\n", "Alpha                                                 7177868.753186\n", "Beta                                                        0.861616\n", "Name: agg_func_mean, dtype: object\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.7/site-packages/ipykernel_launcher.py:6: UserWarning: Object has multiple columns. Aggregating using <function mean at 0x7fb48139f9d8>. Pass column to select a single column/group.\n", "  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["207 ms ± 2.43 ms per loop (mean ± std. dev. of 7 runs, 10 loops each)\n"]}], "source": ["print(returns['b'].vbt.returns.stats(\n", "    settings=dict(benchmark_rets=benchmark_rets, levy_alpha=3., risk_free=0.01, required_return=0.1)))\n", "%timeit big_returns[0].vbt.returns.stats(\\\n", "    silence_warnings=True,\\\n", "    settings=dict(benchmark_rets=big_benchmark_rets, levy_alpha=3., risk_free=0.01, required_return=0.1))\n", "\n", "print(returns.vbt.returns.stats(\n", "    settings=dict(benchmark_rets=benchmark_rets, levy_alpha=3., risk_free=0.01, required_return=0.1)))\n", "%timeit big_returns.vbt.returns.stats(\\\n", "    silence_warnings=True,\\\n", "    settings=dict(benchmark_rets=big_benchmark_rets, levy_alpha=3., risk_free=0.01, required_return=0.1))"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-01b39a\"><g class=\"clips\"><clipPath id=\"clip01b39axyplot\" class=\"plotclip\"><rect width=\"640\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip01b39ax\"><rect x=\"30\" y=\"0\" width=\"640\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip01b39ay\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip01b39axy\"><rect x=\"30\" y=\"46\" width=\"640\" height=\"261\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"30\" y=\"46\" width=\"640\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(65.75,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(207.87,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(350,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(492.13,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(634.25,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,290.2)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,239.87)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,189.55)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,139.22)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,88.9)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(30,46)\" clip-path=\"url('#clip01b39axyplot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter traced848fdf4-b652-49b3-8e72-dc13b1d715c5\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M35.75,244.2L462.13,66.06L604.25,16.8\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(127, 127, 127); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(35.75,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(127, 127, 127); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(177.87,184.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(127, 127, 127); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(320,124.85)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(127, 127, 127); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(462.13,66.06)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(127, 127, 127); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(604.25,16.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(127, 127, 127); fill-opacity: 1;\"/></g><g class=\"text\"/></g><g class=\"trace scatter trace1dd2e99c-7749-46cc-abb1-1f266c7a8b67\" style=\"stroke-miterlimit: 2;\"><g class=\"fills\"><g><path class=\"js-fill\" d=\"M35.75,244.2L604.25,42.9L604.25,244.2L35.75,244.2Z\" style=\"fill: rgb(0, 128, 0); fill-opacity: 0.3; stroke-width: 0;\"/></g></g><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M35.75,244.2L604.25,244.2\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(35.75,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(177.87,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(320,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(462.13,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(604.25,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"text\"/></g><g class=\"trace scatter trace08a92a94-6672-4b38-afa9-37f53c4a0e06\" style=\"stroke-miterlimit: 2;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M35.75,244.2L604.25,42.9\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(35.75,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(177.87,193.87)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(320,143.55)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(462.13,93.22)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(604.25,42.9)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"text\"/></g><g class=\"trace scatter trace12ed6016-02f8-41c3-b11f-8133203755d6\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M35.75,244.2L604.25,42.9\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(148, 103, 189); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(35.75,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(148, 103, 189); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(177.87,193.87)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(148, 103, 189); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(320,143.55)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(148, 103, 189); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(462.13,93.22)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(148, 103, 189); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(604.25,42.9)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(148, 103, 189); fill-opacity: 1;\"/></g><g class=\"text\"/></g><g class=\"trace scatter trace4415e5fa-c2e1-4db5-9baf-866ba3f6fa1f\" style=\"stroke-miterlimit: 2; opacity: 0;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M35.75,244.2L604.25,244.2\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(35.75,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(177.87,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(320,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(462.13,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(604.25,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(65.75,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(207.87,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(350,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(492.13,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(634.25,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 5</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,290.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,239.87)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,189.55)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,139.22)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">4</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,88.9)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">5</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-01b39a\"><g class=\"clips\"/><clipPath id=\"legend01b39a\"><rect width=\"166\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"><path data-index=\"0\" fill-rule=\"evenodd\" d=\"M30,290.2L670,290.2\" clip-path=\"url('#clip01b39ay')\" style=\"opacity: 1; stroke: rgb(128, 128, 128); stroke-opacity: 1; fill: rgb(0, 0, 0); fill-opacity: 0; stroke-dasharray: 9px, 9px; stroke-width: 2px;\"/></g></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(504,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"166\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend01b39a')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Benchmark</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(127, 127, 127); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(127, 127, 127); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"110.421875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(112.921875,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">a</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(148, 103, 189); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(148, 103, 189); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"49.71875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["returns['a'].vbt.returns.plot_cumulative(benchmark_rets).show_svg()"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-5bdfde\"><g class=\"clips\"><clipPath id=\"clip5bdfdexyplot\" class=\"plotclip\"><rect width=\"640\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip5bdfdex\"><rect x=\"30\" y=\"0\" width=\"640\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip5bdfdey\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"261\"/></clipPath><clipPath class=\"axesclip\" id=\"clip5bdfdexy\"><rect x=\"30\" y=\"46\" width=\"640\" height=\"261\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"30\" y=\"46\" width=\"640\" height=\"261\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(65.75,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(207.87,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(350,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(492.13,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(634.25,0)\" d=\"M0,46v261\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,290.2)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,239.87)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,189.55)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,139.22)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,88.9)\" d=\"M30,0h640\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(30,46)\" clip-path=\"url('#clip5bdfdexyplot')\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter tracef0e45c32-cbcd-4226-9d32-d05a2e366154\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M35.75,244.2L462.13,66.06L604.25,16.8\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(127, 127, 127); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(35.75,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(127, 127, 127); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(177.87,184.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(127, 127, 127); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(320,124.85)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(127, 127, 127); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(462.13,66.06)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(127, 127, 127); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(604.25,16.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(127, 127, 127); fill-opacity: 1;\"/></g><g class=\"text\"/></g><g class=\"trace scatter trace85d24af9-8a6c-4c4d-9657-1236a6337cf0\" style=\"stroke-miterlimit: 2;\"><g class=\"fills\"><g><path class=\"js-fill\" d=\"M35.75,244.2L604.25,42.9L604.25,16.8L462.13,66.06L35.75,244.2Z\" style=\"fill: rgb(255, 0, 0); fill-opacity: 0.3; stroke-width: 0;\"/></g></g><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M35.75,244.2L462.13,66.06L604.25,16.8\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(35.75,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(177.87,184.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(320,124.85)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(462.13,66.06)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(604.25,16.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"text\"/></g><g class=\"trace scatter tracefdb3bbce-ee27-4941-a1f3-2ed65bdf0204\" style=\"stroke-miterlimit: 2;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M35.75,244.2L604.25,42.9\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(35.75,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(177.87,193.87)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(320,143.55)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(462.13,93.22)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(604.25,42.9)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"text\"/></g><g class=\"trace scatter trace058924c8-9514-4fe0-9881-430fadcefc95\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M35.75,244.2L604.25,42.9\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(148, 103, 189); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(35.75,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(148, 103, 189); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(177.87,193.87)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(148, 103, 189); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(320,143.55)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(148, 103, 189); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(462.13,93.22)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(148, 103, 189); fill-opacity: 1;\"/><path class=\"point\" transform=\"translate(604.25,42.9)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(148, 103, 189); fill-opacity: 1;\"/></g><g class=\"text\"/></g><g class=\"trace scatter trace6af4f449-66c4-4929-90e1-a81342df8b8c\" style=\"stroke-miterlimit: 2; opacity: 0;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M35.75,244.2L462.13,66.06L604.25,16.8\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(0, 0, 0); stroke-opacity: 0; stroke-width: 0px; opacity: 1;\"/></g><g class=\"points\"><path class=\"point\" transform=\"translate(35.75,244.2)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(177.87,184.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(320,124.85)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(462.13,66.06)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><path class=\"point\" transform=\"translate(604.25,16.8)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(65.75,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"320\">Jan 1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"320\">2018</tspan></text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(207.87,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(350,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(492.13,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"320\" transform=\"translate(634.25,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Jan 5</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,290.2)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,239.87)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,189.55)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">3</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,139.22)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">4</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"29\" y=\"4.199999999999999\" transform=\"translate(0,88.9)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">5</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-5bdfde\"><g class=\"clips\"/><clipPath id=\"legend5bdfde\"><rect width=\"166\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"><path data-index=\"0\" fill-rule=\"evenodd\" d=\"M30,290.2L670,290.2\" clip-path=\"url('#clip5bdfdey')\" style=\"opacity: 1; stroke: rgb(128, 128, 128); stroke-opacity: 1; fill: rgb(0, 0, 0); fill-opacity: 0; stroke-dasharray: 9px, 9px; stroke-width: 2px;\"/></g></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(504,11.779999999999994)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"166\" height=\"29\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legend5bdfde')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Benchmark</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(127, 127, 127); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(127, 127, 127); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"110.421875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(112.921875,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">a</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(148, 103, 189); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M3,0A3,3 0 1,1 0,-3A3,3 0 0,1 3,0Z\" style=\"opacity: 1; stroke-width: 0px; fill: rgb(148, 103, 189); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"49.71875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["returns['a'].vbt.returns.plot_cumulative(benchmark_rets, fill_to_benchmark=True).show_svg()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 4}