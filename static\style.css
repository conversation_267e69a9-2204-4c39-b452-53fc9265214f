* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.mode-selector {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
}

.mode-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    background: rgba(255,255,255,0.2);
    color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.mode-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.mode-btn.active {
    background: rgba(255,255,255,0.9);
    color: #333;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    height: 70vh;
}

.panel {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.hidden {
    display: none !important;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

.section-header h3 {
    color: #333;
    font-size: 1.2rem;
}

.code-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.code-actions button, .code-actions select {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    font-size: 0.9rem;
}

.code-actions button:hover {
    background: #f5f5f5;
}

#code-editor {
    width: 100%;
    height: calc(100% - 60px);
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    resize: none;
    background: #f8f9fa;
}

.chat-section {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

.chat-header h3 {
    color: #333;
    font-size: 1.2rem;
}

#clear-chat {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    font-size: 0.9rem;
}

#clear-chat:hover {
    background: #f5f5f5;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
    max-height: calc(100% - 120px);
}

.message {
    margin-bottom: 20px;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message.user {
    text-align: right;
}

.message.assistant {
    text-align: left;
}

.message-content {
    display: inline-block;
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    word-wrap: break-word;
}

.message.user .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.message.assistant .message-content {
    background: #f1f3f4;
    color: #333;
    border: 1px solid #e0e0e0;
}

.message pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 10px 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.message code {
    background: #2d3748;
    color: #e2e8f0;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
}

.chat-input-section {
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

.input-container {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

#question-input {
    flex: 1;
    min-height: 40px;
    max-height: 120px;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 20px;
    resize: none;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    transition: border-color 0.3s ease;
}

#question-input:focus {
    outline: none;
    border-color: #667eea;
}

#send-btn {
    width: 45px;
    height: 45px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 18px;
    position: relative;
    overflow: visible; /* Allow loader to extend beyond button */
}

.small-loader {
    display: none;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.2); /* Add slight background for better visibility */
}

#send-btn.loading .small-loader {
    display: block;
}

#send-btn.loading .send-icon {
    display: none;
}

#send-btn:hover:not(:disabled) {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

#send-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    color: white;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mode spécifique - VectorBT */
.vectorbt-mode .main-content {
    grid-template-columns: 1fr;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .main-content {
        grid-template-columns: 1fr;
        height: auto;
        gap: 15px;
    }
    
    .panel, .chat-section {
        height: 400px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .mode-selector {
        flex-direction: column;
        align-items: center;
    }
    
    .mode-btn {
        width: 100%;
        max-width: 300px;
    }
}

/* Auto-resize textarea */
.auto-resize {
    overflow: hidden;
    resize: none;
}