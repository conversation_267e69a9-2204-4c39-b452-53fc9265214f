{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["In this example, we will compare development of a pairs trading strategy using backtrader and vectorbt."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import datetime\n", "import collections\n", "import math\n", "import pytz"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import scipy.stats as st\n", "\n", "SYMBOL1 = 'PEP'\n", "SYMBOL2 = 'KO'\n", "FROMDATE = datetime.datetime(2017, 1, 1, tzinfo=pytz.utc)\n", "TODATE = datetime.datetime(2019, 1, 1, tzinfo=pytz.utc)\n", "PERIOD = 100\n", "CASH = 100000\n", "COMMPERC = 0.005  # 0.5%\n", "ORDER_PCT1 = 0.1\n", "ORDER_PCT2 = 0.1\n", "UPPER = st.norm.ppf(1 - 0.05 / 2)\n", "LOWER = -st.norm.ppf(1 - 0.05 / 2)\n", "MODE = 'OLS'  # OLS, log_return"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import vectorbt as vbt\n", "\n", "start_date = FROMDATE.replace(tzinfo=pytz.utc)\n", "end_date = TODATE.replace(tzinfo=pytz.utc)\n", "data = vbt.YFData.download([SYMBOL1, SYMBOL2], start=start_date, end=end_date)\n", "data = data.loc[(data.wrapper.index >= start_date) & (data.wrapper.index < end_date)]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "2017-01-03 00:00:00+00:00   91.831129   91.962386   91.192316   91.577354   \n", "2018-12-31 00:00:00+00:00  102.775161  103.249160  101.604091  102.682220   \n", "\n", "                            Volume  Dividends  Stock Splits  \n", "Date                                                         \n", "2017-01-03 00:00:00+00:00  3741200        0.0             0  \n", "2018-12-31 00:00:00+00:00  5019100        0.0             0  \n", "                                Open       High        Low      Close  \\\n", "Date                                                                    \n", "2017-01-03 00:00:00+00:00  35.801111  36.068542  35.611321  36.059914   \n", "2018-12-31 00:00:00+00:00  43.810820  43.856946  43.321878  43.681664   \n", "\n", "                             Volume  Dividends  Stock Splits  \n", "Date                                                          \n", "2017-01-03 00:00:00+00:00  14711000        0.0             0  \n", "2018-12-31 00:00:00+00:00  10576300        0.0             0  \n"]}], "source": ["print(data.data[SYMBOL1].iloc[[0, -1]])\n", "print(data.data[SYMBOL2].iloc[[0, -1]])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## backtrader"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Adapted version of https://github.com/mementum/backtrader/blob/master/contrib/samples/pair-trading/pair-trading.py"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import backtrader as bt\n", "import backtrader.feeds as btfeeds\n", "import backtrader.indicators as btind\n", "\n", "class CommInfoFloat(bt.CommInfoBase):\n", "    \"\"\"Commission schema that keeps size as float.\"\"\"\n", "    params = (\n", "        ('stocklike', True),\n", "        ('commtype', bt.CommInfoBase.COMM_PERC),\n", "        ('percabs', True),\n", "      )\n", "    \n", "    def getsize(self, price, cash):\n", "        if not self._stocklike:\n", "            return self.p.leverage * (cash / self.get_margin(price))\n", "\n", "        return self.p.leverage * (cash / price)\n", "    \n", "class OLSSlopeIntercept(btind.PeriodN):\n", "    \"\"\"Calculates a linear regression using OLS.\"\"\"\n", "    _mindatas = 2  # ensure at least 2 data feeds are passed\n", "\n", "    packages = (\n", "        ('pandas', 'pd'),\n", "        ('statsmodels.api', 'sm'),\n", "    )\n", "    lines = ('slope', 'intercept',)\n", "    params = (\n", "        ('period', 10),\n", "    )\n", "\n", "    def next(self):\n", "        p0 = pd.Series(self.data0.get(size=self.p.period))\n", "        p1 = pd.Series(self.data1.get(size=self.p.period))\n", "        p1 = sm.add_constant(p1)\n", "        intercept, slope = sm.OLS(p0, p1).fit().params\n", "\n", "        self.lines.slope[0] = slope\n", "        self.lines.intercept[0] = intercept\n", "        \n", "        \n", "class Log(btind.Indicator):\n", "    \"\"\"Calculates log.\"\"\"\n", "    lines = ('log',)\n", "    \n", "    def next(self):\n", "        self.l.log[0] = math.log(self.data[0])\n", "\n", "\n", "class OLSSpread(btind.PeriodN):\n", "    \"\"\"Calculates the z-score of the OLS spread.\"\"\"\n", "    _mindatas = 2  # ensure at least 2 data feeds are passed\n", "    lines = ('spread', 'spread_mean', 'spread_std', 'zscore',)\n", "    params = (('period', 10),)\n", "\n", "    def __init__(self):\n", "        data0_log = Log(self.data0)\n", "        data1_log = Log(self.data1)\n", "        slint = OLSSlopeIntercept(data0_log, data1_log, period=self.p.period)\n", "\n", "        spread = data0_log - (slint.slope * data1_log + slint.intercept)\n", "        self.l.spread = spread\n", "\n", "        self.l.spread_mean = bt.ind.SMA(spread, period=self.p.period)\n", "        self.l.spread_std = bt.ind.StdDev(spread, period=self.p.period)\n", "        self.l.zscore = (spread - self.l.spread_mean) / self.l.spread_std\n", "    \n", "class LogReturns(btind.PeriodN):\n", "    \"\"\"Calculates the log returns.\"\"\"\n", "    lines = ('logret',)\n", "    params = (('period', 1),)\n", "    \n", "    def __init__(self):\n", "        self.addminperiod(self.p.period + 1)\n", "    \n", "    def next(self):\n", "        self.l.logret[0] = math.log(self.data[0] / self.data[-self.p.period])\n", "    \n", "class LogReturnSpread(btind.PeriodN):\n", "    \"\"\"Calculates the spread of the log returns.\"\"\"\n", "    _mindatas = 2  # ensure at least 2 data feeds are passed\n", "    lines = ('logret0', 'logret1', 'spread', 'spread_mean', 'spread_std', 'zscore',)\n", "    params = (('period', 10),)\n", "\n", "    def __init__(self):\n", "        self.l.logret0 = LogReturns(self.data0, period=1)\n", "        self.l.logret1 = LogReturns(self.data1, period=1)\n", "        self.l.spread = self.l.logret0 - self.l.logret1\n", "        self.l.spread_mean = bt.ind.SMA(self.l.spread, period=self.p.period)\n", "        self.l.spread_std = bt.ind.StdDev(self.l.spread, period=self.p.period)\n", "        self.l.zscore = (self.l.spread - self.l.spread_mean) / self.l.spread_std\n", "\n", "class PairTradingStrategy(bt.Strategy):\n", "    \"\"\"Basic pair trading strategy.\"\"\"\n", "    params = dict(\n", "        period=PERIOD,\n", "        order_pct1=ORDER_PCT1,\n", "        order_pct2=ORDER_PCT2,\n", "        printout=True,\n", "        upper=UPPER,\n", "        lower=LOWER,\n", "        mode=MODE\n", "    )\n", "\n", "    def log(self, txt, dt=None):\n", "        if self.p.printout:\n", "            dt = dt or self.data.datetime[0]\n", "            dt = bt.num2date(dt)\n", "            print('%s, %s' % (dt.isoformat(), txt))\n", "\n", "    def notify_order(self, order):\n", "        if order.status in [bt.Order.Submitted, bt.Order.Accepted]:\n", "            return  # Await further notifications\n", "\n", "        if order.status == order.Completed:\n", "            if order.isbuy():\n", "                buytxt = 'BUY COMPLETE {}, size = {:.2f}, price = {:.2f}'.format(\n", "                    order.data._name, order.executed.size, order.executed.price)\n", "                self.log(buytxt, order.executed.dt)\n", "            else:\n", "                selltxt = 'SELL COMPLETE {}, size = {:.2f}, price = {:.2f}'.format(\n", "                    order.data._name, order.executed.size, order.executed.price)\n", "                self.log(selltxt, order.executed.dt)\n", "\n", "        elif order.status in [order.Expired, order.Canceled, order.Margin]:\n", "            self.log('%s ,' % order.Status[order.status])\n", "            pass  # Simply log\n", "\n", "        # Allow new orders\n", "        self.orderid = None\n", "\n", "    def __init__(self):\n", "        # To control operation entries\n", "        self.orderid = None\n", "        self.order_pct1 = self.p.order_pct1\n", "        self.order_pct2 = self.p.order_pct2\n", "        self.upper = self.p.upper\n", "        self.lower = self.p.lower\n", "        self.status = 0\n", "        \n", "        # Signals performed with PD.OLS :\n", "        if self.p.mode == 'log_return':\n", "            self.transform = LogReturnSpread(self.data0, self.data1, period=self.p.period)\n", "        elif self.p.mode == 'OLS':\n", "            self.transform = OLSSpread(self.data0, self.data1, period=self.p.period)\n", "        else:\n", "            raise ValueError(\"Unknown mode\")\n", "        self.spread = self.transform.spread\n", "        self.zscore = self.transform.zscore\n", "        \n", "        # For tracking\n", "        self.spread_sr = pd.Series(dtype=float, name='spread')\n", "        self.zscore_sr = pd.Series(dtype=float, name='zscore')\n", "        self.short_signal_sr = pd.Series(dtype=bool, name='short_signals')\n", "        self.long_signal_sr = pd.Series(dtype=bool, name='long_signals')\n", "\n", "    def next(self):\n", "        if self.orderid:\n", "            return  # if an order is active, no new orders are allowed\n", "        \n", "        self.spread_sr[self.data0.datetime.datetime()] = self.spread[0]\n", "        self.zscore_sr[self.data0.datetime.datetime()] = self.zscore[0]\n", "        self.short_signal_sr[self.data0.datetime.datetime()] = False\n", "        self.long_signal_sr[self.data0.datetime.datetime()] = False\n", "\n", "        if self.zscore[0] > self.upper and self.status != 1:\n", "            # Check conditions for shorting the spread & place the order\n", "            self.short_signal_sr[self.data0.datetime.datetime()] = True\n", "\n", "            # Placing the order\n", "            self.log('SELL CREATE {}, price = {:.2f}, target pct = {:.2%}'.format(\n", "                self.data0._name, self.data0.close[0], -self.order_pct1))\n", "            self.order_target_percent(data=self.data0, target=-self.order_pct1)\n", "            self.log('BUY CREATE {}, price = {:.2f}, target pct = {:.2%}'.format(\n", "                self.data1._name, self.data1.close[0], self.order_pct2))\n", "            self.order_target_percent(data=self.data1, target=self.order_pct2)\n", "\n", "            self.status = 1\n", "\n", "        elif self.zscore[0] < self.lower and self.status != 2:\n", "            # Check conditions for longing the spread & place the order\n", "            self.long_signal_sr[self.data0.datetime.datetime()] = True\n", "\n", "            # Place the order\n", "            self.log('SELL CREATE {}, price = {:.2f}, target pct = {:.2%}'.format(\n", "                self.data1._name, self.data1.close[0], -self.order_pct2))\n", "            self.order_target_percent(data=self.data1, target=-self.order_pct2)\n", "            self.log('BUY CREATE {}, price = {:.2f}, target pct = {:.2%}'.format(\n", "                self.data0._name, self.data0.close[0], self.order_pct1))\n", "            self.order_target_percent(data=self.data0, target=self.order_pct1)\n", "                     \n", "            self.status = 2\n", "\n", "    def stop(self):\n", "        if self.p.printout:\n", "            print('==================================================')\n", "            print('Starting Value - %.2f' % self.broker.startingcash)\n", "            print('Ending   Value - %.2f' % self.broker.getvalue())\n", "            print('==================================================')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["class DataAnalyzer(bt.analyzers.Analyzer):\n", "    \"\"\"Analyzer to extract OHLCV.\"\"\"\n", "    def create_analysis(self):\n", "        self.rets0 = {}\n", "        self.rets1 = {}\n", "\n", "    def next(self):\n", "        self.rets0[self.strategy.datetime.datetime()] = [\n", "            self.data0.open[0],\n", "            self.data0.high[0],\n", "            self.data0.low[0],\n", "            self.data0.close[0],\n", "            self.data0.volume[0]\n", "        ]\n", "        self.rets1[self.strategy.datetime.datetime()] = [\n", "            self.data1.open[0],\n", "            self.data1.high[0],\n", "            self.data1.low[0],\n", "            self.data1.close[0],\n", "            self.data1.volume[0]\n", "        ]\n", "\n", "    def get_analysis(self):\n", "        return self.rets0, self.rets1\n", "\n", "class CashValueAnalyzer(bt.analyzers.Analyzer):\n", "    \"\"\"Analyzer to extract cash and value.\"\"\"\n", "    def create_analysis(self):\n", "        self.rets = {}\n", "\n", "    def notify_cashvalue(self, cash, value):\n", "        self.rets[self.strategy.datetime.datetime()] = (cash, value)\n", "\n", "    def get_analysis(self):\n", "        return self.rets\n", "    \n", "class OrderAnalyzer(bt.analyzers.Analyzer):\n", "    \"\"\"Analyzer to extract order price, size, value, and paid commission.\"\"\"\n", "    def create_analysis(self):\n", "        self.rets0 = {}\n", "        self.rets1 = {}\n", "\n", "    def notify_order(self, order):\n", "        if order.status == order.Completed:\n", "            if order.data._name == SYMBOL1:\n", "                rets = self.rets0\n", "            else:\n", "                rets = self.rets1\n", "            rets[self.strategy.datetime.datetime()] = (\n", "                order.executed.price,\n", "                order.executed.size,\n", "                -order.executed.size * order.executed.price,\n", "                order.executed.comm\n", "            )\n", "\n", "    def get_analysis(self):\n", "        return self.rets0, self.rets1"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def prepare_cerebro(data0, data1, use_analyzers=True, **params):\n", "    # Create a cerebro\n", "    cerebro = bt.<PERSON><PERSON><PERSON>()\n", "\n", "    # Add the 1st data to cerebro\n", "    cerebro.adddata(data0)\n", "\n", "    # Add the 2nd data to cerebro\n", "    cerebro.adddata(data1)\n", "\n", "    # Add the strategy\n", "    cerebro.addstrategy(PairTradingStrategy, **params)\n", "\n", "    # Add the commission - only stocks like a for each operation\n", "    cerebro.broker.setcash(CASH)\n", "\n", "    # Add the commission - only stocks like a for each operation\n", "    comminfo = CommInfoFloat(commission=COMMPERC)\n", "    cerebro.broker.addcommissioninfo(comminfo)\n", "\n", "    if use_analyzers:\n", "        # Add analyzers    \n", "        cerebro.addanalyzer(DataAnalyzer)\n", "        cerebro.addanalyzer(CashValueAnalyzer)\n", "        cerebro.addanalyzer(OrderAnalyzer)\n", "    \n", "    return cerebro"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["class PandasData(btfeeds.PandasData):\n", "    params = (\n", "        # Possible values for datetime (must always be present)\n", "        #  None : datetime is the \"index\" in the Pandas Dataframe\n", "        #  -1 : autodetect position or case-wise equal name\n", "        #  >= 0 : numeric index to the colum in the pandas dataframe\n", "        #  string : column name (as index) in the pandas dataframe\n", "        ('datetime', None),\n", "\n", "        ('open', 'Open'),\n", "        ('high', 'High'),\n", "        ('low', 'Low'),\n", "        ('close', 'Close'),\n", "        ('volume', 'Volume'),\n", "        ('openinterest', None),\n", "    )"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/miniconda3/lib/python3.7/site-packages/statsmodels/tsa/tsatools.py:142: FutureWarning: In a future version of pandas all arguments of concat except for the argument 'objs' will be keyword-only\n", "  x = pd.concat(x[::order], 1)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2017-11-14T00:00:00, SELL CREATE PEP, price = 103.41, target pct = -10.00%\n", "2017-11-14T00:00:00, BUY CREATE KO, price = 41.95, target pct = 10.00%\n", "2017-11-15T00:00:00, SELL COMPLETE PEP, size = -96.70, price = 103.32\n", "2017-11-15T00:00:00, BUY COMPLETE KO, size = 238.39, price = 41.85\n", "2018-04-11T00:00:00, SELL CREATE KO, price = 39.57, target pct = -10.00%\n", "2018-04-11T00:00:00, BUY CREATE PEP, price = 98.49, target pct = 10.00%\n", "2018-04-12T00:00:00, SELL COMPLETE KO, size = -490.65, price = 39.65\n", "2018-04-12T00:00:00, BUY COMPLETE PEP, size = 198.06, price = 98.74\n", "2018-08-31T00:00:00, SELL CREATE PEP, price = 102.44, target pct = -10.00%\n", "2018-08-31T00:00:00, BUY CREATE KO, price = 40.45, target pct = 10.00%\n", "2018-09-04T00:00:00, SELL COMPLETE PEP, size = -198.78, price = 102.26\n", "2018-09-04T00:00:00, BUY COMPLETE KO, size = 498.98, price = 40.48\n", "2018-10-02T00:00:00, SELL CREATE KO, price = 42.57, target pct = -10.00%\n", "2018-10-02T00:00:00, BUY CREATE PEP, price = 100.25, target pct = 10.00%\n", "2018-10-03T00:00:00, SELL COMPLETE KO, size = -482.28, price = 42.52\n", "2018-10-03T00:00:00, BUY COMPLETE PEP, size = 197.45, price = 100.70\n", "2018-11-30T00:00:00, SELL CREATE PEP, price = 112.44, target pct = -10.00%\n", "2018-11-30T00:00:00, BUY CREATE KO, price = 46.50, target pct = 10.00%\n", "2018-12-03T00:00:00, SELL COMPLETE PEP, size = -189.20, price = 111.09\n", "2018-12-03T00:00:00, BUY COMPLETE KO, size = 451.21, price = 46.01\n", "==================================================\n", "Starting Value - 100000.00\n", "Ending   Value - 100284.08\n", "==================================================\n"]}], "source": ["# Create the 1st data\n", "data0 = PandasData(dataname=data.data[SYMBOL1], name=SYMBOL1)\n", "\n", "# Create the 2nd data\n", "data1 = PandasData(dataname=data.data[SYMBOL2], name=SYMBOL2)\n", "\n", "# Prepare a cerebro\n", "cerebro = prepare_cerebro(data0, data1)\n", "\n", "# And run it\n", "bt_strategy = cerebro.run()[0]"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1300x800 with 7 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["[[<Figure size 1300x800 with 7 Axes>]]"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "\n", "plt.rcParams[\"figure.figsize\"] = (13, 8)\n", "cerebro.plot(iplot=False)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(502, 5)\n", "(502, 5)\n", "                  open        high         low       close     volume\n", "2017-01-03   91.831129   91.962386   91.192316   91.577354  3741200.0\n", "2018-12-31  102.775161  103.249160  101.604091  102.682220  5019100.0\n", "                 open       high        low      close      volume\n", "2017-01-03  35.801111  36.068542  35.611321  36.059914  14711000.0\n", "2018-12-31  43.810820  43.856946  43.321878  43.681664  10576300.0\n"]}], "source": ["# Extract OHLCV\n", "bt_s1_rets, bt_s2_rets = bt_strategy.analyzers.dataanalyzer.get_analysis()\n", "data_cols = ['open', 'high', 'low', 'close', 'volume']\n", "bt_s1_ohlcv = pd.DataFrame.from_dict(bt_s1_rets, orient='index', columns=data_cols)\n", "bt_s2_ohlcv = pd.DataFrame.from_dict(bt_s2_rets, orient='index', columns=data_cols)\n", "\n", "print(bt_s1_ohlcv.shape)\n", "print(bt_s2_ohlcv.shape)\n", "\n", "print(bt_s1_ohlcv.iloc[[0, -1]])\n", "print(bt_s2_ohlcv.iloc[[0, -1]])"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["try:\n", "    np.testing.assert_allclose(bt_s1_ohlcv.values, data.data[SYMBOL1].iloc[:, :5].values)\n", "    np.testing.assert_allclose(bt_s2_ohlcv.values, data.data[SYMBOL2].iloc[:, :5].values)\n", "except AssertionError as e:\n", "    print(e)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2017-01-03    100000.000000\n", "2018-12-31    100020.682139\n", "Name: cash, dtype: float64\n", "2017-01-03    100000.000000\n", "2018-12-31    100284.081822\n", "Name: value, dtype: float64\n"]}], "source": ["# Extract cash and value series\n", "bt_cashvalue_rets = bt_strategy.analyzers.cashvalueanalyzer.get_analysis()\n", "bt_cashvalue_df = pd.DataFrame.from_dict(bt_cashvalue_rets, orient='index', columns=['cash', 'value'])\n", "bt_cash = bt_cashvalue_df['cash']\n", "bt_value = bt_cashvalue_df['value']\n", "\n", "print(bt_cash.iloc[[0, -1]])\n", "print(bt_value.iloc[[0, -1]])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["            order_price  order_size   order_value  order_comm\n", "2017-11-15   103.316229  -96.698241   9990.497637   49.952488\n", "2018-12-03   111.094282 -189.202087  21019.269920  105.096350\n", "            order_price  order_size   order_value  order_comm\n", "2017-11-15    41.851497  238.385955  -9976.809115   49.884046\n", "2018-12-03    46.006429  451.211817 -20758.644494  103.793222\n"]}], "source": ["# Extract order info\n", "bt_s1_order_rets, bt_s2_order_rets = bt_strategy.analyzers.orderanalyzer.get_analysis()\n", "order_cols = ['order_price', 'order_size', 'order_value', 'order_comm']\n", "bt_s1_orders = pd.DataFrame.from_dict(bt_s1_order_rets, orient='index', columns=order_cols)\n", "bt_s2_orders = pd.DataFrame.from_dict(bt_s2_order_rets, orient='index', columns=order_cols)\n", "\n", "print(bt_s1_orders.iloc[[0, -1]])\n", "print(bt_s2_orders.iloc[[0, -1]])"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2017-10-16   -0.009388\n", "2018-12-31   -0.021157\n", "Name: spread, dtype: float64\n", "2017-10-16    0.242706\n", "2018-12-31   -0.307914\n", "Name: zscore, dtype: float64\n"]}], "source": ["# Extract spread and z-score\n", "bt_spread = bt_strategy.spread_sr\n", "bt_zscore = bt_strategy.zscore_sr\n", "\n", "print(bt_spread.iloc[[0, -1]])\n", "print(bt_zscore.iloc[[0, -1]])"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2017-11-14    True\n", "2018-08-31    True\n", "2018-11-30    True\n", "Name: short_signals, dtype: bool\n", "2018-04-11    True\n", "2018-10-02    True\n", "Name: long_signals, dtype: bool\n"]}], "source": ["# Extract signals\n", "bt_short_signals = bt_strategy.short_signal_sr\n", "bt_long_signals = bt_strategy.long_signal_sr\n", "\n", "print(bt_short_signals[bt_short_signals])\n", "print(bt_long_signals[bt_long_signals])"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.07 s ± 16.3 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["# How fast is bt?\n", "cerebro = prepare_cerebro(data0, data1, use_analyzers=False, printout=False)\n", "\n", "%timeit cerebro.run(preload=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## vectorbt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Using Portfolio.from_orders"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["from numba import njit\n", "\n", "@njit\n", "def rolling_logret_zscore_nb(a, b, period):\n", "    \"\"\"Calculate the log return spread.\"\"\"\n", "    spread = np.full_like(a, np.nan, dtype=np.float64)\n", "    spread[1:] = np.log(a[1:] / a[:-1]) - np.log(b[1:] / b[:-1])\n", "    zscore = np.full_like(a, np.nan, dtype=np.float64)\n", "    for i in range(a.shape[0]):\n", "        from_i = max(0, i + 1 - period)\n", "        to_i = i + 1\n", "        if i < period - 1:\n", "            continue\n", "        spread_mean = np.mean(spread[from_i:to_i])\n", "        spread_std = np.std(spread[from_i:to_i])\n", "        zscore[i] = (spread[i] - spread_mean) / spread_std\n", "    return spread, zscore\n", "\n", "@njit\n", "def ols_spread_nb(a, b):\n", "    \"\"\"Calculate the OLS spread.\"\"\"\n", "    a = np.log(a)\n", "    b = np.log(b)\n", "    _b = np.vstack((b, np.ones(len(b)))).T\n", "    slope, intercept = np.dot(np.linalg.inv(np.dot(_b.T, _b)), np.dot(_b.T, a))\n", "    spread = a - (slope * b + intercept)\n", "    return spread[-1]\n", "    \n", "@njit\n", "def rolling_ols_zscore_nb(a, b, period):\n", "    \"\"\"Calculate the z-score of the rolling OLS spread.\"\"\"\n", "    spread = np.full_like(a, np.nan, dtype=np.float64)\n", "    zscore = np.full_like(a, np.nan, dtype=np.float64)\n", "    for i in range(a.shape[0]):\n", "        from_i = max(0, i + 1 - period)\n", "        to_i = i + 1\n", "        if i < period - 1:\n", "            continue\n", "        spread[i] = ols_spread_nb(a[from_i:to_i], b[from_i:to_i])\n", "        spread_mean = np.mean(spread[from_i:to_i])\n", "        spread_std = np.std(spread[from_i:to_i])\n", "        zscore[i] = (spread[i] - spread_mean) / spread_std\n", "    return spread, zscore"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Calculate OLS z-score using <PERSON><PERSON> for a nice speedup\n", "if MODE == 'OLS':\n", "    vbt_spread, vbt_zscore = rolling_ols_zscore_nb(\n", "        bt_s1_ohlcv['close'].values, \n", "        bt_s2_ohlcv['close'].values, \n", "        PERIOD\n", "    )\n", "elif <PERSON> == 'log_return':\n", "    vbt_spread, vbt_zscore = rolling_logret_zscore_nb(\n", "        bt_s1_ohlcv['close'].values, \n", "        bt_s2_ohlcv['close'].values, \n", "        PERIOD\n", "    )\n", "else:\n", "    raise ValueError(\"Unknown mode\")\n", "vbt_spread = pd.Series(vbt_spread, index=bt_s1_ohlcv.index, name='spread')\n", "vbt_zscore = pd.Series(vbt_zscore, index=bt_s1_ohlcv.index, name='zscore')"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# Assert equality of bt and vbt z-score arrays\n", "pd.testing.assert_series_equal(bt_spread, vbt_spread[bt_spread.index])\n", "pd.testing.assert_series_equal(bt_zscore, vbt_zscore[bt_zscore.index])"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Generate short and long spread signals\n", "vbt_short_signals = (vbt_zscore > UPPER).rename('short_signals')\n", "vbt_long_signals = (vbt_zscore < LOWER).rename('long_signals')"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-043d55\"><g class=\"clips\"><clipPath id=\"clip043d55xyplot\" class=\"plotclip\"><rect width=\"620\" height=\"199.975\"/></clipPath><clipPath id=\"clip043d55x2y2plot\" class=\"plotclip\"><rect width=\"620\" height=\"199.975\"/></clipPath><clipPath class=\"axesclip\" id=\"clip043d55x\"><rect x=\"50\" y=\"0\" width=\"620\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clip043d55y\"><rect x=\"0\" y=\"49\" width=\"700\" height=\"199.975\"/></clipPath><clipPath class=\"axesclip\" id=\"clip043d55xy\"><rect x=\"50\" y=\"49\" width=\"620\" height=\"199.975\"/></clipPath><clipPath class=\"axesclip\" id=\"clip043d55y2\"><rect x=\"0\" y=\"270.025\" width=\"700\" height=\"199.975\"/></clipPath><clipPath class=\"axesclip\" id=\"clip043d55xy2\"><rect x=\"50\" y=\"270.025\" width=\"620\" height=\"199.975\"/></clipPath><clipPath class=\"axesclip\" id=\"clip043d55x2\"><rect x=\"50\" y=\"0\" width=\"620\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clip043d55x2y\"><rect x=\"50\" y=\"49\" width=\"620\" height=\"199.975\"/></clipPath><clipPath class=\"axesclip\" id=\"clip043d55x2y2\"><rect x=\"50\" y=\"270.025\" width=\"620\" height=\"199.975\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"50\" y=\"49\" width=\"620\" height=\"199.975\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/><rect class=\"bg\" x=\"50\" y=\"270.025\" width=\"620\" height=\"199.975\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"><path data-index=\"0\" fill-rule=\"evenodd\" d=\"M50,307.025H670V418.44499999999994H50Z\" clip-path=\"url(#clip043d55y2)\" style=\"opacity: 0.2; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(128, 128, 128); fill-opacity: 1; stroke-width: 0px;\"/></g></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(123.86,0)\" d=\"M0,49v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(200.24,0)\" d=\"M0,49v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(277.45,0)\" d=\"M0,49v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(354.67,0)\" d=\"M0,49v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(430.21,0)\" d=\"M0,49v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(506.58,0)\" d=\"M0,49v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(583.8,0)\" d=\"M0,49v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(661.01,0)\" d=\"M0,49v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,198.3)\" d=\"M50,0h620\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,57.59)\" d=\"M50,0h620\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,127.95)\" d=\"M50,0h620\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(50,49)\" clip-path=\"url(#clip043d55xyplot)\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter traceec6fdfee-4910-4da7-aaa6-9dce742410ec\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M119.18,110.21L120.02,102.6L123.38,100.94L125.06,109.25L125.9,104.91L129.25,101.93L130.09,91.89L130.93,91.57L131.77,95.8L135.13,91.24L135.97,86.05L136.81,85.56L137.65,90.75L141,85.21L141.84,79.53L142.68,89.6L143.52,84.2L146.04,86.12L146.88,89.22L148.56,91.33L149.4,87.69L151.91,85.77L153.59,88.49L155.27,75.09L157.79,89.79L158.63,94.8L159.47,86.26L160.31,93.27L161.15,87.83L163.66,88.77L164.5,86.23L165.34,85.75L166.18,83.29L167.02,74.6L169.54,76.31L172.9,95.19L175.41,90.93L177.09,96.32L177.93,89.08L178.77,84.39L181.29,80.06L182.97,82.86L183.81,88.61L188,67.22L188.84,66.55L189.68,68.35L190.52,74.04L193.04,66.75L193.88,69.33L196.4,93.37L198.92,93.69L201.43,83.85L202.27,87.14L205.63,89.62L206.47,84.6L207.31,83.58L208.15,106.91L210.67,102.8L211.5,114.07L212.34,122.03L213.18,116.69L214.02,109.29L216.54,105.46L217.38,108.29L218.22,115.06L219.06,117.13L219.9,129.67L222.42,130.56L223.26,129.29L224.93,120.86L225.77,124.59L229.13,154.9L229.97,154.6L230.81,139.5L231.65,138.77L234.17,142.08L235.01,133.54L237.52,105.3L240.04,92.16L241.72,109.66L242.56,99.04L243.4,111.66L245.92,109.59L247.59,128.49L248.43,117.39L249.27,119.22L251.79,132.1L252.63,122.75L253.47,124.95L254.31,124.42L255.15,120.44L257.67,137.11L258.51,116.02L259.35,93.67L260.18,98.87L261.02,79.66L263.54,62.66L264.38,38.72L265.22,52.78L266.06,53.88L266.9,65.97L269.42,64.83L270.26,49.28L271.1,60.94L272.77,51.06L275.29,51.9L276.13,41.57L276.97,42.9L277.81,34.03L281.17,22.72L282.01,22.97L282.85,10L283.69,37.11L284.52,39.48L287.04,30.03L287.88,30.13L288.72,27.01L289.56,26.2L290.4,14.74L292.92,27.65L293.76,23.88L294.6,19.96L295.44,24.38L296.27,20.76L299.63,20.65L302.15,12.21L305.51,28.7L306.35,30.89L307.19,36.47L308.02,33.2L310.54,40.16L313.06,55.7L313.9,53.24L317.26,59.36L318.1,54.76L318.94,60.65L319.78,59.48L322.29,55.59L323.13,63.93L324.81,86.82L325.65,101.87L328.17,92.74L329.01,78.92L329.85,81.27L330.69,75.75L334.04,39.45L334.88,42.61L335.72,40.41L336.56,28.56L337.4,23.53L339.92,47.34L340.76,53.92L341.6,88.96L342.44,89.42L343.28,95.39L347.47,77.01L348.31,68.08L349.15,81.31L351.67,66.54L352.51,55.78L353.35,59.86L354.19,78.26L355.03,76.8L358.38,78.25L359.22,72.12L360.06,64.26L360.9,70.49L363.42,59.42L364.26,50.93L365.1,51.57L365.94,56.59L366.78,55.3L369.29,65.85L370.13,69.26L370.97,79.52L371.81,82.19L372.65,91.11L375.17,94.37L376.01,90.25L376.85,85.16L377.69,89.59L381.04,91.12L381.88,100.84L383.56,105.19L384.4,105.24L386.92,101.07L387.76,106.31L390.28,126.24L392.79,123.68L394.47,154.22L395.31,164.7L396.15,183.62L398.67,189.98L401.19,126.09L402.03,168.87L404.54,173.41L405.38,169.26L406.22,168.85L407.06,152.15L407.9,151.85L410.42,149.37L411.26,150.64L412.1,155.08L412.94,156.73L413.78,150.17L416.3,146.81L417.13,132.97L417.97,116.77L418.81,144.73L419.65,142.47L422.17,125.24L423.01,109.97L423.85,110.04L424.69,108.88L425.53,110.61L428.88,113.78L429.72,126.84L430.56,128.33L431.4,133.89L433.92,154.26L434.76,132.31L435.6,141.31L436.44,134.98L437.28,142.51L439.8,141.23L440.64,137.64L441.47,114.91L443.15,100.14L445.67,100.57L446.51,95.78L447.35,80.64L449.03,48.22L451.55,60.56L452.39,59.38L453.22,41.45L454.9,81.15L457.42,65.48L459.94,117.92L460.78,113.92L463.3,120.67L464.14,87.01L464.97,81.07L465.81,86.92L466.65,76.1L469.17,72.45L470.01,78.23L470.85,71.49L471.69,59.87L472.53,67.09L475.05,68.46L475.89,82.15L477.56,127.34L478.4,130.2L480.92,128.63L481.76,134.14L482.6,134.14L483.44,107.57L484.28,106.75L486.8,93.51L487.64,121.58L488.48,111.78L489.31,116.93L490.15,112.86L492.67,98.3L493.51,99.76L494.35,104.43L495.19,100.94L496.03,109.73L498.55,115.84L499.39,103.95L500.23,107.7L501.9,92.67L504.42,91.72L505.26,90.92L506.1,77.49L507.78,39.77L511.14,62.14L513.65,77.18L516.17,83.8L517.01,84.88L517.85,89.19L518.69,74.58L519.53,87.39L522.05,93.06L522.89,91.17L523.73,94.87L524.57,109L525.4,108.74L527.92,115.96L528.76,100.61L529.6,113.83L530.44,125.89L531.28,123.44L533.8,137.21L534.64,172.17L535.48,157.95L536.32,167.13L537.16,168.02L539.67,167.58L540.51,162.15L541.35,143.45L543.03,131.78L545.55,135.42L546.39,132.83L547.23,132.88L548.91,120.65L551.42,119.12L552.26,120.13L553.1,93.59L553.94,87.19L554.78,100.67L557.3,82.53L558.14,96.38L559.82,119.39L560.66,124.35L564.01,87.58L564.85,83.8L566.53,63.11L569.05,71.24L569.89,69.39L570.73,74.02L571.57,71.74L572.41,60.01L574.92,58.19L575.76,78.47L576.6,78.25L578.28,80.18L581.64,72.35L582.48,52.73L583.32,52.36L584.16,32.68L587.51,71.58L589.19,69.77L590.03,77.99L592.55,76.37L595.07,57.47L595.91,107.89L598.42,102.99L599.26,114.88L600.1,129.37L600.94,120.14L601.78,129.94L604.3,144.32L606.82,126.86L607.66,108.05L610.17,108.72\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"/><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"49\" y=\"4.199999999999999\" transform=\"translate(0,198.3)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">−0.05</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"49\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,127.95)\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"49\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,57.59)\">0.05</text></g></g><g class=\"overaxes-above\"/></g><g class=\"subplot x2y2\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x2\"><path class=\"x2grid crisp\" transform=\"translate(123.86,0)\" d=\"M0,270.025v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(200.24,0)\" d=\"M0,270.025v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(277.45,0)\" d=\"M0,270.025v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(354.67,0)\" d=\"M0,270.025v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(430.21,0)\" d=\"M0,270.025v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(506.58,0)\" d=\"M0,270.025v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(583.8,0)\" d=\"M0,270.025v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(661.01,0)\" d=\"M0,270.025v199.975\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,419.575)\" d=\"M50,0h620\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,305.895)\" d=\"M50,0h620\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"y2zl zl crisp\" transform=\"translate(0,362.73499999999996)\" d=\"M50,0h620\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(50,270.025)\" clip-path=\"url(#clip043d55x2y2plot)\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter trace9653bf7f-f7b1-4d35-8fa9-769a62248e3a\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M240.04,85.81L241.72,111.1L242.56,95.75L243.4,113.88L245.92,110.88L247.59,136.79L248.43,120.69L249.27,122.73L251.79,139.62L252.63,126.06L253.47,128.36L254.31,126.95L255.15,120.99L257.67,142.19L258.51,113.65L259.35,83.75L260.18,90.51L261.02,64.79L263.54,43.11L264.38,15L265.22,34.47L266.06,37.32L266.9,52.8L269.42,52.14L270.26,35.09L271.1,49.57L272.77,39.43L275.29,41.44L276.13,31.4L276.97,34.64L277.81,27.48L278.65,26.47L281.17,21.34L282.01,24.31L282.85,15.59L284.52,45.25L287.04,38.25L287.88,39.61L288.72,38.31L289.56,39.06L290.4,31.51L292.92,43.35L293.76,41.73L294.6,40.11L295.44,44.8L296.27,43.36L299.63,44.5L301.31,43.14L302.15,42.13L307.19,61.26L308.02,59.48L311.38,67.93L312.22,70.9L313.06,75.81L313.9,74.25L317.26,78.5L318.1,75.54L318.94,79.73L319.78,79.17L322.29,76.81L323.13,82.64L324.81,98.15L325.65,108.22L328.17,102.02L329.01,92.72L329.85,94.33L330.69,90.8L334.04,66.93L334.88,69.56L335.72,68.56L336.56,61.14L337.4,58.44L339.92,74.92L340.76,79.73L341.6,103.76L342.44,104.45L343.28,108.93L347.47,96.92L348.31,90.96L349.15,100.77L351.67,90.71L352.51,83.32L353.35,86.95L354.19,101.82L355.03,101.32L358.38,103.91L359.22,99.29L360.06,92.98L360.9,98.68L363.42,89.37L364.26,82.39L365.1,83.37L365.94,88.16L366.78,87.45L369.29,97.42L370.13,101.16L370.97,111.61L371.81,114.85L372.65,124.24L375.17,128.71L376.01,125.53L376.85,121.41L377.69,127.28L381.04,130.03L381.88,142.89L382.72,147.8L383.56,147.96L384.4,147.69L386.92,142.13L390.28,160.68L392.79,155.85L394.47,176.9L395.31,180.92L396.15,189.98L398.67,188.16L401.19,134.05L402.03,162.26L404.54,162.68L405.38,157.51L406.22,155.32L407.06,142.79L407.9,141.3L410.42,138.56L411.26,138.29L412.1,139.99L412.94,139.91L413.78,134.91L416.3,131.95L417.13,122.59L417.97,111.83L418.81,129.09L419.65,127L422.17,115.4L423.01,105L423.85,104.6L424.69,103.36L425.53,104.11L428.88,105.88L429.72,114.53L430.56,115.12L431.4,118.55L433.92,132.29L434.76,116.13L435.6,121.99L436.44,116.96L437.28,121.79L439.8,120.32L440.64,117.21L441.47,100.41L443.15,88.96L445.67,89.15L446.51,85.53L447.35,74.51L449.03,51.77L451.55,61.04L452.39,60.43L453.22,48.03L454.9,75.68L457.42,63.85L459.94,101.57L460.78,98.06L463.3,102.89L464.14,76.53L464.97,71.97L465.81,76.64L466.65,68.24L469.17,65.45L470.01,69.82L470.85,64.7L471.69,55.77L472.53,61.18L475.05,62.09L475.89,72.77L477.56,107.56L478.4,109.43L480.92,107.8L481.76,111.73L482.6,111.29L483.44,89.6L484.28,88.42L486.8,76.98L487.64,99.93L488.48,91.15L489.31,95.15L490.15,91.24L492.67,78.27L493.51,79.34L494.35,83.32L495.19,80.16L496.03,87.76L498.55,92.92L499.39,82.16L500.23,85.34L501.9,72.02L504.42,71.35L505.26,70.82L506.1,59.27L507.78,29.54L511.14,49.57L513.65,64.2L516.17,70.15L517.01,71.5L517.85,75.53L518.69,63.25L519.53,74.95L522.05,80.4L522.89,79L523.73,82.8L524.57,96.45L525.4,96.87L527.92,104.61L528.76,89.95L529.6,103.7L530.44,116.41L531.28,114.44L533.8,129.29L534.64,164.84L535.48,149.74L536.32,158.05L539.67,154.81L540.51,148.81L542.19,121.07L543.03,119.14L545.55,122.21L546.39,119.41L547.23,119.14L548.07,114.61L548.91,107.53L551.42,106.2L552.26,107.34L553.1,82.69L553.94,76.95L554.78,90.27L557.3,73.14L558.14,86.99L559.82,110.26L560.66,115.05L564.01,78.81L564.85,75.27L566.53,55.53L569.05,63.07L569.89,60.68L570.73,65.01L571.57,62.43L572.41,49.87L574.92,48.09L575.76,68.86L576.6,68.37L578.28,70.49L580.8,63.4L581.64,63.6L582.48,44.94L583.32,45.54L584.16,28.13L586.67,53.63L587.51,66.3L589.19,64.62L590.03,72.49L592.55,70.86L595.07,52.8L595.91,100.8L598.42,96.29L599.26,107.87L600.1,121.85L600.94,113.08L601.78,122.64L604.3,136.24L606.82,118.77L607.66,100.68L610.17,101.46\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter tracea7a696b8-4ae9-4780-a44c-a2b62f63751e\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(264.38,15)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(507.78,29.54)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(584.16,28.13)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g><g class=\"text\"/></g><g class=\"trace scatter traceb706313b-601f-476d-a3d1-1612d7adcf1b\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point\" transform=\"translate(388.6,154.24)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/><path class=\"point\" transform=\"translate(534.64,164.84)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"483\" transform=\"translate(123.86,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Apr 2017</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"483\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(200.24,0)\">Jul 2017</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"483\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(277.45,0)\">Oct 2017</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"483\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(354.67,0)\">Jan 2018</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"483\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(430.21,0)\">Apr 2018</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"483\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(506.58,0)\">Jul 2018</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"483\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(583.8,0)\">Oct 2018</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"483\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(661.01,0)\">Jan 2019</text></g></g><g class=\"yaxislayer-above\"><g class=\"y2tick\"><text text-anchor=\"end\" x=\"49\" y=\"4.199999999999999\" transform=\"translate(0,419.575)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">−2</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"49\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,362.73499999999996)\">0</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"49\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,305.895)\">2</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-043d55\"><g class=\"clips\"/><clipPath id=\"legend043d55\"><rect width=\"314\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(356,11.579999999999991)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"314\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legend043d55)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">spread</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"83.1875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(85.6875,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">zscore</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(255, 127, 14); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"80.875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(169.0625,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Exit</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"65.21875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(236.78125,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Entry</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"74.640625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-x2title\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["vbt_short_signals, vbt_long_signals = pd.Series.vbt.signals.clean(\n", "    vbt_short_signals, vbt_long_signals, entry_first=False, broadcast_kwargs=dict(columns_from='keep'))\n", "\n", "def plot_spread_and_zscore(spread, zscore):\n", "    fig = vbt.make_subplots(rows=2, cols=1, shared_xaxes=True, vertical_spacing=0.05)\n", "    spread.vbt.plot(add_trace_kwargs=dict(row=1, col=1), fig=fig)\n", "    zscore.vbt.plot(add_trace_kwargs=dict(row=2, col=1), fig=fig)\n", "    vbt_short_signals.vbt.signals.plot_as_exit_markers(zscore, add_trace_kwargs=dict(row=2, col=1), fig=fig)\n", "    vbt_long_signals.vbt.signals.plot_as_entry_markers(zscore, add_trace_kwargs=dict(row=2, col=1), fig=fig)\n", "    fig.update_layout(height=500)\n", "    fig.add_shape(\n", "        type=\"rect\",\n", "        xref='paper',\n", "        yref='y2',\n", "        x0=0,\n", "        y0=UPPER,\n", "        x1=1,\n", "        y1=LOWER,\n", "        fillcolor=\"gray\",\n", "        opacity=0.2,\n", "        layer=\"below\",\n", "        line_width=0,\n", "    )\n", "    return fig\n", "    \n", "plot_spread_and_zscore(vbt_spread, vbt_zscore).show_svg()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# Assert equality of bt and vbt signal arrays\n", "pd.testing.assert_series_equal(\n", "    bt_short_signals[bt_short_signals], \n", "    vbt_short_signals[vbt_short_signals]\n", ")\n", "pd.testing.assert_series_equal(\n", "    bt_long_signals[bt_long_signals], \n", "    vbt_long_signals[vbt_long_signals]\n", ")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["symbol      PEP   KO\n", "2017-11-15 -0.1  0.1\n", "2018-04-12  0.1 -0.1\n", "2018-09-04 -0.1  0.1\n", "2018-10-03  0.1 -0.1\n", "2018-12-03 -0.1  0.1\n"]}], "source": ["# Build percentage order size\n", "symbol_cols = pd.Index([SYMBOL1, SYMBOL2], name='symbol')\n", "vbt_order_size = pd.DataFrame(index=bt_s1_ohlcv.index, columns=symbol_cols)\n", "vbt_order_size[SYMBOL1] = np.nan\n", "vbt_order_size[SYMBOL2] = np.nan\n", "vbt_order_size.loc[vbt_short_signals, SYMBOL1] = -ORDER_PCT1\n", "vbt_order_size.loc[vbt_long_signals, SYMBOL1] = ORDER_PCT1\n", "vbt_order_size.loc[vbt_short_signals, SYMBOL2] = ORDER_PCT2\n", "vbt_order_size.loc[vbt_long_signals, SYMBOL2] = -ORDER_PCT2\n", "\n", "# Execute at the next bar\n", "vbt_order_size = vbt_order_size.vbt.fshift(1)\n", "\n", "print(vbt_order_size[~vbt_order_size.isnull().any(axis=1)])"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# Simulate the portfolio\n", "vbt_close_price = pd.concat((bt_s1_ohlcv['close'], bt_s2_ohlcv['close']), axis=1, keys=symbol_cols)\n", "vbt_open_price = pd.concat((bt_s1_ohlcv['open'], bt_s2_ohlcv['open']), axis=1, keys=symbol_cols)\n", "\n", "def simulate_from_orders():\n", "    \"\"\"Simulate using `Portfolio.from_orders`.\"\"\"\n", "    return vbt.Portfolio.from_orders(\n", "        vbt_close_price,  # current close as reference price\n", "        size=vbt_order_size,  \n", "        price=vbt_open_price,  # current open as execution price\n", "        size_type='targetpercent', \n", "        val_price=vbt_close_price.vbt.fshift(1),  # previous close as group valuation price\n", "        init_cash=CASH,\n", "        fees=COMMPERC,\n", "        cash_sharing=True,  # share capital between assets in the same group\n", "        group_by=True,  # all columns belong to the same group\n", "        call_seq='auto',  # sell before buying\n", "        freq='d'  # index frequency for annualization\n", "    )\n", "\n", "vbt_pf = simulate_from_orders()"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Order Id Column  Timestamp        Size       Price        Fees  Side\n", "0         0    PEP 2017-11-15   96.698241  103.316229   49.952488  Sell\n", "1         1     KO 2017-11-15  238.385955   41.851497   49.884046   Buy\n", "2         2     KO 2018-04-12  490.647327   39.652521   97.277017  Sell\n", "3         3    PEP 2018-04-12  198.056953   98.739352   97.780076   Buy\n", "4         4    PEP 2018-09-04  198.781680  102.263047  101.640101  Sell\n", "5         5     KO 2018-09-04  498.975917   40.477607  100.986755   Buy\n", "6         6     KO 2018-10-03  482.284086   42.524342  102.544068  Sell\n", "7         7    PEP 2018-10-03  197.454665  100.702245   99.420640   Buy\n", "8         8    PEP 2018-12-03  189.202087  111.094282  105.096350  Sell\n", "9         9     KO 2018-12-03  451.211817   46.006429  103.793222   Buy\n"]}], "source": ["print(vbt_pf.orders.records_readable)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# Proof that both bt and vbt produce the same result\n", "pd.testing.assert_series_equal(bt_cash, vbt_pf.cash().rename('cash'))\n", "pd.testing.assert_series_equal(bt_value, vbt_pf.value().rename('value'))"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Start                         2017-01-03 00:00:00\n", "End                           2018-12-31 00:00:00\n", "Period                          502 days 00:00:00\n", "Start Value                              100000.0\n", "End Value                           100284.081822\n", "Total Return [%]                         0.284082\n", "Benchmark Return [%]                    16.631282\n", "Max Gross Exposure [%]                   0.915879\n", "Total Fees Paid                        908.374763\n", "Max Drawdown [%]                         1.030291\n", "Max Drawdown Duration           168 days 00:00:00\n", "Total Trades                                   10\n", "Total Closed Trades                             8\n", "Total Open Trades                               2\n", "Open Trade PnL                         149.652774\n", "Win Rate [%]                                 62.5\n", "Best Trade [%]                           9.267971\n", "Worst Trade [%]                         -9.229397\n", "Avg Winning Trade [%]                    3.967201\n", "Avg Losing Trade [%]                    -6.182852\n", "Avg Winning Trade Duration       56 days 19:12:00\n", "Avg Losing Trade Duration        80 days 16:00:00\n", "Profit Factor                            1.072464\n", "Expectancy                              16.803631\n", "Sharpe Ratio                             0.185035\n", "Calmar Ratio                             0.200403\n", "Omega Ratio                              1.036058\n", "Sortino Ratio                            0.266979\n", "Name: group, dtype: object\n"]}], "source": ["print(vbt_pf.stats())"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"750\" height=\"670\" style=\"\" viewBox=\"0 0 750 670\"><rect x=\"0\" y=\"0\" width=\"750\" height=\"670\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-86638d\"><g class=\"clips\"><clipPath id=\"clip86638dxyplot\" class=\"plotclip\"><rect width=\"659\" height=\"256.2313432835821\"/></clipPath><clipPath id=\"clip86638dx2y2plot\" class=\"plotclip\"><rect width=\"659\" height=\"256.2313432835821\"/></clipPath><clipPath class=\"axesclip\" id=\"clip86638dx\"><rect x=\"61\" y=\"0\" width=\"659\" height=\"670\"/></clipPath><clipPath class=\"axesclip\" id=\"clip86638dy\"><rect x=\"0\" y=\"73\" width=\"750\" height=\"256.2313432835821\"/></clipPath><clipPath class=\"axesclip\" id=\"clip86638dxy\"><rect x=\"61\" y=\"73\" width=\"659\" height=\"256.2313432835821\"/></clipPath><clipPath class=\"axesclip\" id=\"clip86638dy2\"><rect x=\"0\" y=\"361.7686567164179\" width=\"750\" height=\"256.2313432835821\"/></clipPath><clipPath class=\"axesclip\" id=\"clip86638dxy2\"><rect x=\"61\" y=\"361.7686567164179\" width=\"659\" height=\"256.2313432835821\"/></clipPath><clipPath class=\"axesclip\" id=\"clip86638dx2\"><rect x=\"61\" y=\"0\" width=\"659\" height=\"670\"/></clipPath><clipPath class=\"axesclip\" id=\"clip86638dx2y\"><rect x=\"61\" y=\"73\" width=\"659\" height=\"256.2313432835821\"/></clipPath><clipPath class=\"axesclip\" id=\"clip86638dx2y2\"><rect x=\"61\" y=\"361.7686567164179\" width=\"659\" height=\"256.2313432835821\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"61\" y=\"73\" width=\"659\" height=\"256.2313432835821\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/><rect class=\"bg\" x=\"61\" y=\"361.7686567164179\" width=\"659\" height=\"256.2313432835821\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(139.19,0)\" d=\"M0,73v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(220.04,0)\" d=\"M0,73v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(301.78,0)\" d=\"M0,73v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(383.52,0)\" d=\"M0,73v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(463.48,0)\" d=\"M0,73v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(544.3399999999999,0)\" d=\"M0,73v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(626.08,0)\" d=\"M0,73v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(707.82,0)\" d=\"M0,73v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,290.35)\" d=\"M61,0h659\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,244.78)\" d=\"M61,0h659\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,199.2)\" d=\"M61,0h659\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,153.63)\" d=\"M61,0h659\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,108.06)\" d=\"M61,0h659\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(61,73)\" clip-path=\"url(#clip86638dxyplot)\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter tracea18549c5-71c9-4ff7-9452-a6857d46ed63\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,202.97L0.89,201.38L1.78,202.5L2.67,203.69L5.33,212.47L6.22,224.35L7.11,225.63L8,225.39L8.88,227.7L12.44,222.6L13.33,218.21L14.22,221.24L15.1,214.22L17.77,208.08L18.66,202.5L19.55,203.29L20.44,207.04L21.32,212.31L23.99,210.63L24.88,209.91L25.77,216.06L27.54,199.31L30.21,203.61L31.1,195.32L31.99,194.68L33.76,191.41L36.43,188.06L37.32,184.87L38.2,186.38L39.09,175.3L39.98,175.06L44.42,165.01L45.31,163.97L46.2,161.66L48.87,166.12L49.76,157.27L50.64,156.44L52.42,149.78L55.09,157.25L55.97,159.74L56.86,159.9L57.75,162.15L58.64,157.57L62.19,159.58L63.08,145.36L63.97,145.12L64.86,143.11L67.52,145.04L68.41,140.06L69.3,138.05L70.19,139.98L71.08,137.25L73.74,139.58L74.63,134.6L75.52,134.76L77.3,139.34L79.96,139.34L80.85,137.57L81.74,138.7L82.63,141.59L83.52,141.35L86.18,138.05L87.07,140.38L87.96,125.28L88.85,132.75L92.4,127.61L93.29,119.9L94.18,125.2L95.07,125.44L95.96,126.73L98.62,119.58L99.51,120.87L100.4,127.53L101.29,133.07L102.18,127.93L104.84,135.97L105.73,135.88L106.62,139.9L107.51,130.5L108.39,128.42L111.06,128.42L111.95,132.43L112.84,131.47L113.73,129.86L114.61,130.91L117.28,122.63L118.17,123.84L119.06,124.24L119.95,121.91L120.83,124.64L123.5,116.53L124.39,113.72L125.28,105.61L127.05,90.75L130.61,89.95L131.5,92.66L132.38,89.75L133.27,86.19L135.94,84.49L136.83,85.95L137.71,91.2L138.6,100.58L139.49,100.18L143.05,97.1L143.93,88.61L144.82,89.91L145.71,92.74L148.38,89.26L149.27,83.92L150.15,88.21L151.04,98.48L151.93,91.93L154.6,90.64L155.48,100.18L156.37,96.62L157.26,106.32L158.15,103.82L160.82,104.22L162.59,105.35L163.48,106.73L164.37,103.65L167.92,117.97L168.81,109.64L169.7,115.7L170.59,108.34L174.14,107.7L175.03,105.03L175.92,103.65L176.81,94.6L179.47,98.64L180.36,96.05L182.14,88.86L183.03,94.76L185.69,94.76L186.58,100.34L187.47,102.85L188.36,97.27L189.25,96.7L191.91,91.93L194.58,96.62L195.47,95.57L198.13,84.81L199.02,75.68L199.91,74.62L200.8,80.37L201.69,86.75L204.35,81.42L206.13,89.26L207.02,97.91L207.91,100.9L210.57,103.41L212.35,100.05L213.24,95.33L214.12,94.43L217.68,94.19L219.46,85.07L220.34,100.94L223.01,95.65L223.9,101.27L225.67,107.87L226.56,102.49L229.23,101.35L230.12,105.42L232.78,126.92L235.45,126.19L236.34,127.25L237.23,133.03L238.11,128.63L239,130.34L241.67,149.15L242.56,149.07L243.44,147.36L244.33,138.32L245.22,138.73L247.89,141.9L251.44,120.65L254.11,112.91L255.88,126.11L256.77,120.24L257.66,128.87L260.33,128.22L262.1,141.41L262.99,136.04L263.88,137.1L266.55,144.83L267.43,140.11L268.32,140.93L269.21,141.58L270.1,140.19L272.76,148.01L273.65,138.16L274.54,125.7L275.43,129.93L276.32,119.59L278.98,109.09L279.87,95.08L280.76,100.46L281.65,99.4L282.54,103.88L285.2,102L286.09,92.72L286.98,100.62L288.76,93.94L291.42,94.35L292.31,86.86L293.2,86.29L294.09,82.31L298.53,73.54L299.42,62.8L300.31,80.59L301.2,81.9L303.86,75.18L304.75,75.1L306.53,70.01L307.42,60.17L310.08,69.77L310.97,65.17L311.86,61.81L312.75,68.04L313.63,65.26L317.19,62.88L318.08,59.52L318.97,59.11L319.85,54.43L323.41,69.68L324.3,72.23L325.18,67.47L326.07,64.68L328.74,70.26L329.63,69.52L330.52,74.44L331.4,79.36L332.29,75.26L335.85,71.32L336.74,60.5L337.62,61.57L338.51,52.38L341.18,41.4L342.07,43.12L343.84,46.4L344.73,39.35L347.4,56.48L348.29,54.68L349.17,51.32L350.06,51.32L353.62,99.2L354.5,106.58L355.39,107.07L356.28,134.54L357.17,126.09L359.84,119.95L360.72,118.22L361.61,143.07L362.5,127.82L363.39,127.08L366.94,140.03L367.83,152.17L369.61,138.39L372.27,128.06L373.16,128.47L374.05,137.98L374.94,144.63L375.83,137.03L379.38,132.57L380.27,130.43L382.05,108.12L384.71,106.39L385.6,99.37L386.49,107.55L388.27,115.56L390.93,126.13L391.82,129.93L392.71,139.51L393.6,146.12L394.49,160.9L397.15,155.45L398.04,148.6L398.93,135.88L399.82,136.13L403.37,153.47L404.26,144.05L406.04,124.4L406.93,134.89L410.48,134.06L411.37,140.01L412.26,143.06L413.14,135.22L415.81,129.77L416.7,138.44L419.36,191.22L422.03,190.39L423.81,202.2L424.7,184.78L425.58,197.58L428.25,203.94L430.03,234.58L430.91,231.53L431.8,220.04L434.47,226.32L436.25,243.42L437.13,234.25L438.02,232.93L440.69,235.24L441.58,238.13L442.46,234.91L443.35,228.88L444.24,232.27L448.68,207.99L449.57,210.22L450.46,209.14L454.02,202.62L454.9,196.59L455.79,202.02L456.68,202.02L459.35,203.11L460.23,202.35L461.12,197.19L462.01,194.44L462.9,183.35L465.57,177.35L466.45,166.76L467.34,160.18L468.23,161.34L469.12,140.67L471.78,159.26L472.67,154.42L473.56,155.67L475.34,134.34L478,129.17L478.89,137L479.78,135.17L481.56,130.17L484.22,124.67L485.11,130.75L486.89,124.5L487.78,124.42L490.44,139.42L491.33,96.66L492.22,99.58L493.11,107.99L494,98.32L496.66,96.07L497.55,80.07L498.44,80.49L499.33,72.65L500.22,70.65L502.88,84.15L504.66,77.32L505.55,79.07L506.44,85.07L509.1,85.9L509.99,79.07L510.88,90.66L512.66,68.23L515.32,59.23L517.1,91.66L517.99,89.91L518.87,96.82L521.54,95.57L522.43,94.91L523.32,91.99L525.09,79.4L527.76,80.4L530.42,104.66L531.31,102.83L533.98,100.66L534.87,111L535.76,103.66L536.64,104.41L537.53,103.99L541.09,112.41L542.86,83.75L543.75,90.13L546.42,83.16L547.31,85.18L548.19,80.81L549.08,71.22L549.97,74.75L552.64,67.78L554.41,81.48L555.3,69.29L556.19,71.9L558.86,94.09L560.63,100.56L561.52,104.34L562.41,98.04L565.08,107.2L566.85,134.01L567.74,141.66L568.63,142.66L571.29,128.38L572.18,128.96L573.07,135.52L573.96,154.68L574.85,152.83L577.51,149.89L578.4,135.1L579.29,127.37L580.18,130.56L581.07,110.73L583.73,118.21L584.62,112.07L585.51,88.29L586.4,89.21L587.29,109.38L590.84,81.48L591.73,93.16L593.51,103.33L596.17,76.27L597.95,66.01L598.84,62.23L599.73,50.3L602.39,54.58L603.28,53.15L604.17,57.27L605.06,56.01L605.95,42.98L608.61,37.44L609.5,62.74L610.39,68.79L612.17,67.69L614.83,63.91L615.72,59.04L616.61,41.72L617.5,43.66L618.38,12.81L621.05,37.69L621.94,47.61L623.72,47.88L624.6,56.52L627.27,53.38L628.16,44.07L629.05,46.52L629.93,35.09L630.82,72.36L633.49,81.51L634.38,90.24L635.27,105.48L636.15,104.04L637.04,110.74L639.71,139.45L643.26,102.77L645.93,101.76\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter tracea77fdd3e-a655-4123-a236-e5f0ff66d335\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point plotly-customdata\" transform=\"translate(412.26,137.69)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/><path class=\"point plotly-customdata\" transform=\"translate(566.85,119.8)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g><g class=\"text\"/></g><g class=\"trace scatter trace56007203-14ab-46a1-805f-07fddf4733d0\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point plotly-customdata\" transform=\"translate(280.76,95.98)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/><path class=\"point plotly-customdata\" transform=\"translate(541.09,105.58)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/><path class=\"point plotly-customdata\" transform=\"translate(621.05,25.08)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"/><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"60\" y=\"4.199999999999999\" transform=\"translate(0,290.35)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">90</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"60\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,244.78)\">95</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"60\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,199.2)\">100</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"60\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,153.63)\">105</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"60\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,108.06)\">110</text></g></g><g class=\"overaxes-above\"/></g><g class=\"subplot x2y2\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x2\"><path class=\"x2grid crisp\" transform=\"translate(139.19,0)\" d=\"M0,361.7686567164179v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(220.04,0)\" d=\"M0,361.7686567164179v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(301.78,0)\" d=\"M0,361.7686567164179v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(383.52,0)\" d=\"M0,361.7686567164179v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(463.48,0)\" d=\"M0,361.7686567164179v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(544.3399999999999,0)\" d=\"M0,361.7686567164179v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(626.08,0)\" d=\"M0,361.7686567164179v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"x2grid crisp\" transform=\"translate(707.82,0)\" d=\"M0,361.7686567164179v256.2313432835821\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,583.068656716418)\" d=\"M61,0h659\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,543.338656716418)\" d=\"M61,0h659\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,503.60865671641795)\" d=\"M61,0h659\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,463.87865671641794)\" d=\"M61,0h659\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,424.1486567164179)\" d=\"M61,0h659\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,384.4186567164179)\" d=\"M61,0h659\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"/><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(61,361.7686567164179)\" clip-path=\"url(#clip86638dx2y2plot)\"><g class=\"scatterlayer mlayer\"><g class=\"trace scatter traceb5a763c9-5c48-4f7c-8a44-8cf60481dec6\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"><path class=\"js-line\" d=\"M0,220.11L0.89,222.68L1.78,220.97L2.67,221.14L5.33,228.34L6.22,233.14L7.11,232.97L8.88,235.88L12.44,230.05L13.33,228.85L14.22,231.42L15.1,228.34L17.77,226.45L19.55,214.63L20.44,219.94L21.32,226.11L23.99,227.31L24.88,224.05L25.77,229.37L27.54,224.57L30.21,224.23L31.99,216.34L32.87,229.54L33.76,241.02L36.43,240.34L38.2,243.42L39.09,230.4L39.98,229.88L43.54,225.94L46.2,220.46L48.87,222.34L51.53,208.63L52.42,208.46L55.97,216.86L56.86,216.86L57.75,216.17L58.64,211.72L61.31,209.81L62.19,210.85L63.97,206.01L64.86,209.81L67.52,207.22L68.41,201.69L71.08,208.26L73.74,204.8L74.63,202.9L75.52,203.07L76.41,199.96L77.3,202.73L79.96,203.24L80.85,198.58L81.74,200.48L82.63,198.75L83.52,199.79L86.18,198.4L87.07,198.92L87.96,194.08L88.85,196.68L92.4,191.83L93.29,184.75L94.18,189.07L95.96,191.83L98.62,188.2L99.51,191.14L100.4,188.9L101.29,192.87L102.18,190.45L104.84,189.24L105.73,186.3L106.62,187.51L107.51,181.63L108.39,181.12L111.06,177.66L111.95,184.23L112.84,183.19L113.73,181.46L114.61,182.84L117.28,180.42L118.17,178.35L119.06,176.97L119.95,179.21L123.5,172.64L124.39,169.01L126.16,151.38L127.05,151.72L131.5,150.34L132.38,144.81L133.27,143.08L135.94,141.35L136.83,141.52L137.71,149.65L138.6,156.22L139.49,152.94L142.16,152.76L143.05,151.54L143.93,146.84L144.82,147.71L145.71,146.66L148.38,145.44L149.27,141.43L150.15,148.23L151.04,150.84L151.93,147.71L154.6,144.57L155.48,151.19L156.37,146.49L157.26,155.03L158.15,154.68L160.82,156.25L162.59,155.2L163.48,162.52L167.92,163.74L168.81,160.61L169.7,162L170.59,157.64L173.25,156.77L174.14,157.82L175.03,155.03L175.92,155.2L176.81,151.54L179.47,154.85L182.14,132.54L183.03,134.46L185.69,137.42L186.58,139.86L187.47,141.78L188.36,140.39L189.25,143.35L191.91,140.91L193.69,141.78L194.58,139.17L195.47,141.78L198.13,138.12L199.02,131.32L199.91,131.15L201.69,140.39L204.35,140.21L207.02,144.92L207.91,142.13L210.57,144.74L211.46,144.22L212.35,145.27L213.24,142.48L214.12,138.47L217.68,136.03L218.57,135.33L219.46,129.75L220.34,129.41L223.01,125.57L224.79,119.47L225.67,126.32L226.56,125.09L229.23,126.32L231.01,132.12L231.89,138.8L235.45,133.7L236.34,135.81L237.23,152.15L238.11,147.41L239,145.65L241.67,149.34L243.44,137.04L244.33,136.69L247.89,138.62L248.78,130.54L249.66,126.5L250.55,126.32L251.44,125.09L254.11,117.36L255.88,121.23L256.77,117.89L257.66,121.58L260.33,122.63L262.1,127.38L262.99,124.21L263.88,127.02L266.55,130.71L267.43,128.61L268.32,131.77L270.1,128.78L272.76,137.57L274.54,125.09L275.43,124.21L276.32,118.77L278.98,115.6L279.87,103.13L280.76,114.02L281.65,118.59L282.54,133.35L285.2,137.74L286.09,132.12L286.98,131.07L288.76,130.36L291.42,130.01L292.31,131.24L293.2,137.04L294.09,125.7L294.98,122.16L297.64,117.55L298.53,117.02L299.42,113.66L301.2,133.85L303.86,133.5L304.75,134.2L305.64,123.4L307.42,118.26L310.08,122.87L310.97,119.33L311.86,120.21L312.75,128.71L313.63,128.89L317.19,124.99L318.08,122.87L318.97,126.59L319.85,123.75L324.3,131.55L325.18,120.21L328.74,121.63L329.63,117.55L331.4,120.92L332.29,118.97L335.85,112.24L336.74,107.1L337.62,106.04L338.51,101.08L342.07,95.94L342.95,89.21L343.84,89.04L344.73,76.81L348.29,96.65L349.17,93.46L350.06,95.94L350.95,108.7L354.5,145.19L355.39,147.14L356.28,173L357.17,172.47L360.72,153.69L361.61,155.28L363.39,139.7L366.94,157.23L367.83,168.75L368.72,165.56L369.61,156.35L372.27,156.52L374.05,170.87L374.94,167.15L375.83,162.01L378.49,159L379.38,158.3L380.27,160.24L382.05,142.53L384.71,147.67L385.6,146.96L386.49,154.11L388.27,159.82L390.93,163.4L392.71,168.05L393.6,172.33L394.49,180.02L397.15,173.58L398.04,170.01L398.93,162.33L399.82,160.36L403.37,173.94L406.04,143.03L406.93,151.6L409.59,153.21L410.48,150.35L412.26,149.64L413.14,141.06L415.81,138.02L416.7,134.45L419.36,154.82L422.03,150.53L423.81,178.23L424.7,172.51L425.58,162.51L428.25,164.29L430.03,184.84L430.91,187.88L431.8,179.48L434.47,183.41L435.36,189.31L436.25,189.85L437.13,182.7L438.02,183.41L440.69,185.2L441.58,190.92L442.46,193.96L443.35,180.55L444.24,182.7L446.91,178.59L447.8,181.45L448.68,178.77L449.57,180.2L450.46,178.77L454.02,173.76L454.9,165.72L455.79,168.05L456.68,165.9L459.35,159.29L460.23,166.08L461.12,159.82L462.01,160.18L462.9,151.07L465.57,147.85L466.45,142.85L467.34,146.96L468.23,150.74L469.12,141.01L471.78,151.29L472.67,150.38L474.45,159.58L475.34,156.69L478,150.74L478.89,155.25L479.78,159.94L480.67,152.73L481.56,145.7L484.22,147.68L486.89,131.27L487.78,131.63L490.44,135.96L491.33,125.68L493.11,131.09L494,129.83L496.66,130.01L497.55,120.64L498.44,122.98L499.33,123.16L500.22,120.1L502.88,126.04L503.77,120.46L504.66,105.49L505.55,102.79L506.44,103.33L509.1,102.97L509.99,95.76L511.77,103.87L512.66,95.94L515.32,95.4L516.21,98.1L517.1,104.41L517.99,100.8L518.87,105.67L521.54,110.18L522.43,109.1L523.32,105.67L524.21,103.15L525.09,96.3L527.76,93.95L528.65,103.15L530.42,112.52L531.31,113.79L533.98,112.7L534.87,118.47L535.76,119.19L536.64,126.04L537.53,132.9L541.09,129.47L542.86,111.26L543.75,112.16L546.42,106.03L547.31,106.75L548.19,102.79L549.08,103.15L549.97,100.24L552.64,94.24L553.53,101.33L554.41,100.79L555.3,88.42L556.19,89.51L558.86,98.79L559.74,105.88L562.41,96.61L565.08,95.88L565.96,90.79L566.85,102.42L567.74,102.79L568.63,102.24L571.29,91.33L572.18,92.97L573.96,124.79L574.85,124.06L577.51,119.88L578.4,109.33L579.29,102.61L580.18,107.15L581.07,94.06L583.73,100.61L585.51,86.79L586.4,90.79L587.29,101.51L589.95,91.7L591.73,65.88L592.62,68.42L597.95,38.79L598.84,39.51L599.73,33.15L602.39,29.69L603.28,29.88L604.17,31.69L605.06,32.06L605.95,24.24L608.61,18.06L609.5,38.6L610.39,50.42L612.17,45.15L614.83,47.88L615.72,38.79L616.61,32.6L617.5,38.83L618.38,12.81L621.05,25.82L623.72,31.5L624.6,36.82L627.27,34.07L628.16,28.57L629.05,34.44L629.93,29.85L630.82,32.24L633.49,50.75L634.38,50.93L635.27,58.63L636.15,65.22L637.04,64.67L639.71,94.18L641.49,76.22L642.37,65.41L643.26,71.46L645.93,68.71\" style=\"vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;\"/></g><g class=\"points\"/><g class=\"text\"/></g><g class=\"trace scatter tracefae22444-3927-4f0b-8204-dc34626490f4\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point plotly-customdata\" transform=\"translate(280.76,105.06)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/><path class=\"point plotly-customdata\" transform=\"translate(541.09,132.36)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/><path class=\"point plotly-customdata\" transform=\"translate(621.05,22.52)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g><g class=\"text\"/></g><g class=\"trace scatter trace292a007d-db62-409c-8b12-576095a2ea56\" style=\"stroke-miterlimit: 2; opacity: 1;\"><g class=\"fills\"/><g class=\"errorbars\"/><g class=\"lines\"/><g class=\"points\"><path class=\"point plotly-customdata\" transform=\"translate(412.26,148.75)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/><path class=\"point plotly-customdata\" transform=\"translate(566.85,91.7)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g><g class=\"text\"/></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"631\" transform=\"translate(139.19,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">Apr 2017</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"631\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(220.04,0)\">Jul 2017</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"631\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(301.78,0)\">Oct 2017</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"631\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(383.52,0)\">Jan 2018</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"631\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(463.48,0)\">Apr 2018</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"631\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(544.3399999999999,0)\">Jul 2018</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"631\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(626.08,0)\">Oct 2018</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"631\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(707.82,0)\">Jan 2019</text></g></g><g class=\"yaxislayer-above\"><g class=\"y2tick\"><text text-anchor=\"end\" x=\"60\" y=\"4.199999999999999\" transform=\"translate(0,583.068656716418)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">36</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"60\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,543.338656716418)\">38</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"60\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,503.60865671641795)\">40</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"60\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,463.87865671641794)\">42</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"60\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,424.1486567164179)\">44</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"60\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,384.4186567164179)\">46</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-86638d\"><g class=\"clips\"/><clipPath id=\"legend86638d\"><rect width=\"213\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(507,11.462686567164205)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"213\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legend86638d)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Close</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"><path class=\"js-line\" d=\"M5,0h30\" style=\"fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;\"/></g><g class=\"legendsymbols\"><g class=\"legendpoints\"/></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"74.859375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(77.359375,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Buy</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,2H4.62L0,-4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"65.421875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(145.28125,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Sell</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"scatterpts\" transform=\"translate(20,0)\" d=\"M-4.62,-2H4.62L0,4Z\" style=\"opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"64.4375\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-x2title\"><text class=\"x2title\" x=\"390.5\" y=\"658.3\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Time</text></g><g class=\"g-ytitle\" transform=\"translate(1.697265625,0)\"><text class=\"ytitle\" transform=\"rotate(-90,12.309375000000003,201.11567164179104)\" x=\"12.309375000000003\" y=\"201.11567164179104\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Price</text></g><g class=\"g-y2title\"><text class=\"y2title\" transform=\"rotate(-90,19.934375000000003,489.88432835820896)\" x=\"19.934375000000003\" y=\"489.88432835820896\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Price</text></g><g class=\"annotation\" data-index=\"0\" style=\"opacity: 1;\"><g class=\"annotation-text-g\" transform=\"rotate(0,390.5,61)\"><g class=\"cursor-pointer\" transform=\"translate(337,49)\"><rect class=\"bg\" x=\"0.5\" y=\"0.5\" width=\"107\" height=\"23\" style=\"stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><text class=\"annotation-text\" text-anchor=\"middle\" x=\"53.828125\" y=\"18\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Orders (PEP)</text></g></g></g><g class=\"annotation\" data-index=\"1\" style=\"opacity: 1;\"><g class=\"annotation-text-g\" transform=\"rotate(0,390.5,349.7686567164179)\"><g class=\"cursor-pointer\" transform=\"translate(340,338)\"><rect class=\"bg\" x=\"0.5\" y=\"0.5\" width=\"101\" height=\"23\" style=\"stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><text class=\"annotation-text\" text-anchor=\"middle\" x=\"50.96875\" y=\"18\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Orders (KO)</text></g></g></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot portfolio\n", "from functools import partial\n", "\n", "def plot_orders(portfolio, column=None, add_trace_kwargs=None, fig=None):\n", "    portfolio.orders.plot(column=column, add_trace_kwargs=add_trace_kwargs, fig=fig)\n", "\n", "vbt_pf.plot(subplots=[\n", "    ('symbol1_orders', dict(\n", "        title=f\"Orders ({SYMBOL1})\",\n", "        yaxis_title=\"Price\",\n", "        check_is_not_grouped=False,\n", "        plot_func=partial(plot_orders, column=SYMBOL1),\n", "        pass_column=False\n", "    )),\n", "    ('symbol2_orders', dict(\n", "        title=f\"Orders ({SYMBOL2})\",\n", "        yaxis_title=\"Price\",\n", "        check_is_not_grouped=False,\n", "        plot_func=partial(plot_orders, column=SYMBOL2),\n", "        pass_column=False\n", "    ))\n", "]).show_svg()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.72 ms ± 15.9 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["# How fast is vbt?\n", "%timeit simulate_from_orders()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["While Portfolio.from_orders is a very convenient and optimized function for simulating portfolios, it requires some prior steps to produce the size array. In the example above, we needed to manually run the calculation of the spread z-score, generate the signals from the z-score, build the size array from the signals, and make sure that all arrays are perfectly aligned. All these steps must be repeated and adapted accordingly once there is more than one hyperparameter combination to test.\n", "\n", "Nevertheless, dividing the pipeline into clearly separated backtesting steps helps us to analyze each step thoroughly and actually does wonders for strategy development and debugging."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Using Portfolio.from_order_func"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Portfolio.from_order_func follows a different (self-contained) approach where as much steps as possible should be defined in the simulation function itself. It sequentially processes timestamps one by one and executes orders based on the logic the user defined rather than parses this logic from some arrays. While this makes order execution less transparent as you cannot analyze each piece of data on the fly anymore (sadly, no pandas and plotting within Numba), it has one big advantage over other vectorized methods: event-driven order processing. This gives best flexibility (you can write any logic), security (less probability of exposing yourself to a look-ahead bias among other biases), and performance (you're traversing the data only once). This method is the most similar one compared to backtrader."]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["from vectorbt.portfolio import nb as portfolio_nb\n", "from vectorbt.base.reshape_fns import flex_select_auto_nb\n", "from vectorbt.portfolio.enums import SizeType, Direction\n", "from collections import namedtuple\n", "\n", "Memory = namedtuple(\"Memory\", ('spread', 'zscore', 'status'))\n", "Params = namedtuple(\"Params\", ('period', 'upper', 'lower', 'order_pct1', 'order_pct2'))\n", "\n", "@njit\n", "def pre_group_func_nb(c, _period, _upper, _lower, _order_pct1, _order_pct2):\n", "    \"\"\"Prepare the current group (= pair of columns).\"\"\"\n", "    assert c.group_len == 2\n", "    \n", "    # In contrast to bt, we don't have a class instance that we could use to store arrays,\n", "    # so let's create a namedtuple acting as a container for our arrays\n", "    # ( you could also pass each array as a standalone object, but a single object is more convenient)\n", "    spread = np.full(c.target_shape[0], np.nan, dtype=np.float64)\n", "    zscore = np.full(c.target_shape[0], np.nan, dtype=np.float64)\n", "    \n", "    # Note that namedtuples aren't mutable, you can't simply assign a value,\n", "    # thus make status variable an array of one element for an easy assignment\n", "    status = np.full(1, 0, dtype=np.int64)\n", "    memory = Memory(spread, zscore, status)\n", "    \n", "    # Treat each param as an array with value per group, and select the combination of params for this group\n", "    period = flex_select_auto_nb(np.asarray(_period), 0, c.group, True)\n", "    upper = flex_select_auto_nb(np.asarray(_upper), 0, c.group, True)\n", "    lower = flex_select_auto_nb(np.asarray(_lower), 0, c.group, True)\n", "    order_pct1 = flex_select_auto_nb(np.asarray(_order_pct1), 0, c.group, True)\n", "    order_pct2 = flex_select_auto_nb(np.asarray(_order_pct2), 0, c.group, True)\n", "    \n", "    # Put all params into a container (again, this is optional)\n", "    params = Params(period, upper, lower, order_pct1, order_pct2)\n", "    \n", "    # Create an array that will store our two target percentages used by order_func_nb\n", "    # we do it here instead of in pre_segment_func_nb to initialize the array once, instead of in each row\n", "    size = np.empty(c.group_len, dtype=np.float64)\n", "    \n", "    # The returned tuple is passed as arguments to the function below\n", "    return (memory, params, size)\n", "    \n", "\n", "@njit\n", "def pre_segment_func_nb(c, memory, params, size, mode):\n", "    \"\"\"Prepare the current segment (= row within group).\"\"\"\n", "    \n", "    # We want to perform calculations once we reach full window size\n", "    if c.i < params.period - 1:\n", "        size[0] = np.nan  # size of nan means no order\n", "        size[1] = np.nan\n", "        return (size,)\n", "    \n", "    # z-core is calculated using a window (=period) of spread values\n", "    # This window can be specified as a slice\n", "    window_slice = slice(max(0, c.i + 1 - params.period), c.i + 1)\n", "    \n", "    # Here comes the same as in rolling_ols_zscore_nb\n", "    if mode == 'OLS':\n", "        a = c.close[window_slice, c.from_col]\n", "        b = c.close[window_slice, c.from_col + 1]\n", "        memory.spread[c.i] = ols_spread_nb(a, b)\n", "    elif mode == 'log_return':\n", "        logret_a = np.log(c.close[c.i, c.from_col] / c.close[c.i - 1, c.from_col])\n", "        logret_b = np.log(c.close[c.i, c.from_col + 1] / c.close[c.i - 1, c.from_col + 1])\n", "        memory.spread[c.i] = logret_a - logret_b\n", "    else:\n", "        raise ValueError(\"Unknown mode\")\n", "    spread_mean = np.mean(memory.spread[window_slice])\n", "    spread_std = np.std(memory.spread[window_slice])\n", "    memory.zscore[c.i] = (memory.spread[c.i] - spread_mean) / spread_std\n", "    \n", "    # Check if any bound is crossed\n", "    # Since zscore is calculated using close, use zscore of the previous step\n", "    # This way we are executing signals defined at the previous bar\n", "    # Same logic as in PairTradingStrategy\n", "    if memory.zscore[c.i - 1] > params.upper and memory.status[0] != 1:\n", "        size[0] = -params.order_pct1\n", "        size[1] = params.order_pct2\n", "        \n", "        # Here we specify the order of execution\n", "        # call_seq_now defines order for the current group (2 elements)\n", "        c.call_seq_now[0] = 0\n", "        c.call_seq_now[1] = 1\n", "        memory.status[0] = 1\n", "    elif memory.zscore[c.i - 1] < params.lower and memory.status[0] != 2:\n", "        size[0] = params.order_pct1\n", "        size[1] = -params.order_pct2\n", "        c.call_seq_now[0] = 1  # execute the second order first to release funds early\n", "        c.call_seq_now[1] = 0\n", "        memory.status[0] = 2\n", "    else:\n", "        size[0] = np.nan\n", "        size[1] = np.nan\n", "        \n", "    # Group value is converted to shares using previous close, just like in bt\n", "    # Note that last_val_price contains valuation price of all columns, not just the current pair\n", "    c.last_val_price[c.from_col] = c.close[c.i - 1, c.from_col]\n", "    c.last_val_price[c.from_col + 1] = c.close[c.i - 1, c.from_col + 1]\n", "        \n", "    return (size,)\n", "\n", "@njit\n", "def order_func_nb(c, size, price, commperc):\n", "    \"\"\"Place an order (= element within group and row).\"\"\"\n", "    \n", "    # Get column index within group (if group starts at column 58 and current column is 59, \n", "    # the column within group is 1, which can be used to get size)\n", "    group_col = c.col - c.from_col\n", "    return portfolio_nb.order_nb(\n", "        size=size[group_col], \n", "        price=price[c.i, c.col],\n", "        size_type=SizeType.TargetPercent,\n", "        fees=commperc\n", "    )"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["def simulate_from_order_func():\n", "    \"\"\"Simulate using `Portfolio.from_order_func`.\"\"\"\n", "    return vbt.Portfolio.from_order_func(\n", "        vbt_close_price,\n", "        order_func_nb, \n", "        vbt_open_price.values, COMMPERC,  # *args for order_func_nb\n", "        pre_group_func_nb=pre_group_func_nb, \n", "        pre_group_args=(PERIOD, UPPER, LOWER, ORDER_PCT1, ORDER_PCT2),\n", "        pre_segment_func_nb=pre_segment_func_nb, \n", "        pre_segment_args=(MODE,),\n", "        fill_pos_record=False,  # a bit faster\n", "        init_cash=CASH,\n", "        cash_sharing=True, \n", "        group_by=True,\n", "        freq='d'\n", "    )\n", "\n", "vbt_pf2 = simulate_from_order_func()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Order Id Column  Timestamp        Size       Price        Fees  Side\n", "0         0    PEP 2017-11-15   96.698241  103.316229   49.952488  Sell\n", "1         1     KO 2017-11-15  238.385955   41.851497   49.884046   Buy\n", "2         2     KO 2018-04-12  490.647327   39.652521   97.277017  Sell\n", "3         3    PEP 2018-04-12  198.056953   98.739352   97.780076   Buy\n", "4         4    PEP 2018-09-04  198.781680  102.263047  101.640101  Sell\n", "5         5     KO 2018-09-04  498.975917   40.477607  100.986755   Buy\n", "6         6     KO 2018-10-03  482.284086   42.524342  102.544068  Sell\n", "7         7    PEP 2018-10-03  197.454665  100.702245   99.420640   Buy\n", "8         8    PEP 2018-12-03  189.202087  111.094282  105.096350  Sell\n", "9         9     KO 2018-12-03  451.211817   46.006429  103.793222   Buy\n"]}], "source": ["print(vbt_pf2.orders.records_readable)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["# Proof that both bt and vbt produce the same result\n", "pd.testing.assert_series_equal(bt_cash, vbt_pf2.cash().rename('cash'))\n", "pd.testing.assert_series_equal(bt_value, vbt_pf2.value().rename('value'))"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.4 ms ± 17.3 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["# How fast is vbt?\n", "%timeit simulate_from_order_func()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Numba paradise (or hell?) - fastest"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["def simulate_nb_from_order_func():\n", "    \"\"\"Simulate using `simulate_nb`.\"\"\"\n", "    # iterate over 502 rows and 2 columns, each element is a potential order\n", "    target_shape = vbt_close_price.shape\n", "    \n", "    # number of columns in the group - exactly two\n", "    group_lens = np.array([2])\n", "    \n", "    # build default call sequence (orders are executed from the left to the right column)\n", "    call_seq = portfolio_nb.build_call_seq(target_shape, group_lens)\n", "    \n", "    # initial cash per group\n", "    init_cash = np.array([CASH], dtype=np.float64)\n", "    \n", "    order_records, log_records = portfolio_nb.simulate_nb(\n", "        target_shape=target_shape, \n", "        group_lens=group_lens,\n", "        init_cash=init_cash,\n", "        cash_sharing=True,\n", "        call_seq=call_seq,  \n", "        segment_mask=np.full(target_shape, True),  # used for disabling some segments\n", "        pre_group_func_nb=pre_group_func_nb, \n", "        pre_group_args=(PERIOD, UPPER, LOWER, ORDER_PCT1, ORDER_PCT2),\n", "        pre_segment_func_nb=pre_segment_func_nb, \n", "        pre_segment_args=(MODE,),\n", "        order_func_nb=order_func_nb, \n", "        order_args=(vbt_open_price.values, COMMPERC),\n", "        close=vbt_close_price.values,  # used for target percentage, but we override the valuation price\n", "        fill_pos_record=False\n", "    )\n", "    \n", "    return target_shape, group_lens, call_seq, init_cash, order_records, log_records\n", "\n", "target_shape, group_lens, call_seq, init_cash, order_records, log_records = simulate_nb_from_order_func()"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Order Id Column  Timestamp        Size       Price        Fees  Side\n", "0         0    PEP 2017-11-15   96.698241  103.316229   49.952488  Sell\n", "1         1     KO 2017-11-15  238.385955   41.851497   49.884046   Buy\n", "2         2     KO 2018-04-12  490.647327   39.652521   97.277017  Sell\n", "3         3    PEP 2018-04-12  198.056953   98.739352   97.780076   Buy\n", "4         4    PEP 2018-09-04  198.781680  102.263047  101.640101  Sell\n", "5         5     KO 2018-09-04  498.975917   40.477607  100.986755   Buy\n", "6         6     KO 2018-10-03  482.284086   42.524342  102.544068  Sell\n", "7         7    PEP 2018-10-03  197.454665  100.702245   99.420640   Buy\n", "8         8    PEP 2018-12-03  189.202087  111.094282  105.096350  Sell\n", "9         9     KO 2018-12-03  451.211817   46.006429  103.793222   Buy\n"]}], "source": ["# Print order records in a readable format\n", "print(vbt.Orders(vbt_close_price.vbt.wrapper, order_records, vbt_close_price).records_readable)"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["# Proof that both bt and vbt produce the same cash\n", "from vectorbt.records import nb as records_nb\n", "\n", "col_map = records_nb.col_map_nb(order_records['col'], target_shape[1])\n", "cash_flow = portfolio_nb.cash_flow_nb(target_shape, order_records, col_map, False)\n", "cash_flow_grouped = portfolio_nb.cash_flow_grouped_nb(cash_flow, group_lens)\n", "cash_grouped = portfolio_nb.cash_grouped_nb(target_shape, cash_flow_grouped, group_lens, init_cash)\n", "\n", "pd.testing.assert_series_equal(bt_cash, bt_cash.vbt.wrapper.wrap(cash_grouped))"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["# Proof that both bt and vbt produce the same value\n", "asset_flow = portfolio_nb.asset_flow_nb(target_shape, order_records, col_map, Direction.Both)\n", "assets = portfolio_nb.assets_nb(asset_flow)\n", "asset_value = portfolio_nb.asset_value_nb(vbt_close_price.values, assets)\n", "asset_value_grouped = portfolio_nb.asset_value_grouped_nb(asset_value, group_lens)\n", "value = portfolio_nb.value_nb(cash_grouped, asset_value_grouped)\n", "\n", "pd.testing.assert_series_equal(bt_value, bt_value.vbt.wrapper.wrap(value))"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Start                         2017-01-03 00:00:00\n", "End                           2018-12-31 00:00:00\n", "Period                          502 days 00:00:00\n", "Start Value                              100000.0\n", "End Value                           100284.081822\n", "Total Return [%]                         0.284082\n", "Benchmark Return [%]                    16.631282\n", "Max Gross Exposure [%]                   0.915879\n", "Total Fees Paid                        908.374763\n", "Max Drawdown [%]                         1.030291\n", "Max Drawdown Duration           168 days 00:00:00\n", "Total Trades                                   10\n", "Total Closed Trades                             8\n", "Total Open Trades                               2\n", "Open Trade PnL                         149.652774\n", "Win Rate [%]                                 62.5\n", "Best Trade [%]                           9.267971\n", "Worst Trade [%]                         -9.229397\n", "Avg Winning Trade [%]                    3.967201\n", "Avg Losing Trade [%]                    -6.182852\n", "Avg Winning Trade Duration       56 days 19:12:00\n", "Avg Losing Trade Duration        80 days 16:00:00\n", "Profit Factor                            1.072464\n", "Expectancy                              16.803631\n", "Sharpe Ratio                             0.185035\n", "Calmar Ratio                             0.200403\n", "Omega Ratio                              1.036058\n", "Sortino Ratio                            0.266979\n", "Name: group, dtype: object\n"]}], "source": ["# To produce more complex metrics such as stats, it's advisable to use Portfolio,\n", "# which can be easily constructed from the arguments and outputs of simulate_nb\n", "vbt_pf3 = vbt.Portfolio(\n", "    wrapper=vbt_close_price.vbt(freq='d', group_by=True).wrapper, \n", "    close=vbt_close_price, \n", "    order_records=order_records, \n", "    log_records=log_records, \n", "    init_cash=init_cash,\n", "    cash_sharing=True, \n", "    call_seq=call_seq\n", ")\n", "\n", "print(vbt_pf3.stats())"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.3 ms ± 9.23 µs per loop (mean ± std. dev. of 7 runs, 100 loops each)\n"]}], "source": ["# How fast is vbt?\n", "%timeit simulate_nb_from_order_func()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As you can see, writing Numba isn't straightforward and requires at least intermediate knowledge of NumPy. That's why Portfolio.from_orders and other class methods based on arrays are usually a good starting point."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Multiple parameters"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, why waste all energy to port a strategy to vectorbt? Right, for hyperparameter optimization.\n", "\n", "*The example below is just for demo purposes, usually brute-forcing many combinations on a single data sample easily leads to overfitting.*"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["periods = np.arange(10, 105, 5)\n", "uppers = np.arange(1.5, 2.2, 0.1)\n", "lowers = -1 * np.arange(1.5, 2.2, 0.1)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["def simulate_mult_from_order_func(periods, uppers, lowers):\n", "    \"\"\"Simulate multiple parameter combinations using `Portfolio.from_order_func`.\"\"\"\n", "    # Build param grid\n", "    param_product = vbt.utils.params.create_param_product([periods, uppers, lowers])\n", "    param_tuples = list(zip(*param_product))\n", "    param_columns = pd.MultiIndex.from_tuples(param_tuples, names=['period', 'upper', 'lower'])\n", "    \n", "    # We need two price columns per param combination\n", "    vbt_close_price_mult = vbt_close_price.vbt.tile(len(param_columns), keys=param_columns)\n", "    vbt_open_price_mult = vbt_open_price.vbt.tile(len(param_columns), keys=param_columns)\n", "    \n", "    return vbt.Portfolio.from_order_func(\n", "        vbt_close_price_mult,\n", "        order_func_nb, \n", "        vbt_open_price_mult.values, COMMPERC,  # *args for order_func_nb\n", "        pre_group_func_nb=pre_group_func_nb, \n", "        pre_group_args=(\n", "            np.array(param_product[0]), \n", "            np.array(param_product[1]), \n", "            np.array(param_product[2]), \n", "            ORDER_PCT1, \n", "            ORDER_PCT2\n", "        ),\n", "        pre_segment_func_nb=pre_segment_func_nb, \n", "        pre_segment_args=(MODE,),\n", "        fill_pos_record=False,\n", "        init_cash=CASH,\n", "        cash_sharing=True, \n", "        group_by=param_columns.names,\n", "        freq='d'\n", "    )\n", "\n", "vbt_pf_mult = simulate_mult_from_order_func(periods, uppers, lowers)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["period  upper  lower\n", "10      1.6    -1.5    -0.068466\n", "        1.5    -1.5    -0.067897\n", "        1.8    -1.5    -0.066885\n", "        1.5    -1.6    -0.065260\n", "        1.7    -1.5    -0.065036\n", "                          ...   \n", "100     2.0    -2.2     0.003538\n", "        1.9    -2.2     0.003538\n", "        2.1    -2.2     0.003538\n", "        1.8    -2.2     0.003538\n", "        2.2    -2.2     0.003538\n", "Name: total_return, Length: 1216, dtype: float64\n"]}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"350\" style=\"\" viewBox=\"0 0 700 350\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"350\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-8b96f9\"><g class=\"clips\"><clipPath id=\"clip8b96f9xyplot\" class=\"plotclip\"><rect width=\"634\" height=\"274\"/></clipPath><clipPath class=\"axesclip\" id=\"clip8b96f9x\"><rect x=\"36\" y=\"0\" width=\"634\" height=\"350\"/></clipPath><clipPath class=\"axesclip\" id=\"clip8b96f9y\"><rect x=\"0\" y=\"46\" width=\"700\" height=\"274\"/></clipPath><clipPath class=\"axesclip\" id=\"clip8b96f9xy\"><rect x=\"36\" y=\"46\" width=\"634\" height=\"274\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"36\" y=\"46\" width=\"634\" height=\"274\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"/><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,273.1)\" d=\"M36,0h634\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,226.2)\" d=\"M36,0h634\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,179.3)\" d=\"M36,0h634\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,132.4)\" d=\"M36,0h634\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,85.5)\" d=\"M36,0h634\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,320)\" d=\"M36,0h634\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(36,46)\" clip-path=\"url(#clip8b96f9xyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" shape-rendering=\"crispEdges\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,274V271.65H17.14V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M17.14,274V269.31H34.27V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M34.27,274V266.96H51.41V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M51.41,274V264.62H68.54V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M68.54,274V269.31H85.68V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M85.68,274V271.65H102.81V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M102.81,274V259.93H119.95V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M119.95,274V259.93H137.08V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M137.08,274V262.27H154.22V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M154.22,274V264.62H171.35V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M171.35,274V220.06H188.49V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M188.49,274V220.06H205.62V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M205.62,274V229.44H222.76V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M222.76,274V196.61H239.89V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M239.89,274V205.99H257.03V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M257.03,274V220.06H274.16V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M274.16,274V189.58H291.3V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M291.3,274V191.92H308.43V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M308.43,274V180.2H325.57V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M325.57,274V184.89H342.7V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M342.7,274V187.23H359.84V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M359.84,274V229.44H376.97V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M376.97,274V238.82H394.11V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M394.11,274V236.48H411.24V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M411.24,274V208.34H428.38V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M428.38,274V147.37H445.51V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M445.51,274V105.16H462.65V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M462.65,274V114.54H479.78V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M479.78,274V142.68H496.92V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M496.92,274V156.75H514.05V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M514.05,274V126.26H531.19V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M531.19,274V72.33H548.32V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M548.32,274V119.23H565.46V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M565.46,274V13.7H582.59V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M582.59,274V149.71H599.73V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M599.73,274V126.26H616.86V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g><g class=\"point\"><path d=\"M616.86,274V213.03H634V274Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" transform=\"translate(36,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">−0.07</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(121.68,0)\">−0.06</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(207.35,0)\">−0.05</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(293.03,0)\">−0.04</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(378.7,0)\">−0.03</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(464.38,0)\">−0.02</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(550.05,0)\">−0.01</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"333\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(635.73,0)\">0</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"35\" y=\"4.199999999999999\" transform=\"translate(0,320)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"35\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,273.1)\">20</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"35\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,226.2)\">40</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"35\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,179.3)\">60</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"35\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,132.4)\">80</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"35\" y=\"4.199999999999999\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(0,85.5)\">100</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-8b96f9\"><g class=\"clips\"/><clipPath id=\"legend8b96f9\"><rect width=\"118\" height=\"29\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(552,11.519999999999996)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"118\" height=\"29\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legend8b96f9)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">total_return</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendundefined\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0px; fill: rgb(31, 119, 180); fill-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"114.6875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"/><g class=\"g-xtitle\"/><g class=\"g-ytitle\"/></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(vbt_pf_mult.total_return().sort_values())\n", "\n", "vbt_pf_mult.total_return().vbt.histplot().show_svg()"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.15 s ± 15.5 ms per loop (mean ± std. dev. of 7 runs, 1 loop each)\n"]}], "source": ["# How fast is vbt?\n", "%timeit simulate_mult_from_order_func(periods, uppers, lowers)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Even though the strategy is profitable on paper, the majority of hyperparameter combinations yield a loss, so finding a proper \"slice\" of hyperparameters is just a question of luck when relying on a single backtest. Thanks to vectorbt, we can do thousands of tests in seconds to validate our strategy - the same would last hours using conventional libraries."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 4}