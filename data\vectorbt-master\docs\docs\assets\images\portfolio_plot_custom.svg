<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="750" height="600" style="" viewBox="0 0 750 600"><rect x="0" y="0" width="750" height="600" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-e4a638"><g class="clips"><clipPath id="clipe4a638xyplot" class="plotclip"><rect width="647" height="225.4"/></clipPath><clipPath id="clipe4a638x2y2plot" class="plotclip"><rect width="647" height="225.4"/></clipPath><clipPath class="axesclip" id="clipe4a638x"><rect x="73" y="0" width="647" height="600"/></clipPath><clipPath class="axesclip" id="clipe4a638y"><rect x="0" y="65" width="750" height="225.4"/></clipPath><clipPath class="axesclip" id="clipe4a638xy"><rect x="73" y="65" width="647" height="225.4"/></clipPath><clipPath class="axesclip" id="clipe4a638y2"><rect x="0" y="322.6" width="750" height="225.4"/></clipPath><clipPath class="axesclip" id="clipe4a638xy2"><rect x="73" y="322.6" width="647" height="225.4"/></clipPath><clipPath class="axesclip" id="clipe4a638x2"><rect x="73" y="0" width="647" height="600"/></clipPath><clipPath class="axesclip" id="clipe4a638x2y"><rect x="73" y="65" width="647" height="225.4"/></clipPath><clipPath class="axesclip" id="clipe4a638x2y2"><rect x="73" y="322.6" width="647" height="225.4"/></clipPath></g><g class="gradients"/></defs><g class="bglayer"><rect class="bg" x="73" y="65" width="647" height="225.4" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/><rect class="bg" x="73" y="322.6" width="647" height="225.4" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(95.28999999999999,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(173.1,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(245.89,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(323.71000000000004,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(399.01,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(476.82,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(552.13,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(629.94,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(707.75,0)" d="M0,65v225.4" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,250.46)" d="M73,0h647" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,194.76)" d="M73,0h647" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,139.06)" d="M73,0h647" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,83.36)" d="M73,0h647" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"/><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(73,65)" clip-path="url('#clipe4a638xyplot')"><g class="scatterlayer mlayer"><g class="trace scatter tracedee0ee7c-bf80-42c7-b25b-6f465acf28ae" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M22.29,152.04L24.8,158.02L27.31,148.01L29.82,146.18L32.33,146.16L37.35,125.2L39.86,127.54L42.37,133.13L44.88,125.12L47.39,128.72L49.9,124.4L52.41,125.75L54.92,106.71L57.43,107.29L59.94,109.6L62.45,103.89L64.96,103.5L67.47,110.09L69.98,111.45L72.49,108.99L75,110.8L77.51,118.44L80.02,117.36L82.53,119.52L85.04,113.14L87.55,104.42L90.06,91.92L92.57,93.09L95.08,87.73L97.59,92.15L100.1,90.97L105.12,93.74L107.63,96.87L110.14,84.83L112.65,81.58L117.67,77.82L120.18,70.81L122.69,78.05L125.2,68.26L127.71,64.98L130.22,68.09L132.73,65.37L135.24,77.14L137.75,75.88L140.26,82.69L142.77,70.1L145.28,84.27L147.79,84.96L150.3,82.79L152.81,83.44L155.32,76.16L160.34,92.39L162.85,106.91L165.36,107.91L167.87,111.03L170.38,113.06L172.89,114.1L175.4,105.54L177.91,107.82L180.42,108.73L182.94,99.72L185.45,98.5L187.96,104.42L190.47,126.75L192.98,131.89L198,132.23L200.51,214.13L203.02,197.62L205.53,207.74L208.04,202.39L210.55,212.91L213.06,207.03L215.57,206.68L218.08,180.14L220.59,179.93L223.1,180.31L225.61,190.19L228.12,173.87L230.63,165L233.14,166.5L235.65,165.51L243.18,187.64L245.69,173.49L248.2,173.25L250.71,168.57L253.22,163.36L255.73,165.04L258.24,161.3L260.75,163.43L263.26,150.04L265.77,152.7L268.28,148.31L270.79,149.2L273.3,161.36L275.81,161.54L278.32,158.42L280.83,161.93L283.34,162L285.85,167.58L288.36,154.36L293.38,150.44L295.89,152.34L298.4,160.9L300.91,160.95L303.42,154.35L305.93,145.64L308.44,142.27L310.95,141.74L313.46,138.68L315.97,135.45L318.48,135.14L320.99,107.45L323.5,111.42L326.01,105.68L328.52,102.23L331.03,104.77L333.54,104.34L336.05,101.82L338.56,94.42L341.07,75.41L343.58,78.44L346.09,85.37L348.6,108.69L351.11,113L353.62,107.36L358.64,81.47L361.15,92.77L363.66,91.41L366.17,83.23L368.68,81.67L371.19,81.61L373.7,87.34L376.21,99.63L378.72,96.83L381.23,96.08L383.74,107.75L386.25,104.5L388.76,106.5L391.27,96.87L393.78,87.27L396.29,89.68L398.8,82.4L401.31,89.07L403.82,69.4L406.33,87.15L411.35,79.61L413.86,83.37L416.37,83.7L418.88,80.77L423.9,79.75L426.41,77.68L428.92,92.95L431.43,88.52L433.94,88.67L436.45,91.14L441.47,86.93L443.98,88.53L446.49,90.44L449,93.89L451.51,92.65L454.02,93.45L456.53,83.84L459.04,84.37L461.55,93.18L464.06,94.53L469.09,100.65L471.6,97.91L474.11,96.59L476.62,98.07L479.13,95.55L481.64,98.47L484.15,99.48L486.66,98.22L489.17,99.85L491.68,91.45L494.19,94.88L496.7,89.98L499.21,94.17L501.72,94.14L504.23,95.22L506.74,94.21L509.25,95.13L511.76,95.14L516.78,98.23L519.29,97.69L524.31,96.73L526.82,97.34L531.84,87.28L534.35,85.73L536.86,86.96L539.37,83.05L541.88,76.7L544.39,46.46L546.9,48.64L549.41,43.41L551.92,43.11L554.43,37.2L556.94,25.05L559.45,44.71L561.96,39.35L564.47,40.47L566.98,25.4L569.49,24.49L572,29.46L574.51,25.21L577.02,27.39L579.53,21.75L582.04,34.77L584.55,29.92L587.06,24.37L589.57,24.79L592.08,22.1L594.59,21.34L597.1,11.27L599.61,18.6L602.12,25.09L604.63,21.74L607.14,29.71L609.65,27.22L612.16,27.69L614.67,24.63L617.18,36.01L619.69,32.61L622.2,37.2L624.71,31.1L627.22,32.09L629.73,26.39L632.24,27.25L634.75,19.18" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter trace2a170b0b-62b5-4531-8666-084656833922" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(37.35,125.2)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(59.94,109.6)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(135.24,77.14)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(228.12,173.87)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(318.48,135.14)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(381.23,96.08)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(408.84,83.62)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(469.09,100.65)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(521.8,97.48)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(554.43,37.2)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter trace6c612c6f-e822-48a0-b11d-a5fd8fdf616e" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(44.88,125.12)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(82.53,119.52)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(187.96,104.42)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(275.81,161.54)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(336.05,101.82)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(391.27,96.87)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(466.58,97.37)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(506.74,94.21)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(526.82,97.34)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(609.65,27.22)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"/><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="72" y="4.199999999999999" transform="translate(0,250.46)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">6k</text></g><g class="ytick"><text text-anchor="end" x="72" y="4.199999999999999" transform="translate(0,194.76)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">8k</text></g><g class="ytick"><text text-anchor="end" x="72" y="4.199999999999999" transform="translate(0,139.06)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">10k</text></g><g class="ytick"><text text-anchor="end" x="72" y="4.199999999999999" transform="translate(0,83.36)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">12k</text></g></g><g class="overaxes-above"/></g><g class="subplot x2y2"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x2"/><g class="y2"><path class="y2grid crisp" transform="translate(0,468.54)" d="M73,0h647" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="y2grid crisp" transform="translate(0,389.07000000000005)" d="M73,0h647" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"><path class="y2zl zl crisp" transform="translate(0,548)" d="M73,0h647" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;"/></g><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(73,322.6)" clip-path="url('#clipe4a638x2y2plot')"><g class="barlayer mlayer"><g class="trace bars" style="opacity: 1;"><g class="points"><g class="point"><path d="M21.29,225.4V225.4H23.29V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M23.8,225.4V225.4H25.8V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M26.31,225.4V225.4H28.31V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M28.82,225.4V225.4H30.82V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M31.33,225.4V225.4H33.33V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M33.84,225.4V225.4H35.84V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M36.35,225.4V30.72H38.35V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M38.86,225.4V225.4H40.86V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M41.37,225.4V225.4H43.37V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M43.88,225.4V30.72H45.88V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M46.39,225.4V225.4H48.39V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M48.9,225.4V225.4H50.9V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M51.41,225.4V225.4H53.41V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M53.92,225.4V225.4H55.92V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M56.43,225.4V225.4H58.43V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M58.94,225.4V43.16H60.94V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M61.45,225.4V225.4H63.45V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M63.96,225.4V225.4H65.96V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M66.47,225.4V225.4H68.48V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M68.98,225.4V225.4H70.99V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M71.49,225.4V225.4H73.5V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M74,225.4V225.4H76.01V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M76.51,225.4V225.4H78.52V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M79.02,225.4V225.4H81.03V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M81.53,225.4V43.16H83.54V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M84.04,225.4V225.4H86.05V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M86.55,225.4V225.4H88.56V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M89.06,225.4V225.4H91.07V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M91.57,225.4V225.4H93.58V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M94.08,225.4V225.4H96.09V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M96.59,225.4V225.4H98.6V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M99.1,225.4V225.4H101.11V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M101.61,225.4V225.4H103.62V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M104.12,225.4V225.4H106.13V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M106.63,225.4V225.4H108.64V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M109.14,225.4V225.4H111.15V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M111.65,225.4V225.4H113.66V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M114.16,225.4V225.4H116.17V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M116.67,225.4V225.4H118.68V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M119.18,225.4V225.4H121.19V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M121.69,225.4V225.4H123.7V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M124.2,225.4V225.4H126.21V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M126.71,225.4V225.4H128.72V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M129.22,225.4V225.4H131.23V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M131.73,225.4V225.4H133.74V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M134.24,225.4V71.2H136.25V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M136.75,225.4V225.4H138.76V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M139.26,225.4V225.4H141.27V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M141.77,225.4V225.4H143.78V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M144.28,225.4V225.4H146.29V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M146.79,225.4V225.4H148.8V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M149.3,225.4V225.4H151.31V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M151.81,225.4V225.4H153.82V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M154.32,225.4V225.4H156.33V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M156.83,225.4V225.4H158.84V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M159.34,225.4V225.4H161.35V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M161.85,225.4V225.4H163.86V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M164.36,225.4V225.4H166.37V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M166.87,225.4V225.4H168.88V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M169.38,225.4V225.4H171.39V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M171.89,225.4V225.4H173.9V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M174.4,225.4V225.4H176.41V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M176.91,225.4V225.4H178.92V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M179.42,225.4V225.4H181.43V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M181.93,225.4V225.4H183.94V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M184.44,225.4V225.4H186.45V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M186.95,225.4V71.2H188.96V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M189.46,225.4V225.4H191.47V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M191.97,225.4V225.4H193.98V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M194.48,225.4V225.4H196.49V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M196.99,225.4V225.4H199V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M199.5,225.4V225.4H201.51V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M202.01,225.4V225.4H204.02V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M204.52,225.4V225.4H206.53V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M207.03,225.4V225.4H209.04V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M209.54,225.4V225.4H211.55V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M212.05,225.4V225.4H214.06V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M214.56,225.4V225.4H216.57V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M217.07,225.4V225.4H219.08V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M219.58,225.4V225.4H221.59V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M222.09,225.4V225.4H224.1V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M224.6,225.4V225.4H226.61V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M227.11,225.4V11.27H229.12V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M229.62,225.4V225.4H231.63V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M232.13,225.4V225.4H234.14V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M234.64,225.4V225.4H236.65V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M237.15,225.4V225.4H239.16V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M239.66,225.4V225.4H241.67V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M242.17,225.4V225.4H244.18V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M244.68,225.4V225.4H246.69V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M247.19,225.4V225.4H249.2V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M249.7,225.4V225.4H251.71V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M252.21,225.4V225.4H254.22V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M254.72,225.4V225.4H256.73V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M257.23,225.4V225.4H259.24V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M259.74,225.4V225.4H261.75V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M262.25,225.4V225.4H264.26V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M264.76,225.4V225.4H266.77V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M267.27,225.4V225.4H269.28V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M269.78,225.4V225.4H271.79V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M272.29,225.4V225.4H274.3V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M274.8,225.4V11.27H276.81V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M277.31,225.4V225.4H279.32V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M279.82,225.4V225.4H281.83V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M282.33,225.4V225.4H284.34V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M284.84,225.4V225.4H286.85V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M287.35,225.4V225.4H289.36V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M292.37,225.4V225.4H294.38V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M294.88,225.4V225.4H296.89V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M297.4,225.4V225.4H299.4V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M299.91,225.4V225.4H301.91V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M302.42,225.4V225.4H304.42V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M304.93,225.4V225.4H306.93V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M307.44,225.4V225.4H309.44V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M309.95,225.4V225.4H311.95V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M312.46,225.4V225.4H314.46V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M314.97,225.4V225.4H316.97V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M317.48,225.4V37.27H319.48V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M319.99,225.4V225.4H321.99V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M322.5,225.4V225.4H324.5V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M325.01,225.4V225.4H327.01V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M327.52,225.4V225.4H329.52V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M330.03,225.4V225.4H332.03V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M332.54,225.4V225.4H334.54V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M335.05,225.4V37.27H337.05V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M337.56,225.4V225.4H339.56V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M340.07,225.4V225.4H342.07V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M342.58,225.4V225.4H344.58V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M345.09,225.4V225.4H347.09V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M347.6,225.4V225.4H349.6V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M350.11,225.4V225.4H352.12V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M352.62,225.4V225.4H354.63V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M355.13,225.4V225.4H357.14V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M357.64,225.4V225.4H359.65V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M360.15,225.4V225.4H362.16V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M362.66,225.4V225.4H364.67V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M365.17,225.4V225.4H367.18V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M367.68,225.4V225.4H369.69V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M370.19,225.4V225.4H372.2V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M372.7,225.4V225.4H374.71V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M375.21,225.4V225.4H377.22V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M377.72,225.4V225.4H379.73V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M380.23,225.4V41.48H382.24V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M382.74,225.4V225.4H384.75V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M385.25,225.4V225.4H387.26V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M387.76,225.4V225.4H389.77V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M390.27,225.4V41.48H392.28V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M392.78,225.4V225.4H394.79V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M395.29,225.4V225.4H397.3V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M397.8,225.4V225.4H399.81V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M400.31,225.4V225.4H402.32V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M402.82,225.4V225.4H404.83V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M405.33,225.4V225.4H407.34V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M407.84,225.4V50.54H409.85V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M410.35,225.4V225.4H412.36V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M412.86,225.4V225.4H414.87V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M415.37,225.4V225.4H417.38V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M417.88,225.4V225.4H419.89V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M420.39,225.4V225.4H422.4V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M422.9,225.4V225.4H424.91V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M425.41,225.4V225.4H427.42V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M427.92,225.4V225.4H429.93V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M430.43,225.4V225.4H432.44V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M432.94,225.4V225.4H434.95V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M435.45,225.4V225.4H437.46V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M437.96,225.4V225.4H439.97V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M440.47,225.4V225.4H442.48V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M442.98,225.4V225.4H444.99V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M445.49,225.4V225.4H447.5V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M448,225.4V225.4H450.01V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M450.51,225.4V225.4H452.52V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M453.02,225.4V225.4H455.03V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M455.53,225.4V225.4H457.54V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M458.04,225.4V225.4H460.05V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M460.55,225.4V225.4H462.56V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M463.06,225.4V225.4H465.07V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M465.57,225.4V50.54H467.58V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M468.08,225.4V48.27H470.09V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M470.59,225.4V225.4H472.6V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M473.1,225.4V225.4H475.11V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M475.61,225.4V225.4H477.62V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M478.12,225.4V225.4H480.13V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M480.63,225.4V225.4H482.64V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M483.14,225.4V225.4H485.15V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M485.65,225.4V225.4H487.66V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M488.16,225.4V225.4H490.17V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M490.67,225.4V225.4H492.68V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M493.18,225.4V225.4H495.19V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M495.69,225.4V225.4H497.7V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M498.2,225.4V225.4H500.21V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M500.71,225.4V225.4H502.72V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M503.22,225.4V225.4H505.23V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M505.73,225.4V48.27H507.74V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M508.24,225.4V225.4H510.25V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M510.75,225.4V225.4H512.76V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M513.26,225.4V225.4H515.27V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M515.77,225.4V225.4H517.78V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M518.28,225.4V225.4H520.29V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M520.79,225.4V46H522.8V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M523.3,225.4V225.4H525.31V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M525.81,225.4V46H527.82V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M528.32,225.4V225.4H530.33V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M530.83,225.4V225.4H532.84V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M533.34,225.4V225.4H535.35V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M535.85,225.4V225.4H537.86V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M538.36,225.4V225.4H540.37V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M540.87,225.4V225.4H542.88V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M543.38,225.4V225.4H545.39V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M545.89,225.4V225.4H547.9V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M548.4,225.4V225.4H550.41V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M550.91,225.4V225.4H552.92V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M553.42,225.4V80.21H555.43V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M555.93,225.4V225.4H557.94V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M558.44,225.4V225.4H560.45V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M560.95,225.4V225.4H562.96V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M563.46,225.4V225.4H565.47V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M565.97,225.4V225.4H567.98V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M568.48,225.4V225.4H570.49V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M570.99,225.4V225.4H573V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M573.5,225.4V225.4H575.51V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M576.01,225.4V225.4H578.02V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M578.52,225.4V225.4H580.53V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M581.04,225.4V225.4H583.04V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M583.55,225.4V225.4H585.55V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M586.06,225.4V225.4H588.06V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M588.57,225.4V225.4H590.57V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M591.08,225.4V225.4H593.08V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M593.59,225.4V225.4H595.59V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M596.1,225.4V225.4H598.1V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M598.61,225.4V225.4H600.61V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M601.12,225.4V225.4H603.12V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M603.63,225.4V225.4H605.63V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M606.14,225.4V225.4H608.14V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M608.65,225.4V80.21H610.65V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M611.16,225.4V225.4H613.16V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M613.67,225.4V225.4H615.67V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M616.18,225.4V225.4H618.18V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M618.69,225.4V225.4H620.69V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M621.2,225.4V225.4H623.2V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M623.71,225.4V225.4H625.71V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M626.22,225.4V225.4H628.22V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M628.73,225.4V225.4H630.73V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M631.24,225.4V225.4H633.24V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g><g class="point"><path d="M633.75,225.4V225.4H635.75V225.4Z" style="vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g></g></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(95.28999999999999,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jan 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(173.1,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Feb 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(245.89,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Mar 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(323.71000000000004,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Apr 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(399.01,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">May 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(476.82,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jun 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(552.13,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Jul 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(629.94,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Aug 2020</text></g><g class="x2tick"><text text-anchor="middle" x="0" y="561" transform="translate(707.75,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Sep 2020</text></g></g><g class="yaxislayer-above"><g class="y2tick"><text text-anchor="end" x="72" y="4.199999999999999" transform="translate(0,548)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0</text></g><g class="y2tick"><text text-anchor="end" x="72" y="4.199999999999999" transform="translate(0,468.54)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0.005</text></g><g class="y2tick"><text text-anchor="end" x="72" y="4.199999999999999" transform="translate(0,389.07000000000005)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">0.01</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-e4a638"><g class="clips"/><clipPath id="legende4a638"><rect width="321" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(399,11.84999999999998)"><rect class="bg" shape-rendering="crispEdges" width="321" height="29" x="0" y="0" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;"/><g class="scrollbox" transform="" clip-path="url('#legende4a638')"><g class="groups"><g class="traces" transform="translate(0,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Close</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="74.859375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(77.359375,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Buy</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="65.421875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(145.28125,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Sell</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="64.4375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(212.21875,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Order Size</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="legendundefined" d="M6,6H-6V-6H6Z" transform="translate(20,0)" style="stroke-width: 0.5px; fill: rgb(220, 57, 18); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="105.984375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" x="0" y="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;"/></g><g class="g-gtitle"/><g class="g-xtitle"/><g class="g-x2title"><text class="x2title" x="396.5" y="588.3" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Date</text></g><g class="g-ytitle"><text class="ytitle" transform="rotate(-90,24.840625000000003,177.7)" x="24.840625000000003" y="177.7" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Price</text></g><g class="g-y2title" transform="translate(1.693359375,0)"><text class="y2title" transform="rotate(-90,12.309375000000003,435.3)" x="12.309375000000003" y="435.3" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Order size</text></g><g class="annotation" data-index="0" style="opacity: 1;"><g class="annotation-text-g" transform="rotate(0,396.5,53)"><g class="cursor-pointer" transform="translate(368,41)"><rect class="bg" x="0.5" y="0.5" width="57" height="23" style="stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;"/><text class="annotation-text" text-anchor="middle" x="29.046875" y="18" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Orders</text></g></g></g><g class="annotation" data-index="1" style="opacity: 1;"><g class="annotation-text-g" transform="rotate(0,396.5,310.6)"><g class="cursor-pointer" transform="translate(352,299)"><rect class="bg" x="0.5" y="0.5" width="88" height="23" style="stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;"/><text class="annotation-text" text-anchor="middle" x="44.328125" y="18" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Order Size</text></g></g></g></g></svg>