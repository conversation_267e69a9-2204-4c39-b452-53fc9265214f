<svg class="main-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="750" height="380" style="" viewBox="0 0 750 380"><rect x="0" y="0" width="750" height="380" style="fill: rgb(255, 255, 255); fill-opacity: 1;"/><defs id="defs-cc5f49"><g class="clips"><clipPath id="clipcc5f49xyplot" class="plotclip"><rect width="663" height="245"/></clipPath><clipPath class="axesclip" id="clipcc5f49x"><rect x="57" y="0" width="663" height="380"/></clipPath><clipPath class="axesclip" id="clipcc5f49y"><rect x="0" y="67" width="750" height="245"/></clipPath><clipPath class="axesclip" id="clipcc5f49xy"><rect x="57" y="67" width="663" height="245"/></clipPath></g><g class="gradients"/><g class="patterns"/></defs><g class="bglayer"><rect class="bg" x="57" y="67" width="663" height="245" style="fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;"/></g><g class="layer-below"><g class="imagelayer"/><g class="shapelayer"/></g><g class="cartesianlayer"><g class="subplot xy"><g class="layer-subplot"><g class="shapelayer"/><g class="imagelayer"/></g><g class="gridlayer"><g class="x"><path class="xgrid crisp" transform="translate(118.86,0)" d="M0,67v245" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(201.82,0)" d="M0,67v245" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(284.78999999999996,0)" d="M0,67v245" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(367.76,0)" d="M0,67v245" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(450.73,0)" d="M0,67v245" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(533.69,0)" d="M0,67v245" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(616.66,0)" d="M0,67v245" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="xgrid crisp" transform="translate(699.63,0)" d="M0,67v245" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g><g class="y"><path class="ygrid crisp" transform="translate(0,295.93)" d="M57,0h663" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,253.04)" d="M57,0h663" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,210.14)" d="M57,0h663" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,167.24)" d="M57,0h663" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,124.34)" d="M57,0h663" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/><path class="ygrid crisp" transform="translate(0,81.44)" d="M57,0h663" style="stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;"/></g></g><g class="zerolinelayer"/><path class="xlines-below"/><path class="ylines-below"/><g class="overlines-below"/><g class="xaxislayer-below"/><g class="yaxislayer-below"/><g class="overaxes-below"/><g class="plot" transform="translate(57,67)" clip-path="url(#clipcc5f49xyplot)"><g class="scatterlayer mlayer"><g class="trace scatter tracee201b0a9-48aa-471c-99a2-e073bf86305a" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"><path class="js-line" d="M38.15,148.6L44.08,25.01L50,71.92L55.93,100.52L61.86,195.47L67.78,195.47L73.71,216.48L79.63,43.14L85.56,100L91.49,77.05L97.41,224.52L103.34,20.89L109.27,50.38L115.19,183.39L121.12,189.93L127.04,189.59L132.97,163.68L138.9,116.38L144.82,136.28L150.75,166.47L156.68,97.69L162.6,199.01L168.53,166.27L174.45,150.35L180.38,131.11L186.31,60.52L192.23,186.11L198.16,118.63L204.09,101.86L210.01,218.97L215.94,98.62L221.86,192.36L227.79,214.98L233.72,25.4L239.64,21.81L245.57,55.54L251.5,163.6L257.42,207.98L263.35,82.17L269.27,134.52L275.2,202.76L281.13,122.72L287.05,221.56L292.98,33.89L298.91,173.43L304.83,86.83L310.76,162.07L316.68,117.38L322.61,111.67L328.54,189.28L334.46,20.96L340.39,62.67L346.32,27.41L352.24,37L358.17,100.69L364.09,31.2L370.02,209.95L375.95,186.9L381.87,219.23L387.8,159.15L393.73,145.56L399.65,170.73L405.58,51.17L411.5,152.41L417.43,168.67L423.36,112.53L429.28,198.71L435.21,56.87L441.14,212.94L447.06,17.25L452.99,63.29L458.91,186.31L464.84,227.75L470.77,54.02L476.69,77.32L482.62,72.56L488.55,63.5L494.47,213.05L500.4,152.04L506.32,204.08L512.25,43.8L518.18,95.24L524.1,157.96L530.03,215.3L535.96,162.23L541.88,159.18L547.81,72.44L553.73,92.18L559.66,38.63L565.59,127.65L571.51,203.28L577.44,75.95L583.37,65.75L589.29,108.54L595.22,63.56L601.14,123.02L607.07,116.81L613,137.23L618.92,223.48L624.85,205.79" style="vector-effect: non-scaling-stroke; fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px; opacity: 1;"/></g><g class="points"/><g class="text"/></g><g class="trace scatter tracea9373a0b-5a04-4fdf-9a39-e2d6f597af93" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(38.15,148.6)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(55.93,100.52)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(61.86,195.47)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(79.63,43.14)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(97.41,224.52)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(103.34,20.89)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(109.27,50.38)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(121.12,189.93)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(138.9,116.38)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(144.82,136.28)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(150.75,166.47)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(156.68,97.69)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(162.6,199.01)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(192.23,186.11)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(204.09,101.86)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(210.01,218.97)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(215.94,98.62)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(221.86,192.36)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(227.79,214.98)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(245.57,55.54)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(263.35,82.17)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(275.2,202.76)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(281.13,122.72)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(287.05,221.56)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(304.83,86.83)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(316.68,117.38)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(322.61,111.67)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(328.54,189.28)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(346.32,27.41)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(364.09,31.2)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(370.02,209.95)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(375.95,186.9)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(381.87,219.23)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(393.73,145.56)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(399.65,170.73)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(411.5,152.41)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(417.43,168.67)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(423.36,112.53)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(429.28,198.71)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(435.21,56.87)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(452.99,63.29)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(458.91,186.31)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(464.84,227.75)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(470.77,54.02)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(476.69,77.32)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(482.62,72.56)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(488.55,63.5)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(494.47,213.05)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(500.4,152.04)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(506.32,204.08)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(512.25,43.8)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(535.96,162.23)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(553.73,92.18)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(565.59,127.65)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(583.37,65.75)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(601.14,123.02)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(607.07,116.81)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(618.92,223.48)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g><g class="text"/></g><g class="trace scatter tracee32d7fc2-361e-426d-bd12-ee10c21ef2ac" style="stroke-miterlimit: 2; opacity: 1;"><g class="fills"/><g class="errorbars"/><g class="lines"/><g class="points"><path class="point plotly-customdata" transform="translate(44.08,25.01)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(50,71.92)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(67.78,195.47)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(73.71,216.48)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(85.56,100)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(91.49,77.05)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(115.19,183.39)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(127.04,189.59)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(132.97,163.68)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(168.53,166.27)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(174.45,150.35)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(180.38,131.11)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(186.31,60.52)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(198.16,118.63)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(233.72,25.4)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(239.64,21.81)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(251.5,163.6)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(257.42,207.98)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(269.27,134.52)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(292.98,33.89)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(298.91,173.43)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(310.76,162.07)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(334.46,20.96)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(340.39,62.67)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(352.24,37)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(358.17,100.69)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(387.8,159.15)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(405.58,51.17)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(441.14,212.94)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(447.06,17.25)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(518.18,95.24)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(524.1,157.96)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(530.03,215.3)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(541.88,159.18)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(547.81,72.44)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(559.66,38.63)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(571.51,203.28)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(577.44,75.95)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(589.29,108.54)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(595.22,63.56)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(613,137.23)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/><path class="point plotly-customdata" transform="translate(624.85,205.79)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g><g class="text"/></g></g></g><g class="overplot"/><path class="xlines-above crisp" d="M0,0" style="fill: none;"/><path class="ylines-above crisp" d="M0,0" style="fill: none;"/><g class="overlines-above"/><g class="xaxislayer-above"><g class="xtick"><text text-anchor="middle" x="0" y="325" transform="translate(118.86,0)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;"><tspan class="line" dy="0em" x="0" y="325">Jan 5</tspan><tspan class="line" dy="1.3em" x="0" y="325">2020</tspan></text></g><g class="xtick"><text text-anchor="middle" x="0" y="325" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(201.82,0)">Jan 19</text></g><g class="xtick"><text text-anchor="middle" x="0" y="325" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(284.78999999999996,0)">Feb 2</text></g><g class="xtick"><text text-anchor="middle" x="0" y="325" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(367.76,0)">Feb 16</text></g><g class="xtick"><text text-anchor="middle" x="0" y="325" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(450.73,0)">Mar 1</text></g><g class="xtick"><text text-anchor="middle" x="0" y="325" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(533.69,0)">Mar 15</text></g><g class="xtick"><text text-anchor="middle" x="0" y="325" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(616.66,0)">Mar 29</text></g><g class="xtick"><text text-anchor="middle" x="0" y="325" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(699.63,0)">Apr 12</text></g></g><g class="yaxislayer-above"><g class="ytick"><text text-anchor="end" x="56" y="4.199999999999999" transform="translate(0,295.93)" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;">1</text></g><g class="ytick"><text text-anchor="end" x="56" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,253.04)">1.2</text></g><g class="ytick"><text text-anchor="end" x="56" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,210.14)">1.4</text></g><g class="ytick"><text text-anchor="end" x="56" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,167.24)">1.6</text></g><g class="ytick"><text text-anchor="end" x="56" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,124.34)">1.8</text></g><g class="ytick"><text text-anchor="end" x="56" y="4.199999999999999" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;" transform="translate(0,81.44)">2</text></g></g><g class="overaxes-above"/></g></g><g class="polarlayer"/><g class="ternarylayer"/><g class="geolayer"/><g class="funnelarealayer"/><g class="pielayer"/><g class="iciclelayer"/><g class="treemaplayer"/><g class="sunburstlayer"/><g class="glimages"/><defs id="topdefs-cc5f49"><g class="clips"/><clipPath id="legendcc5f49"><rect width="213" height="29" x="0" y="0"/></clipPath></defs><g class="layer-above"><g class="imagelayer"/><g class="shapelayer"/></g><g class="infolayer"><g class="legend" pointer-events="all" transform="translate(507,12.210526315789501)"><rect class="bg" shape-rendering="crispEdges" style="stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;" width="213" height="29" x="0" y="0"/><g class="scrollbox" transform="" clip-path="url(#legendcc5f49)"><g class="groups"><g class="traces" transform="translate(0,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Close</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"><path class="js-line" d="M5,0h30" style="fill: none; stroke: rgb(31, 119, 180); stroke-opacity: 1; stroke-width: 2px;"/></g><g class="legendsymbols"><g class="legendpoints"/></g></g><rect class="legendtoggle" x="0" y="-9.5" width="74.859375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(77.359375,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Buy</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M-4.62,2H4.62L0,-4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(55, 177, 63); fill-opacity: 1; stroke: rgb(38, 123, 44); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="65.421875" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g><g class="traces" transform="translate(145.28125,14.5)" style="opacity: 1;"><text class="legendtext" text-anchor="start" x="40" y="4.680000000000001" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Sell</text><g class="layers" style="opacity: 1;"><g class="legendfill"/><g class="legendlines"/><g class="legendsymbols"><g class="legendpoints"><path class="scatterpts" transform="translate(20,0)" d="M-4.62,-2H4.62L0,4Z" style="opacity: 1; stroke-width: 1px; fill: rgb(234, 67, 53); fill-opacity: 1; stroke: rgb(181, 31, 18); stroke-opacity: 1;"/></g></g></g><rect class="legendtoggle" x="0" y="-9.5" width="64.4375" height="19" style="fill: rgb(0, 0, 0); fill-opacity: 0;"/></g></g></g><rect class="scrollbar" rx="20" ry="3" width="0" height="0" style="fill: rgb(128, 139, 164); fill-opacity: 1;" x="0" y="0"/></g><g class="g-gtitle"/><g class="g-xtitle"><text class="xtitle" x="388.5" y="367.909375" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Index</text></g><g class="g-ytitle" transform="translate(2.4248046875,0)"><text class="ytitle" transform="rotate(-90,11.575000000000003,189.5)" x="11.575000000000003" y="189.5" text-anchor="middle" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;">Price</text></g><g class="annotation" data-index="0" style="opacity: 1;"><g class="annotation-text-g" transform="rotate(0,388.5,55)"><g class="cursor-pointer" transform="translate(360,43)"><rect class="bg" x="0.5" y="0.5" width="57" height="23" style="stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;"/><text class="annotation-text" text-anchor="middle" x="29.046875" y="18" style="font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;">Orders</text></g></g></g></g></svg>