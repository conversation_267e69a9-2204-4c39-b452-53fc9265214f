params moduleUtilities for working with parameters. broadcast_params functionbroadcast_params ( param_list , to_n = None ) Broadcast parameters in param_list . create_param_combs functioncreate_param_combs ( op_tree , depth = 0 ) Create arbitrary parameter combinations from the operation tree op_tree . op_tree is a tuple with nested instructions to generate parameters. The first element of the tuple should be a callable that takes remaining elements as arguments. If one of the elements is a tuple itself and its first argument is a callable, it will be unfolded in the same way as above. Usage import numpy as np from itertools import combinations , product create_param_combs (( product , ( combinations , [ 0 , 1 , 2 , 3 ], 2 ), [ 4 , 5 ])) [[0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2], [1, 1, 2, 2, 3, 3, 2, 2, 3, 3, 3, 3], [4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5]] create_param_product functioncreate_param_product ( param_list ) Make Cartesian product out of all params in param_list . flatten_param_tuples functionflatten_param_tuples ( param_tuples ) Flattens a nested list of iterables using unzipping. to_typed_list functionto_typed_list ( lst ) Cast Python list to typed list. Direct construction is flawed in Numba 0.52.0. See https://github.com/numba/numba/issues/6651.