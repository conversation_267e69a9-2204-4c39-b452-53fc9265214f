colors moduleUtilities for working with colors. adjust_lightness functionadjust_lightness ( color , amount = 0.7 ) Lightens the given color by multiplying (1-luminosity) by the given amount. Input can be matplotlib color string, hex string, or RGB tuple. Output will be an RGB string. adjust_opacity functionadjust_opacity ( color , opacity ) Adjust opacity of color. rgb_from_cmap functionrgb_from_cmap ( cmap_name , value , value_range ) Map value_range to colormap with name cmap_name and get RGB of the value from that range.