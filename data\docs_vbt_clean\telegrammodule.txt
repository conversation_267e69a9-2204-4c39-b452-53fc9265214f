telegram moduleMessaging using python-telegram-bot . self_decorator functionself_decorator ( func ) Pass bot object to func command. send_action functionsend_action ( action ) Sends action while processing func command. Suitable only for bound callbacks taking arguments self , update , context and optionally other. LogHandler classLogHandler ( callback , pass_update_queue = False , pass_job_queue = False , pass_user_data = False , pass_chat_data = False , run_async = False ) Handler to log user updates. Superclasses abc.ABC telegram.ext.handler.Handler typing.Generic check_update methodLogHandler . check_update ( update ) This method is called to determine if an update should be handled by this handler instance. It should always be overridden. Note Custom updates types can be handled by the dispatcher. Therefore, an implementation of this method should always check the type of :attr: update . Args update (:obj: str | :class: telegram.Update ): The update to be tested. Returns Either :obj: None or :obj: False if the update should not be handled. Otherwise an object that will be passed to :meth: handle_update and :meth: collect_additional_context when the update gets handled. TelegramBot classTelegramBot ( giphy_kwargs = None , ** kwargs ) Telegram bot. See Extensions – Your first Bot . **kwargs are passed to telegram.ext.updater.Updater and override settings under messaging.telegram in settings . Usage Let's extend TelegramBot to track cryptocurrency prices: from telegram.ext import CommandHandler import ccxt import logging import vectorbt as vbt logging . basicConfig ( level = logging . INFO ) # enable logging

MyTelegramBot ( vbt . TelegramBot ): ... @property ... def custom_handlers ( self ): ... return ( CommandHandler ( 'get' , self . get ),) ... ... @property ... def help_message ( self ): ... return "Type /get [symbol] [exchange id (optional)] to get the latest price." ... ... def get ( self , update , context ): ... chat_id = update . effective_chat . id ... ... if len ( context . args ) == 1 : ... symbol = context . args [ 0 ] ... exchange = 'binance' ... elif len ( context . args ) == 2 : ... symbol = context . args [ 0 ] ... exchange = context . args [ 1 ] ... else : ... self . send_message ( chat_id , "This command requires symbol and optionally exchange id." ) ... return ... try : ... ticker = getattr ( ccxt , exchange )() . fetchTicker ( symbol ) ... except Exception as e : ... self . send_message ( chat_id , str ( e )) ... return ... self . send_message ( chat_id , str ( ticker [ 'last' ])) bot = MyTelegramBot ( token = 'YOUR_TOKEN' ) bot . start () INFO:vectorbt.utils.messaging:Initializing bot INFO:vectorbt.utils.messaging:Loaded chat ids [447924619] INFO:vectorbt.utils.messaging:Running bot vectorbt_bot INFO:apscheduler.scheduler:Scheduler started INFO:vectorbt.utils.messaging:447924619 - Bot: "I'm back online!" INFO:vectorbt.utils.messaging:447924619 - User: "/start" INFO:vectorbt.utils.messaging:447924619 - Bot: "Hello!" INFO:vectorbt.utils.messaging:447924619 - User: "/help" INFO:vectorbt.utils.messaging:447924619 - Bot: "Type /get [symbol] [exchange id (optional)] to get the latest price." INFO:vectorbt.utils.messaging:447924619 - User: "/get BTC/USDT" INFO:vectorbt.utils.messaging:447924619 - Bot: "55530.55" INFO:vectorbt.utils.messaging:447924619 - User: "/get BTC/USD bitmex" INFO:vectorbt.utils.messaging:447924619 - Bot: "55509.0" INFO:telegram.ext.updater:Received signal 2 (SIGINT), stopping... INFO:apscheduler.scheduler:Scheduler has been shut down Superclasses Configured Documented Pickleable Inherited members Configured.config Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Configured.writeable_attrs Pickleable.load() Pickleable.save() chat_ids propertyChat ids that ever interacted with this bot. A chat id is added upon receiving the "/start" command. chat_migration_callback methodTelegramBot . chat_migration_callback ( update , context ) Chat migration callback. custom_handlers propertyCustom handlers to add. Override to add custom handlers. Order counts. dispatcher propertyDispatcher. error_callback methodTelegramBot . error_callback ( update , context , * args ) Error callback. help_callback methodTelegramBot . help_callback ( update , context ) Help command callback. help_message propertyMessage to be sent upon "/help" command. Override to define your own message. log_handler propertyLog handler. running propertyWhether the bot is running. send methodTelegramBot . send ( kind , chat_id , * args , log_msg = None , ** kwargs ) Send message of any kind to chat_id . send_giphy methodTelegramBot . send_giphy ( chat_id , text , * args , giphy_kwargs = None , ** kwargs ) Send GIPHY from text to chat_id . send_giphy_to_all methodTelegramBot . send_giphy_to_all ( text , * args , giphy_kwargs = None , ** kwargs ) Send GIPHY from text to all in TelegramBot.chat_ids . send_message methodTelegramBot . send_message ( chat_id , text , * args , ** kwargs ) Send text message to chat_id . send_message_to_all methodTelegramBot . send_message_to_all ( text , * args , ** kwargs ) Send text message to all in TelegramBot.chat_ids . send_to_all methodTelegramBot . send_to_all ( kind , * args , ** kwargs ) Send message of any kind to all in TelegramBot.chat_ids . start methodTelegramBot . start ( in_background = False , ** kwargs ) Start the bot. **kwargs are passed to telegram.ext.updater.Updater.start_polling and override settings under messaging.telegram in settings . start_callback methodTelegramBot . start_callback ( update , context ) Start command callback. start_message propertyMessage to be sent upon "/start" command. Override to define your own message. started_callback methodTelegramBot . started_callback () Callback once the bot has been started. Override to execute custom commands upon starting the bot. stop methodTelegramBot . stop () Stop the bot. unknown_callback methodTelegramBot . unknown_callback ( update , context ) Unknown command callback. updater propertyUpdater.