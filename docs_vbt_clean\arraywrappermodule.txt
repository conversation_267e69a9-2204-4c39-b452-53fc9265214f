array_wrapper moduleClass for wrapping NumPy arrays into Series/DataFrames. vectorbt's functionality is based upon the ability to perform the most essential pandas operations using NumPy+Numba stack. One has to convert the Series/DataFrame into the NumPy format, perform the computation, and put the array back into the pandas format. The last step is done using ArrayWrapper . It stores metadata of the original pandas object and offers methods wrap and wrap_reduced for wrapping NumPy arrays to match the stored metadata as closest as possible. import numpy as np import pandas as pd import vectorbt as vbt from vectorbt.base.array_wrapper import ArrayWrapper aw = ArrayWrapper ( index = [ 'x' , 'y' , 'z' ], columns = [ 'a' , 'b' , 'c' ], ndim = 2 ) aw . _config { 'columns': Index(['a', 'b', 'c'], dtype='object'), 'group_select': None, 'ndim': 2, 'freq': None, 'column_only_select': None, 'grouped_ndim': None, 'index': ['x', 'y', 'z'], 'allow_modify': True, 'allow_enable': True, 'group_by': None, 'allow_disable': True } np . random . seed ( 42 ) a = np . random . uniform ( size = ( 3 , 3 )) aw . wrap ( a ) a b c x 0.374540 0.950714 0.731994 y 0.598658 0.156019 0.155995 z 0.058084 0.866176 0.601115 aw . wrap_reduced ( np . sum ( a , axis = 0 )) a 1.031282 b 1.972909 c 1.489103 dtype: float64 It can also be indexed as a regular pandas object and integrates ColumnGrouper : aw . loc [ 'x' : 'y' , 'a' ] . _config { 'columns': Index(['a'], dtype='object'), 'group_select': None, 'ndim': 1, 'freq': None, 'column_only_select': None, 'grouped_ndim': None, 'index': Index(['x', 'y'], dtype='object'), 'allow_modify': True, 'allow_enable': True, 'group_by': None, 'allow_disable': True } aw . regroup ( np . array ([ 0 , 0 , 1 ])) . _config { 'columns': Index(['a', 'b', 'c'], dtype='object'), 'group_select': None, 'ndim': 2, 'freq': None, 'column_only_select': None, 'grouped_ndim': None, 'index': ['x', 'y', 'z'], 'allow_modify': True, 'allow_enable': True, 'group_by': array([0, 0, 1]), 'allow_disable': True }

Wrapping is a convenience class meant to be subclassed by classes that do not want to subclass ArrayWrapper but rather use it as an attribute (which is a better SE design pattern anyway!). ArrayWrapper classArrayWrapper ( index , columns , ndim , freq = None , column_only_select = None , group_select = None , grouped_ndim = None , ** kwargs ) Class that stores index, columns and shape metadata for wrapping NumPy arrays. Tightly integrated with ColumnGrouper . If the underlying object is a Series, pass [sr.name] as columns . **kwargs are passed to ColumnGrouper . Note This class is meant to be immutable. To change any attribute, use Configured.replace() . Use methods that begin with get_ to get group-aware results. Superclasses Configured Documented IndexingBase PandasIndexer Pickleable Inherited members Configured.config Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Configured.writeable_attrs PandasIndexer.iloc PandasIndexer.indexing_kwargs PandasIndexer.loc PandasIndexer.xs() Pickleable.load() Pickleable.save() column_only_select propertyWhether to perform indexing on columns only. columns propertyColumns. dummy methodArrayWrapper . dummy ( group_by = None , ** kwargs ) Create a dummy Series/DataFrame. fill methodArrayWrapper . fill ( fill_value , group_by = None , ** kwargs ) Fill a Series/DataFrame. fill_reduced methodArrayWrapper . fill_reduced ( fill_value , group_by = None , ** kwargs ) Fill a reduced Series/DataFrame. freq propertyIndex frequency. from_obj class methodArrayWrapper . from_obj ( obj , * args , ** kwargs ) Derive metadata from an object. from_shape class methodArrayWrapper . from_shape ( shape , * args , ** kwargs ) Derive metadata from shape. get_columns methodArrayWrapper . get_columns ( group_by = None ) Get group-aware ArrayWrapper.columns . get_name methodArrayWrapper . get_name ( group_by = None ) Get group-aware ArrayWrapper.name . get_ndim methodArrayWrapper . get_ndim ( group_by = None ) Get group-aware ArrayWrapper.ndim . get_shape methodArrayWrapper . get_shape ( group_by = None ) Get group-aware ArrayWrapper.shape . get_shape_2d methodArrayWrapper . get_shape_2d ( group_by = None ) Get group-aware ArrayWrapper.shape_2d . group_select propertyWhether to perform indexing on groups. grouped_ndim propertyNumber of dimensions under column grouping. grouper propertyColumn grouper. index propertyIndex. indexing_func methodArrayWrapper . indexing_func ( pd_indexing_func , ** kwargs ) Perform indexing on ArrayWrapper indexing_func_meta methodArrayWrapper . indexing_func_meta ( pd_indexing_func , index = None , columns = None , column_only_select = None , group_select = None , group_by = None ) Perform indexing on ArrayWrapper and also return indexing metadata. Takes into account column grouping. Set column_only_select to True to index the array wrapper as a Series of columns. This way, selection of index (axis 0) can be avoided. Set group_select to True to select groups rather than columns. Takes effect only if grouping is enabled. Note If column_only_select is True, make sure to index the array wrapper as a Series of columns rather than a DataFrame. For example, the operation .iloc[:, :2] should become .iloc[:2] . Operations are not allowed if the object is already a Series and thus has only one column/group. name propertyName. ndim propertyNumber of dimensions. regroup methodArrayWrapper . regroup ( group_by , ** kwargs ) Regroup this object. Only creates a new instance if grouping has changed, otherwise returns itself. resolve methodArrayWrapper . resolve ( group_by = None , ** kwargs ) Resolve this object. Replaces columns and other metadata with groups. shape propertyShape. shape_2d propertyShape as if the object was two-dimensional. to_timedelta methodArrayWrapper . to_timedelta ( a , to_pd = False , silence_warnings = None ) Convert array to duration using ArrayWrapper.freq . wrap methodArrayWrapper . wrap ( arr , index = None , columns = None , fillna = None , dtype = None , group_by = None , to_timedelta = False , to_index = False , silence_warnings = None ) Wrap a NumPy array using the stored metadata. Runs the following pipeline: 1) Converts to NumPy array 2) Fills NaN (optional) 3) Wraps using index, columns, and dtype (optional) 4) Converts to index (optional) 5) Converts to timedelta using ArrayWrapper.to_timedelta() (optional) wrap_reduced methodArrayWrapper . wrap_reduced ( arr , name_or_index = None , columns = None , fillna = None , dtype = None , group_by = None , to_timedelta = False , to_index = False , silence_warnings = None ) Wrap result of reduction. name_or_index can be the name of the resulting series if reducing to a scalar per column, or the index of the resulting series/dataframe if reducing to an array per column. columns can be set to override object's default columns. See ArrayWrapper.wrap() for the pipeline. Wrapping classWrapping ( wrapper , ** kwargs ) Class that uses ArrayWrapper globally. Superclasses AttrResolver Configured Documented IndexingBase PandasIndexer Pickleable Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() AttrResolver.self_aliases Configured.config Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Configured.writeable_attrs PandasIndexer.iloc PandasIndexer.indexing_kwargs PandasIndexer.loc PandasIndexer.xs() Pickleable.load() Pickleable.save() Subclasses BaseAccessor ColumnMapper Data IndicatorBase MappedArray Portfolio Records indexing_func methodWrapping . indexing_func ( pd_indexing_func , ** kwargs ) Perform indexing on Wrapping . regroup methodWrapping . regroup ( group_by , ** kwargs ) Regroup this object. Only creates a new instance if grouping has changed, otherwise returns itself. **kwargs will be passed to ArrayWrapper.regroup() . resolve_self methodWrapping . resolve_self ( cond_kwargs = None , custom_arg_names = None , impacts_caching = True , silence_warnings = None ) Resolve self. Creates a copy of this instance if a different freq can be found in cond_kwargs . select_one methodWrapping . select_one ( column = None , group_by = None , ** kwargs ) Select one column/group. column can be a label-based position as well as an integer position (if label fails). select_one_from_obj static methodWrapping . select_one_from_obj ( obj , wrapper , column = None ) Select one column/group from a pandas object. column can be a label-based position as well as an integer position (if label fails). wrapper propertyArray wrapper.