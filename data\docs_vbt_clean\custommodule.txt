custom moduleCustom data classes that subclass Data . generate_gbm_paths functiongenerate_gbm_paths ( S0 , mu , sigma , T , M , I , seed = None ) Generate using Geometric Brownian Motion (GBM). See https://stackoverflow.com/a/45036114/8141780. AlpacaData classAlpacaData ( wrapper , data , tz_localize , tz_convert , missing_index , missing_columns , download_kwargs , ** kwargs ) Data for data coming from alpaca-py . Sign up for Alpaca API keys under https://app.alpaca.markets/signup. Usage Fetch the 1-minute data of the last 2 hours, wait 1 minute, and update: import vectorbt as vbt alpaca_data = vbt . AlpacaData . download ( ... "AAPL" , ... start = '2 hours ago UTC' , ... end = '15 minutes ago UTC' , ... interval = '1m' ... ) alpaca_data . get () Open High Low Close Volume timestamp 2021-12-27 14:04:00+00:00 177.0500 177.0500 177.0500 177.0500 1967 2021-12-27 14:05:00+00:00 177.0500 177.0500 177.0300 177.0500 3218 2021-12-27 14:06:00+00:00 177.0400 177.0400 177.0400 177.0400 873 ... ... ... ... ... ... 2021-12-27 15:46:00+00:00 177.9500 178.0000 177.8289 177.8850 162778 2021-12-27 15:47:00+00:00 177.8810 177.9600 177.8400 177.9515 123284 2021-12-27 15:48:00+00:00 177.9600 178.0500 177.9600 178.0100 159700 [105 rows x 5 columns] import time time . sleep ( 60 ) alpaca_data = alpaca_data . update () alpaca_data . get () Open High Low Close Volume timestamp 2021-12-27 14:04:00+00:00 177.0500 177.0500 177.0500 177.0500 1967 2021-12-27 14:05:00+00:00 177.0500 177.0500 177.0300 177.0500 3218 2021-12-27 14:06:00+00:00 177.0400 177.0400 177.0400 177.0400 873 ... ... ... ... ... ... 2021-12-27 15:47:00+00:00 177.8810 177.9600 177.8400 177.9515 123284 2021-12-27 15:48:00+00:00 177.9600 178.0500 177.9600 178.0100 159700 2021-12-27 15:49:00+00:00 178.0100 178.0700 177.9700 178.0650 185037 [106 rows x 5 columns] Superclasses AttrResolver Configured Data Documented IndexingBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Data.align_columns() Data.align_index() Data.concat() Data.config Data.data Data.download() Data.download_kwargs Data.from_data() Data.get() Data.iloc Data.indexing_func() Data.indexing_kwargs Data.loc Data.missing_columns Data.missing_index Data.plot() Data.plots_defaults Data.select_symbol_kwargs() Data.self_aliases Data.stats_defaults Data.symbols Data.tz_convert Data.tz_localize Data.update() Data.wrapper Data.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() download_symbol class methodAlpacaData . download_symbol ( symbol , timeframe = '1d' , start = 0 , end = 'now UTC' , adjustment = 'all' , limit = 500 , feed = None , ** kwargs ) Download the symbol. Args symbol : str Symbol. timeframe : str Timeframe of data. Must be integer multiple of 'm' (minute), 'h' (hour) or 'd' (day). i.e. '15m'. See https://alpaca.markets/data. Note Data from the latest 15 minutes is not available with a free data plan. start : any Start datetime. See to_tzaware_datetime() . end : any End datetime. See to_tzaware_datetime() . adjustment : str Specifies the corporate action adjustment for the stocks. Allowed are raw , split , dividend or all . limit : int The maximum number of returned items. feed : str The feed to pull market data from. This is either "iex", "otc", or "sip". Feeds "sip" and "otc" are only available to those with a subscription. Default is "iex" for free plans and "sip" for paid. For defaults, see data.alpaca in settings . update_symbol methodAlpacaData . update_symbol ( symbol , ** kwargs ) Update the symbol. **kwargs will override keyword arguments passed to AlpacaData.download_symbol() . BinanceData classBinanceData ( wrapper , data , tz_localize , tz_convert , missing_index , missing_columns , download_kwargs , ** kwargs ) Data for data coming from python-binance . Usage Fetch the 1-minute data of the last 2 hours, wait 1 minute, and update: import vectorbt as vbt binance_data = vbt . BinanceData . download ( ... "BTCUSDT" , ... start = '2 hours ago UTC' , ... end = 'now UTC' , ... interval = '1m' ... ) binance_data . get () 2021-05-02 14:47:20.478000+00:00 - 2021-05-02 16:47:00+00:00: : 1it [00:00, 3.42it/s] Open High Low Close Volume \ Open time 2021-05-02 14:48:00+00:00 56867.44 56913.57 56857.40 56913.56 28.709976 2021-05-02 14:49:00+00:00 56913.56 56913.57 56845.94 56888.00 19.734841 2021-05-02 14:50:00+00:00 56888.00 56947.32 56879.78 56934.71 23.150163 ... ... ... ... ... ... 2021-05-02 16:45:00+00:00 56664.13 56666.77 56641.11 56644.03 40.852719 2021-05-02 16:46:00+00:00 56644.02 56663.43 56605.17 56605.18 27.573654 2021-05-02 16:47:00+00:00 56605.18 56657.55 56605.17 56627.12 7.719933 Close time Quote volume \ Open time 2021-05-02 14:48:00+00:00 2021-05-02 14:48:59.999000+00:00 1.633534e+06 2021-05-02 14:49:00+00:00 2021-05-02 14:49:59.999000+00:00 1.122519e+06 2021-05-02 14:50:00+00:00 2021-05-02 14:50:59.999000+00:00 1.317969e+06 ... ... ... 2021-05-02 16:45:00+00:00 2021-05-02 16:45:59.999000+00:00 2.314579e+06 2021-05-02 16:46:00+00:00 2021-05-02 16:46:59.999000+00:00 1.561548e+06 2021-05-02 16:47:00+00:00 2021-05-02 16:47:59.999000+00:00 4.371848e+05 Number of trades Taker base volume \ Open time 2021-05-02 14:48:00+00:00 991 13.771152 2021-05-02 14:49:00+00:00 816 5.981942 2021-05-02 14:50:00+00:00 1086 10.813757 ... ... ... 2021-05-02 16:45:00+00:00 1006 18.106933 2021-05-02 16:46:00+00:00 916 14.869411 2021-05-02 16:47:00+00:00 353 3.903321 Taker quote volume Open time 2021-05-02 14:48:00+00:00 7.835391e+05 2021-05-02 14:49:00+00:00 3.402170e+05 2021-05-02 14:50:00+00:00 6.156418e+05 ... ... 2021-05-02 16:45:00+00:00 1.025892e+06 2021-05-02 16:46:00+00:00 8.421173e+05 2021-05-02 16:47:00+00:00 2.210323e+05 [120 rows x 10 columns] import time time . sleep ( 60 ) binance_data = binance_data . update () binance_data . get () Open High Low Close Volume \ Open time 2021-05-02 14:48:00+00:00 56867.44 56913.57 56857.40 56913.56 28.709976 2021-05-02 14:49:00+00:00 56913.56 56913.57 56845.94 56888.00 19.734841 2021-05-02 14:50:00+00:00 56888.00 56947.32 56879.78 56934.71 23.150163 ... ... ... ... ... ... 2021-05-02 16:46:00+00:00 56644.02 56663.43 56605.17 56605.18 27.573654 2021-05-02 16:47:00+00:00 56605.18 56657.55 56605.17 56625.76 14.615437 2021-05-02 16:48:00+00:00 56625.75 56643.60 56614.32 56623.01 5.895843 Close time Quote volume \ Open time 2021-05-02 14:48:00+00:00 2021-05-02 14:48:59.999000+00:00 1.633534e+06 2021-05-02 14:49:00+00:00 2021-05-02 14:49:59.999000+00:00 1.122519e+06 2021-05-02 14:50:00+00:00 2021-05-02 14:50:59.999000+00:00 1.317969e+06 ... ... ... 2021-05-02 16:46:00+00:00 2021-05-02 16:46:59.999000+00:00 1.561548e+06 2021-05-02 16:47:00+00:00 2021-05-02 16:47:59.999000+00:00 8.276017e+05 2021-05-02 16:48:00+00:00 2021-05-02 16:48:59.999000+00:00 3.338702e+05 Number of trades Taker base volume \ Open time 2021-05-02 14:48:00+00:00 991 13.771152 2021-05-02 14:49:00+00:00 816 5.981942 2021-05-02 14:50:00+00:00 1086 10.813757 ... ... ... 2021-05-02 16:46:00+00:00 916 14.869411 2021-05-02 16:47:00+00:00 912 7.778489 2021-05-02 16:48:00+00:00 308 2.358130 Taker quote volume Open time 2021-05-02 14:48:00+00:00 7.835391e+05 2021-05-02 14:49:00+00:00 3.402170e+05 2021-05-02 14:50:00+00:00 6.156418e+05 ... ... 2021-05-02 16:46:00+00:00 8.421173e+05 2021-05-02 16:47:00+00:00 4.404362e+05 2021-05-02 16:48:00+00:00 1.335474e+05 [121 rows x 10 columns] Superclasses AttrResolver Configured Data Documented IndexingBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Data.align_columns() Data.align_index() Data.concat() Data.config Data.data Data.download_kwargs Data.from_data() Data.get() Data.iloc Data.indexing_func() Data.indexing_kwargs Data.loc Data.missing_columns Data.missing_index Data.plot() Data.plots_defaults Data.select_symbol_kwargs() Data.self_aliases Data.stats_defaults Data.symbols Data.tz_convert Data.tz_localize Data.update() Data.wrapper Data.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() download class methodBinanceData . download ( symbols , client = None , ** kwargs ) Override Data.download() to instantiate a Binance client. download_symbol class methodBinanceData . download_symbol ( symbol , client = None , interval = '1d' , start = 0 , end = 'now UTC' , delay = 500 , limit = 500 , show_progress = True , tqdm_kwargs = None ) Download the symbol. Args symbol : str Symbol. client : binance.client.Client Binance client of type binance.client.Client . interval : str Kline interval. See binance.enums . start : any Start datetime. See to_tzaware_datetime() . end : any End datetime. See to_tzaware_datetime() . delay : float Time to sleep after each request (in milliseconds). limit : int The maximum number of returned items. show_progress : bool Whether to show the progress bar. tqdm_kwargs : dict Keyword arguments passed to tqdm . For defaults, see data.binance in settings . update_symbol methodBinanceData . update_symbol ( symbol , ** kwargs ) Update the symbol. **kwargs will override keyword arguments passed to BinanceData.download_symbol() . CCXTData classCCXTData ( wrapper , data , tz_localize , tz_convert , missing_index , missing_columns , download_kwargs , ** kwargs ) Data for data coming from ccxt . Usage Fetch the 1-minute data of the last 2 hours, wait 1 minute, and update: import vectorbt as vbt ccxt_data = vbt . CCXTData . download ( ... "BTC/USDT" , ... start = '2 hours ago UTC' , ... end = 'now UTC' , ... timeframe = '1m' ... ) ccxt_data . get () 2021-05-02 14:50:26.305000+00:00 - 2021-05-02 16:50:00+00:00: : 1it [00:00, 1.96it/s] Open High Low Close Volume Open time 2021-05-02 14:51:00+00:00 56934.70 56964.59 56910.00 56948.99 22.158319 2021-05-02 14:52:00+00:00 56948.99 56999.00 56940.04 56977.62 46.958464 2021-05-02 14:53:00+00:00 56977.61 56987.09 56882.98 56885.42 27.752200 ... ... ... ... ... ... 2021-05-02 16:48:00+00:00 56625.75 56643.60 56595.47 56596.01 15.452510 2021-05-02 16:49:00+00:00 56596.00 56664.14 56596.00 56640.35 12.777475 2021-05-02 16:50:00+00:00 56640.35 56675.82 56640.35 56670.65 6.882321 [120 rows x 5 columns] import time time . sleep ( 60 ) ccxt_data = ccxt_data . update () ccxt_data . get () Open High Low Close Volume Open time 2021-05-02 14:51:00+00:00 56934.70 56964.59 56910.00 56948.99 22.158319 2021-05-02 14:52:00+00:00 56948.99 56999.00 56940.04 56977.62 46.958464 2021-05-02 14:53:00+00:00 56977.61 56987.09 56882.98 56885.42 27.752200 ... ... ... ... ... ... 2021-05-02 16:49:00+00:00 56596.00 56664.14 56596.00 56640.35 12.777475 2021-05-02 16:50:00+00:00 56640.35 56689.99 56640.35 56678.33 14.610231 2021-05-02 16:51:00+00:00 56678.33 56688.99 56636.89 56653.42 11.647158 [121 rows x 5 columns] Superclasses AttrResolver Configured Data Documented IndexingBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Data.align_columns() Data.align_index() Data.concat() Data.config Data.data Data.download() Data.download_kwargs Data.from_data() Data.get() Data.iloc Data.indexing_func() Data.indexing_kwargs Data.loc Data.missing_columns Data.missing_index Data.plot() Data.plots_defaults Data.select_symbol_kwargs() Data.self_aliases Data.stats_defaults Data.symbols Data.tz_convert Data.tz_localize Data.update() Data.wrapper Data.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() download_symbol class methodCCXTData . download_symbol ( symbol , exchange = 'binance' , config = None , timeframe = '1d' , start = 0 , end = 'now UTC' , delay = None , limit = 500 , retries = 3 , show_progress = True , params = None , tqdm_kwargs = None ) Download the symbol. Args symbol : str Symbol. exchange : str or object Exchange identifier or an exchange object of type ccxt.base.exchange.Exchange . config : dict Config passed to the exchange upon instantiation. Will raise an exception if exchange has been already instantiated. timeframe : str Timeframe supported by the exchange. start : any Start datetime. See to_tzaware_datetime() . end : any End datetime. See to_tzaware_datetime() . delay : float Time to sleep after each request (in milliseconds). Note Use only if enableRateLimit is not set. limit : int The maximum number of returned items. retries : int The number of retries on failure to fetch data. show_progress : bool Whether to show the progress bar. tqdm_kwargs : dict Keyword arguments passed to tqdm . params : dict Exchange-specific key-value parameters. For defaults, see data.ccxt in settings . update_symbol methodCCXTData . update_symbol ( symbol , ** kwargs ) Update the symbol. **kwargs will override keyword arguments passed to CCXTData.download_symbol() . GBMData classGBMData ( wrapper , data , tz_localize , tz_convert , missing_index , missing_columns , download_kwargs , ** kwargs ) SyntheticData for data generated using Geometric Brownian Motion (GBM). Usage See the example under BinanceData . import vectorbt as vbt gbm_data = vbt . GBMData . download ( 'GBM' , start = '2 hours ago' , end = 'now' , freq = '1min' , seed = 42 ) gbm_data . get () 2021-05-02 14:14:15.182089+00:00 102.386605 2021-05-02 14:15:15.182089+00:00 101.554203 2021-05-02 14:16:15.182089+00:00 104.765771 ... ... 2021-05-02 16:12:15.182089+00:00 51.614839 2021-05-02 16:13:15.182089+00:00 53.525376 2021-05-02 16:14:15.182089+00:00 55.615250 Freq: T, Length: 121, dtype: float64 import time time . sleep ( 60 ) gbm_data = gbm_data . update () gbm_data . get () 2021-05-02 14:14:15.182089+00:00 102.386605 2021-05-02 14:15:15.182089+00:00 101.554203 2021-05-02 14:16:15.182089+00:00 104.765771 ... ... 2021-05-02 16:13:15.182089+00:00 53.525376 2021-05-02 16:14:15.182089+00:00 51.082220 2021-05-02 16:15:15.182089+00:00 54.725304 Freq: T, Length: 122, dtype: float64 Superclasses AttrResolver Configured Data Documented IndexingBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin SyntheticData Wrapping Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Data.align_columns() Data.align_index() Data.concat() Data.download() Data.from_data() Data.get() Data.indexing_func() Data.plot() Data.select_symbol_kwargs() Data.update() PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() SyntheticData.config SyntheticData.data SyntheticData.download_kwargs SyntheticData.download_symbol() SyntheticData.iloc SyntheticData.indexing_kwargs SyntheticData.loc SyntheticData.missing_columns SyntheticData.missing_index SyntheticData.plots_defaults SyntheticData.self_aliases SyntheticData.stats_defaults SyntheticData.symbols SyntheticData.tz_convert SyntheticData.tz_localize SyntheticData.wrapper SyntheticData.writeable_attrs Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() generate_symbol class methodGBMData . generate_symbol ( symbol , index , S0 = 100.0 , mu = 0.0 , sigma = 0.05 , T = None , I = 1 , seed = None ) Generate the symbol using generate_gbm_paths() . Args symbol : str Symbol. index : pd.Index Pandas index. S0 : float Value at time 0. Does not appear as the first value in the output data. mu : float Drift, or mean of the percentage change. sigma : float Standard deviation of the percentage change. T : int Number of time steps. Defaults to the length of index . I : int Number of generated paths (columns in our case). seed : int Set seed to make the results deterministic. update_symbol methodGBMData . update_symbol ( symbol , ** kwargs ) Update the symbol. **kwargs will override keyword arguments passed to SyntheticData.download_symbol() . SyntheticData classSyntheticData ( wrapper , data , tz_localize , tz_convert , missing_index , missing_columns , download_kwargs , ** kwargs ) Data for synthetically generated data. Superclasses AttrResolver Configured Data Documented IndexingBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Data.align_columns() Data.align_index() Data.concat() Data.config Data.data Data.download() Data.download_kwargs Data.from_data() Data.get() Data.iloc Data.indexing_func() Data.indexing_kwargs Data.loc Data.missing_columns Data.missing_index Data.plot() Data.plots_defaults Data.select_symbol_kwargs() Data.self_aliases Data.stats_defaults Data.symbols Data.tz_convert Data.tz_localize Data.update() Data.wrapper Data.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() Subclasses GBMData download_symbol class methodSyntheticData . download_symbol ( symbol , start = 0 , end = 'now' , freq = None , date_range_kwargs = None , ** kwargs ) Download the symbol. Generates datetime index and passes it to SyntheticData.generate_symbol() to fill the Series/DataFrame with generated data. generate_symbol class methodSyntheticData . generate_symbol ( symbol , index , ** kwargs ) Abstract method to generate a symbol. update_symbol methodSyntheticData . update_symbol ( symbol , ** kwargs ) Update the symbol. **kwargs will override keyword arguments passed to SyntheticData.download_symbol() . YFData classYFData ( wrapper , data , tz_localize , tz_convert , missing_index , missing_columns , download_kwargs , ** kwargs ) Data for data coming from yfinance . Stocks are usually in the timezone "+0500" and cryptocurrencies in UTC. Warning Data coming from Yahoo is not the most stable data out there. Yahoo may manipulate data how they want, add noise, return missing data points (see volume in the example below), etc. It's only used in vectorbt for demonstration purposes. Usage Fetch the business day except the last 5 minutes of trading data, and then update with the missing 5 minutes: import vectorbt as vbt yf_data = vbt . YFData . download ( ... "TSLA" , ... start = '2021-04-12 09:30:00 -0400' , ... end = '2021-04-12 09:35:00 -0400' , ... interval = '1m' ... ) yf_data . get () Open High Low Close \ Datetime 2021-04-12 13:30:00+00:00 685.080017 685.679993 684.765015 685.679993 2021-04-12 13:31:00+00:00 684.625000 686.500000 684.010010 685.500000 2021-04-12 13:32:00+00:00 685.646790 686.820007 683.190002 686.455017 2021-04-12 13:33:00+00:00 686.455017 687.000000 685.000000 685.565002 2021-04-12 13:34:00+00:00 685.690002 686.400024 683.200012 683.715027 Volume Dividends Stock Splits Datetime 2021-04-12 13:30:00+00:00 0 0 0 2021-04-12 13:31:00+00:00 152276 0 0 2021-04-12 13:32:00+00:00 168363 0 0 2021-04-12 13:33:00+00:00 129607 0 0 2021-04-12 13:34:00+00:00 134620 0 0 yf_data = yf_data . update ( end = '2021-04-12 09:40:00 -0400' ) yf_data . get () Open High Low Close \ Datetime 2021-04-12 13:30:00+00:00 685.080017 685.679993 684.765015 685.679993 2021-04-12 13:31:00+00:00 684.625000 686.500000 684.010010 685.500000 2021-04-12 13:32:00+00:00 685.646790 686.820007 683.190002 686.455017 2021-04-12 13:33:00+00:00 686.455017 687.000000 685.000000 685.565002 2021-04-12 13:34:00+00:00 685.690002 686.400024 683.200012 683.715027 2021-04-12 13:35:00+00:00 683.604980 684.340027 682.760071 684.135010 2021-04-12 13:36:00+00:00 684.130005 686.640015 683.333984 686.563904 2021-04-12 13:37:00+00:00 686.530029 688.549988 686.000000 686.635010 2021-04-12 13:38:00+00:00 686.593201 689.500000 686.409973 688.179993 2021-04-12 13:39:00+00:00 688.500000 689.347595 687.710022 688.070007 Volume Dividends Stock Splits Datetime 2021-04-12 13:30:00+00:00 0 0 0 2021-04-12 13:31:00+00:00 152276 0 0 2021-04-12 13:32:00+00:00 168363 0 0 2021-04-12 13:33:00+00:00 129607 0 0 2021-04-12 13:34:00+00:00 0 0 0 2021-04-12 13:35:00+00:00 110500 0 0 2021-04-12 13:36:00+00:00 148384 0 0 2021-04-12 13:37:00+00:00 243851 0 0 2021-04-12 13:38:00+00:00 203569 0 0 2021-04-12 13:39:00+00:00 93308 0 0 Superclasses AttrResolver Configured Data Documented IndexingBase PandasIndexer Pickleable PlotsBuilderMixin StatsBuilderMixin Wrapping Inherited members AttrResolver.deep_getattr() AttrResolver.post_resolve_attr() AttrResolver.pre_resolve_attr() AttrResolver.resolve_attr() Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Data.align_columns() Data.align_index() Data.concat() Data.config Data.data Data.download() Data.download_kwargs Data.from_data() Data.get() Data.iloc Data.indexing_func() Data.indexing_kwargs Data.loc Data.missing_columns Data.missing_index Data.plot() Data.plots_defaults Data.select_symbol_kwargs() Data.self_aliases Data.stats_defaults Data.symbols Data.tz_convert Data.tz_localize Data.update() Data.wrapper Data.writeable_attrs PandasIndexer.xs() Pickleable.load() Pickleable.save() PlotsBuilderMixin.build_subplots_doc() PlotsBuilderMixin.override_subplots_doc() PlotsBuilderMixin.plots() StatsBuilderMixin.build_metrics_doc() StatsBuilderMixin.override_metrics_doc() StatsBuilderMixin.stats() Wrapping.regroup() Wrapping.resolve_self() Wrapping.select_one() Wrapping.select_one_from_obj() download_symbol class methodYFData . download_symbol ( symbol , period = 'max' , start = None , end = None , ** kwargs ) Download the symbol. Args symbol : str Symbol. period : str Period. start : any Start datetime. See to_tzaware_datetime() . end : any End datetime. See to_tzaware_datetime() . **kwargs Keyword arguments passed to yfinance.base.TickerBase.history . update_symbol methodYFData . update_symbol ( symbol , ** kwargs ) Update the symbol. **kwargs will override keyword arguments passed to YFData.download_symbol() .