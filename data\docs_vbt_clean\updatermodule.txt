updater moduleClass for scheduling data updates. DataUpdater classDataUpdater ( data , schedule_manager = None , ** kwargs ) Class for scheduling data updates. Usage Update in the foreground: import vectorbt as vbt

MyDataUpdater ( vbt . DataUpdater ): ... def __init__ ( self , * args , ** kwargs ): ... super () . __init__ ( * args , ** kwargs ) ... self . update_count = 0 ... ... def update ( self , count_limit = None ): ... prev_index_len = len ( self . data . wrapper . index ) ... super () . update () ... new_index_len = len ( self . data . wrapper . index ) ... print ( f "Data updated with { new_index_len - prev_index_len } data points" ) ... self . update_count += 1 ... if count_limit is not None and self . update_count >= count_limit : ... raise vbt . CancelledError data = vbt . GBMData . download ( 'SYMBOL' , start = '1 minute ago' , freq = '1s' ) my_updater = MyDataUpdater ( data ) my_updater . update_every ( count_limit = 10 ) Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points my_updater . data . get () 2021-05-02 16:53:51.755347+00:00 96.830482 2021-05-02 16:53:52.755347+00:00 94.481883 2021-05-02 16:53:53.755347+00:00 94.327835 2021-05-02 16:53:54.755347+00:00 90.178038 2021-05-02 16:53:55.755347+00:00 88.260168 ... 2021-05-02 16:54:57.755347+00:00 99.342590 2021-05-02 16:54:58.755347+00:00 94.872893 2021-05-02 16:54:59.755347+00:00 93.212823 2021-05-02 16:55:00.755347+00:00 95.199882 2021-05-02 16:55:01.755347+00:00 93.070532 Freq: S, Length: 71, dtype: float64 Update in the background: my_updater = MyDataUpdater ( my_updater . data ) my_updater . update_every ( in_background = True , count_limit = 10 ) Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points Data updated with 1 data points my_updater . data . get () 2021-05-02 16:53:51.755347+00:00 96.830482 2021-05-02 16:53:52.755347+00:00 94.481883 2021-05-02 16:53:53.755347+00:00 94.327835 2021-05-02 16:53:54.755347+00:00 90.178038 2021-05-02 16:53:55.755347+00:00 88.260168 ... 2021-05-02 16:55:07.755347+00:00 94.502885 2021-05-02 16:55:08.755347+00:00 94.823707 2021-05-02 16:55:09.755347+00:00 92.570025 2021-05-02 16:55:10.755347+00:00 84.239018 2021-05-02 16:55:11.755347+00:00 81.294486 Freq: S, Length: 81, dtype: float64 Superclasses Configured Documented Pickleable Inherited members Configured.config Configured.copy() Configured.dumps() Configured.loads() Configured.replace() Configured.to_doc() Configured.update_config() Configured.writeable_attrs Pickleable.load() Pickleable.save() data propertyData instance. See Data . schedule_manager propertySchedule manager instance. See ScheduleManager . update methodDataUpdater . update ( ** kwargs ) Method that updates data. Override to do pre- and postprocessing. To stop this method from running again, raise CancelledError . update_every methodDataUpdater . update_every ( * args , to = None , tags = None , in_background = False , start_kwargs = None , ** kwargs ) Schedule DataUpdater.update() . For *args , to and tags , see ScheduleManager.every() . If in_background is set to True, starts in the background as an asyncio task. The task can be stopped with ScheduleManager.stop() . **kwargs are passed to DataUpdater.update() .