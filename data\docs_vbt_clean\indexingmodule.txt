indexing moduleClasses for indexing. The main purpose of indexing classes is to provide pandas-like indexing to user-defined classes holding objects that have rows and/or columns. This is done by forwarding indexing commands to each structured object and constructing the new user-defined class using them. This way, one can manipulate complex classes with dozens of pandas objects using a single command. build_param_indexer functionbuild_param_indexer ( param_names , class_name = 'ParamIndexer' , module_name = None ) A factory to create a class with parameter indexing. Parameter indexer enables accessing a group of rows and columns by a parameter array (similar to loc ). This way, one can query index/columns by another Series called a parameter mapper, which is just a pd.Series that maps columns (its index) to params (its values). Parameter indexing is important, since querying by column/index labels alone is not always the best option. For example, pandas doesn't let you query by list at a specific index/column level. Args param_names : list of str Names of the parameters. class_name : str Name of the generated class. module_name : str Name of the module to which the class should be bound. Usage import pandas as pd from vectorbt.base.indexing import build_param_indexer , indexing_on_mapper MyParamIndexer = build_param_indexer ([ 'my_param' ])

C ( MyParamIndexer ): ... def __init__ ( self , df , param_mapper ): ... self . df = df ... self . _my_param_mapper = param_mapper ... super () . __init__ ([ param_mapper ]) ... ... def indexing_func ( self , pd_indexing_func ): ... return self . __class__ ( ... pd_indexing_func ( self . df ), ... indexing_on_mapper ( self . _my_param_mapper , self . df , pd_indexing_func ) ... ) df = pd . DataFrame ({ 'a' : [ 1 , 2 ], 'b' : [ 3 , 4 ]}) param_mapper = pd . Series ([ 'First' , 'Second' ], index = [ 'a' , 'b' ]) c = C ( df , param_mapper ) c . my_param_loc [ 'First' ] . df 0 1 1 2 Name: a, dtype: int64 c . my_param_loc [ 'Second' ] . df 0 3 1 4 Name: b, dtype: int64 c . my_param_loc [[ 'First' , 'First' , 'Second' , 'Second' ]] . df a b 0 1 1 3 3 1 2 2 4 4 indexing_on_mapper functionindexing_on_mapper ( mapper , ref_obj , pd_indexing_func ) Broadcast mapper Series to ref_obj and perform pandas indexing using pd_indexing_func . IndexingBase classIndexingBase () Class that supports indexing through IndexingBase.indexing_func() . Subclasses PandasIndexer vectorbt.indicators.basic.ParamIndexer vectorbt.indicators.basic.ParamIndexer vectorbt.indicators.basic.ParamIndexer vectorbt.indicators.basic.ParamIndexer vectorbt.indicators.basic.ParamIndexer vectorbt.indicators.basic.ParamIndexer vectorbt.indicators.basic.ParamIndexer vectorbt.indicators.basic.ParamIndexer vectorbt.labels.generators.ParamIndexer vectorbt.labels.generators.ParamIndexer vectorbt.labels.generators.ParamIndexer vectorbt.labels.generators.ParamIndexer vectorbt.labels.generators.ParamIndexer vectorbt.labels.generators.ParamIndexer vectorbt.labels.generators.ParamIndexer vectorbt.labels.generators.ParamIndexer vectorbt.labels.generators.ParamIndexer vectorbt.signals.generators.ParamIndexer vectorbt.signals.generators.ParamIndexer vectorbt.signals.generators.ParamIndexer vectorbt.signals.generators.ParamIndexer vectorbt.signals.generators.ParamIndexer vectorbt.signals.generators.ParamIndexer vectorbt.signals.generators.ParamIndexer vectorbt.signals.generators.ParamIndexer vectorbt.signals.generators.ParamIndexer vectorbt.signals.generators.ParamIndexer vectorbt.signals.generators.ParamIndexer indexing_func methodIndexingBase . indexing_func ( pd_indexing_func , ** kwargs ) Apply pd_indexing_func on all pandas objects in question and return a new instance of the class. Should be overridden. IndexingError classIndexingError ( * args , ** kwargs ) Exception raised when an indexing error has occurred. Superclasses builtins.BaseException builtins.Exception Loc classLoc ( indexing_func , ** kwargs ) Forwards pd.Series.loc / pd.DataFrame.loc operation to each Series/DataFrame and returns a new class instance. Superclasses LocBase Inherited members LocBase.indexing_func LocBase.indexing_kwargs LocBase classLocBase ( indexing_func , ** kwargs ) Class that implements location-based indexing. Subclasses Loc ParamLoc iLoc indexing_func propertyIndexing function. indexing_kwargs propertyKeyword arguments passed to LocBase.indexing_func . PandasIndexer classPandasIndexer ( ** kwargs ) Implements indexing using iloc , loc , xs and __getitem__ . Usage import pandas as pd from vectorbt.base.indexing import PandasIndexer

C ( PandasIndexer ): ... def __init__ ( self , df1 , df2 ): ... self . df1 = df1 ... self . df2 = df2 ... super () . __init__ () ... ... def indexing_func ( self , pd_indexing_func ): ... return self . __class__ ( ... pd_indexing_func ( self . df1 ), ... pd_indexing_func ( self . df2 ) ... ) df1 = pd . DataFrame ({ 'a' : [ 1 , 2 ], 'b' : [ 3 , 4 ]}) df2 = pd . DataFrame ({ 'a' : [ 5 , 6 ], 'b' : [ 7 , 8 ]}) c = C ( df1 , df2 ) c . iloc [:, 0 ] <__main__.C object at 0x1a1cacbbe0> c . iloc [:, 0 ] . df1 0 1 1 2 Name: a, dtype: int64 c . iloc [:, 0 ] . df2 0 5 1 6 Name: a, dtype: int64 Superclasses IndexingBase Inherited members IndexingBase.indexing_func() Subclasses ArrayWrapper Wrapping iloc propertyForwards pd.Series.iloc / pd.DataFrame.iloc operation to each Series/DataFrame and returns a new class instance. indexing_kwargs propertyIndexing keyword arguments. loc propertyForwards pd.Series.loc / pd.DataFrame.loc operation to each Series/DataFrame and returns a new class instance. xs methodPandasIndexer . xs ( * args , ** kwargs ) Forwards pd.Series.xs / pd.DataFrame.xs operation to each Series/DataFrame and returns a new class instance. ParamLoc classParamLoc ( mapper , indexing_func , level_name = None , ** kwargs ) Access a group of columns by parameter using pd.Series.loc . Uses mapper to establish link between columns and parameter values. Superclasses LocBase Inherited members LocBase.indexing_func LocBase.indexing_kwargs get_indices methodParamLoc . get_indices ( key ) Get array of indices affected by this key. level_name propertyLevel name. mapper propertyMapper. iLoc classiLoc ( indexing_func , ** kwargs ) Forwards pd.Series.iloc / pd.DataFrame.iloc operation to each Series/DataFrame and returns a new class instance. Superclasses LocBase Inherited members LocBase.indexing_func LocBase.indexing_kwargs